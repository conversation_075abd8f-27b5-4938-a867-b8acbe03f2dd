version: '3.8'

services:
  video-converter-api:
    build: .
    ports:
      - "8083:8083"
    environment:
      - NODE_ENV=production
      - HTTP_PORT=8083
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - UPLOAD_DIR=/usr/src/app/uploads
      - TEMP_DIR=/usr/src/app/temp
    volumes:
      - ./uploads:/usr/src/app/uploads
      - ./temp:/usr/src/app/temp
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
