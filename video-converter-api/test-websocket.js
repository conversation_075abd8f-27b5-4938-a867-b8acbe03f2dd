const { io } = require('socket.io-client');
const axios = require('axios');

const API_BASE_URL = 'http://localhost:8083';
const SOCKET_URL = 'http://localhost:8083';

async function testWebSocketConnection() {
  console.log('Testing WebSocket connection to video converter API...');
  
  return new Promise((resolve, reject) => {
    const socket = io(SOCKET_URL, {
      transports: ['websocket', 'polling'],
      timeout: 10000
    });

    socket.on('connect', () => {
      console.log('✅ WebSocket connected successfully!');
      console.log('Socket ID:', socket.id);
      
      // Test progress event listener
      socket.on('video_conversion_progress', (data) => {
        console.log('📊 Progress update received:', data);
      });

      socket.on('video_conversion_complete', (data) => {
        console.log('✅ Conversion complete:', data);
        socket.disconnect();
        resolve(true);
      });

      socket.on('video_conversion_error', (data) => {
        console.log('❌ Conversion error:', data);
        socket.disconnect();
        resolve(false);
      });

      // Test with a mock conversion request
      setTimeout(() => {
        console.log('🔌 WebSocket connection test completed');
        socket.disconnect();
        resolve(true);
      }, 2000);
    });

    socket.on('connect_error', (error) => {
      console.error('❌ WebSocket connection failed:', error.message);
      reject(error);
    });

    socket.on('disconnect', () => {
      console.log('🔌 WebSocket disconnected');
    });
  });
}

async function testHealthCheck() {
  try {
    console.log('Testing health check...');
    const response = await axios.get(`${API_BASE_URL}/video-converter/health`);
    console.log('✅ Health check passed:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting video converter API tests...\n');
  
  try {
    // Test 1: Health check
    const healthOk = await testHealthCheck();
    if (!healthOk) {
      console.log('❌ Health check failed, skipping WebSocket test');
      return;
    }
    
    console.log('');
    
    // Test 2: WebSocket connection
    const socketOk = await testWebSocketConnection();
    
    console.log('\n📋 Test Results:');
    console.log(`Health Check: ${healthOk ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`WebSocket: ${socketOk ? '✅ PASS' : '❌ FAIL'}`);
    
  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  testWebSocketConnection,
  testHealthCheck
};
