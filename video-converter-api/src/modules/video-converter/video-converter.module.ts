import { Module } from '@nestjs/common';
import { VideoConverterController } from './controllers/video-converter.controller';
import { VideoConverterService } from './services/video-converter.service';
import { SocketModule } from '../socket/socket.module';
import { QueueModule } from '../queue/queue.module';

@Module({
  imports: [SocketModule, QueueModule],
  controllers: [VideoConverterController],
  providers: [VideoConverterService],
  exports: [VideoConverterService]
})
export class VideoConverterModule {}
