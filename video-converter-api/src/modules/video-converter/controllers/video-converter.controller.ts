import {
  <PERSON>,
  Post,
  Body,
  Get,
  Param,
  HttpCode,
  HttpStatus,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Res,
  Query,
  Delete
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { existsSync, mkdirSync, unlinkSync, createReadStream } from 'fs';
import { VideoConverterService, IConvertProgress } from '../services/video-converter.service';
import { ConvertVideoDto, VideoMetadataDto, CreateThumbnailsDto } from '../dtos/convert-video.dto';
import { SocketGateway } from '../../socket/socket.gateway';
import { QueueEventService, QueueEvent } from '../../queue/queue-event.service';
import { StringHelper } from '../../../shared';
import config from '../../../config';

const VIDEO_QUEUE_CHANNEL = 'VIDEO_PROCESS';

@Controller('video-converter')
export class VideoConverterController {
  constructor(
    private readonly videoConverterService: VideoConverterService,
    private readonly socketGateway: SocketGateway,
    private readonly queueEventService: QueueEventService
  ) {
    // Subscribe to video processing queue
    this.queueEventService.subscribe(
      VIDEO_QUEUE_CHANNEL,
      'PROCESS_VIDEO',
      this.processVideoFromQueue.bind(this)
    );
  }

  @Get('health')
  @HttpCode(HttpStatus.OK)
  async healthCheck() {
    return {
      success: true,
      message: 'Video Converter API is running',
      timestamp: new Date().toISOString()
    };
  }

  @Post('convert')
  @HttpCode(HttpStatus.OK)
  async convertVideo(@Body() convertVideoDto: ConvertVideoDto) {
    try {
      const { filePath, toPath, size, socketId } = convertVideoDto;

      if (!existsSync(filePath)) {
        throw new BadRequestException('File not found');
      }

      // Set up progress callback for WebSocket updates
      const onProgress = (progress: IConvertProgress) => {
        if (socketId) {
          this.socketGateway.emitToClient(socketId, 'video_conversion_progress', {
            filePath,
            progress
          });
        }
      };

      const result = await this.videoConverterService.convert2Mp4(filePath, {
        toPath,
        size,
        onProgress
      });

      // Emit completion event
      // if (socketId) {
      //   this.socketGateway.emitToClient(socketId, 'video_conversion_complete', {
      //     filePath,
      //     result
      //   });
      // }

      return {
        success: true,
        data: result
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('convert/queue')
  @HttpCode(HttpStatus.OK)
  async queueVideoConversion(@Body() convertVideoDto: ConvertVideoDto) {
    try {
      if (!existsSync(convertVideoDto.filePath)) {
        throw new BadRequestException('File not found');
      }

      await this.queueEventService.publish(
        new QueueEvent({
          channel: VIDEO_QUEUE_CHANNEL,
          eventName: 'PROCESS_VIDEO',
          data: convertVideoDto
        })
      );

      return {
        success: true,
        message: 'Video conversion queued successfully'
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('upload')
  @UseInterceptors(
    FileInterceptor('video', {
      storage: diskStorage({
        destination: (_req, _file, cb) => {
          const uploadPath = config.app.uploadDir;
          if (!existsSync(uploadPath)) {
            mkdirSync(uploadPath, { recursive: true });
          }
          cb(null, uploadPath);
        },
        filename: (_req, file, cb) => {
          const randomName = StringHelper.randomString(10);
          const ext = extname(file.originalname);
          cb(null, `${randomName}${ext}`);
        }
      })
    })
  )
  async uploadVideo(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return {
      success: true,
      data: {
        filename: file.filename,
        originalname: file.originalname,
        path: file.path,
        size: file.size,
        mimetype: file.mimetype
      }
    };
  }

  @Post('metadata')
  @HttpCode(HttpStatus.OK)
  async getVideoMetadata(@Body() videoMetadataDto: VideoMetadataDto) {
    try {
      const { filePath } = videoMetadataDto;

      if (!existsSync(filePath)) {
        throw new BadRequestException('File not found');
      }

      const metadata = await this.videoConverterService.getMetaData(filePath);

      return {
        success: true,
        data: metadata
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('thumbnails')
  @HttpCode(HttpStatus.OK)
  async createThumbnails(@Body() createThumbnailsDto: CreateThumbnailsDto) {
    try {
      const { filePath, toFolder, count, size } = createThumbnailsDto;

      if (!existsSync(filePath)) {
        throw new BadRequestException('File not found');
      }

      if (!existsSync(toFolder)) {
        mkdirSync(toFolder, { recursive: true });
      }

      const thumbnails = await this.videoConverterService.createThumbs(filePath, {
        toFolder,
        count,
        size
      });

      return {
        success: true,
        data: thumbnails
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('check-html5/:filePath')
  async checkHtml5Support(@Param('filePath') filePath: string) {
    try {
      // Decode the file path
      const decodedPath = decodeURIComponent(filePath);

      if (!existsSync(decodedPath)) {
        throw new BadRequestException('File not found');
      }

      const isSupported = await this.videoConverterService.isSupportHtml5(decodedPath);

      return {
        success: true,
        data: {
          filePath: decodedPath,
          supportsHtml5: isSupported
        }
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('files/download')
  @HttpCode(HttpStatus.OK)
  async downloadFile(@Query('path') filePath: string, @Res() response: any) {
    try {
      if (!filePath) {
        throw new BadRequestException('File path is required');
      }

      if (!existsSync(filePath)) {
        throw new BadRequestException('File not found');
      }

      const fileName = filePath.split('/').pop() || 'download';

      response.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      response.setHeader('Content-Type', 'application/octet-stream');

      const fileStream = createReadStream(filePath);
      fileStream.pipe(response);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Delete('files/cleanup')
  @HttpCode(HttpStatus.OK)
  async cleanupFile(@Body() body: { path: string }) {
    try {
      const { path } = body;

      if (!path) {
        throw new BadRequestException('File path is required');
      }

      // if (existsSync(path)) {
      //   unlinkSync(path);
      // }

      return {
        success: true,
        message: 'File cleaned up successfully'
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  private async processVideoFromQueue(event: QueueEvent) {
    try {
      const { filePath, toPath, size, socketId } = event.data;

      // Set up progress callback for WebSocket updates
      const onProgress = (progress: IConvertProgress) => {
        if (socketId) {
          this.socketGateway.emitToClient(socketId, 'video_conversion_progress', {
            filePath,
            progress
          });
        }
      };

      const result = await this.videoConverterService.convert2Mp4(filePath, {
        toPath,
        size,
        onProgress
      });

      // Emit completion event
      if (socketId) {
        this.socketGateway.emitToClient(socketId, 'video_conversion_complete', {
          filePath,
          result
        });
      }

      console.log('Video conversion completed:', result);
    } catch (error) {
      console.error('Video conversion failed:', error);
      
      if (event.data.socketId) {
        this.socketGateway.emitToClient(event.data.socketId, 'video_conversion_error', {
          filePath: event.data.filePath,
          error: error.message
        });
      }
    }
  }
}
