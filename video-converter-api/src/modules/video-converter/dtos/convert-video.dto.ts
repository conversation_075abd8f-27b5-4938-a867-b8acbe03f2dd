import { IsOptional, IsString } from 'class-validator';

export class ConvertVideoDto {
  @IsString()
  filePath: string;

  @IsOptional()
  @IsString()
  toPath?: string;

  @IsOptional()
  @IsString()
  size?: string;

  @IsOptional()
  @IsString()
  socketId?: string; // For progress updates via WebSocket
}

export class VideoMetadataDto {
  @IsString()
  filePath: string;
}

export class CreateThumbnailsDto {
  @IsString()
  filePath: string;

  @IsString()
  toFolder: string;

  @IsOptional()
  count?: number;

  @IsOptional()
  @IsString()
  size?: string;
}
