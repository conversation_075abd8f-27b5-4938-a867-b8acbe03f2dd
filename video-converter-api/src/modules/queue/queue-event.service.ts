import { Injectable } from '@nestjs/common';
import { QueueService } from './queue.service';

export class QueueEvent {
  channel: string;
  eventName: string;
  data: any;

  constructor(data: { channel: string; eventName: string; data: any }) {
    this.channel = data.channel;
    this.eventName = data.eventName;
    this.data = data.data;
  }
}

@Injectable()
export class QueueEventService {
  private queues = new Map();

  constructor(private readonly queueService: QueueService) {}

  public async publish(event: QueueEvent) {
    const queueName = `${event.channel}_${event.eventName}`;
    let queue = this.queues.get(queueName);
    
    if (!queue) {
      queue = this.queueService.createInstance(queueName);
      this.queues.set(queueName, queue);
    }

    return queue.createJob(event.data).save();
  }

  public subscribe(channel: string, eventName: string, handler: (event: QueueEvent) => void) {
    const queueName = `${channel}_${eventName}`;
    let queue = this.queues.get(queueName);
    
    if (!queue) {
      queue = this.queueService.createInstance(queueName);
      this.queues.set(queueName, queue);
    }

    queue.process((job, done) => {
      try {
        const event = new QueueEvent({
          channel,
          eventName,
          data: job.data
        });
        handler(event);
        done();
      } catch (error) {
        done(error);
      }
    });
  }
}
