import { DynamicModule, Global, Module } from '@nestjs/common';
import { QueueService } from './queue.service';
import { QueueEventService } from './queue-event.service';
import config from '../../config';

@Global()
@Module({})
export class QueueModule {
  static forRoot(opts?: any): DynamicModule {
    const queueServiceProvider = {
      provide: QueueService,
      useFactory: async () => {
        const queueConfig = {
          prefix: `${config.redis.prefix}_bq`,
          stallInterval: 5000,
          nearTermWindow: 1200000,
          delayedDebounce: 1000,
          redis: {
            host: config.redis.host,
            port: config.redis.port,
            db: config.redis.db,
            password: config.redis.password,
            options: {}
          },
          isWorker: true,
          getEvents: true,
          sendEvents: true,
          storeJobs: false,
          ensureScripts: true,
          activateDelayedJobs: false,
          removeOnSuccess: true,
          removeOnFailure: true,
          redisScanCount: 100,
          ...opts || {}
        };
        return new QueueService(queueConfig);
      },
      inject: []
    };

    const queueEventServiceProvider = {
      provide: QueueEventService,
      useFactory: async (queueService: QueueService) => {
        return new QueueEventService(queueService);
      },
      inject: [QueueService]
    };

    return {
      module: QueueModule,
      providers: [queueServiceProvider, queueEventServiceProvider],
      exports: [queueServiceProvider, queueEventServiceProvider]
    };
  }
}
