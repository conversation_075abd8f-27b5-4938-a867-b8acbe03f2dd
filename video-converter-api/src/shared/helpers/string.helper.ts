export const getExt = (path: string) => {
  const i = path.lastIndexOf('.');
  return i < 0 ? '' : path.substr(i);
};

/**
 * get file name from the path
 * @type {String}
 */
export const getFileName = (fullPath: string, removeExtension: boolean) => {
  /* eslint-disable */
  const name = fullPath.replace(/^.*[\\\/]/, '');

  return removeExtension ? name.replace(/\.[^/.]+$/, '') : name;
  /* eslint-enable */
};

export const getFilePath = (fullPath) => fullPath.substring(0, fullPath.lastIndexOf('/'));

export const randomString = (len: number, charSetInput?: string) => {
  const charSet = charSetInput
    || 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let randomStr = '';
  for (let i = 0; i < len; i += 1) {
    const randomPoz = Math.floor(Math.random() * charSet.length);
    randomStr += charSet.substring(randomPoz, randomPoz + 1);
  }
  return randomStr;
};

/**
 * replace special char by another string and allow some chars for alias
 */
export const createAlias = (str: string) => {
  /* eslint-disable */
  if (!str || typeof str !== 'string') {
    return '';
  }
  return str.toLowerCase().replace(/[&\/\\#,+()$~%.'":*?<>{}\s]/g, '-');
};

export class StringHelper {
  static getExt = getExt;
  static getFileName = getFileName;
  static getFilePath = getFilePath;
  static randomString = randomString;
  static createAlias = createAlias;
}
