import { Module } from '@nestjs/common';
import { VideoConverterModule } from './modules/video-converter/video-converter.module';
import { SocketModule } from './modules/socket/socket.module';
import { QueueModule } from './modules/queue/queue.module';

@Module({
  imports: [
    QueueModule.forRoot(),
    SocketModule,
    VideoConverterModule
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
