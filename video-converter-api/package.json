{"name": "video-converter-api", "version": "1.0.0", "description": "Standalone video converter API server", "author": "", "private": true, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "dev": "yarn start:dev", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint '{src,apps,libs,test}/**/*.ts' --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.3.3", "@nestjs/core": "^10.3.3", "@nestjs/platform-express": "^10.3.3", "@nestjs/platform-socket.io": "^10.3.3", "@nestjs/swagger": "^7.3.0", "@nestjs/websockets": "^10.3.3", "@socket.io/redis-adapter": "^8.2.1", "bee-queue": "^1.7.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dotenv": "^16.4.4", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.2", "ioredis": "^5.3.2", "lodash": "^4.17.21", "mkdirp": "^3.0.1", "multer": "^1.4.5-lts.1", "nestjs-config": "^1.4.11", "reflect-metadata": "^0.2.1", "rimraf": "^5.0.5", "rxjs": "^7.8.1", "socket.io": "^4.7.4", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.1.1", "@nestjs/testing": "^10.3.3", "@types/express": "^4.17.21", "@types/jest": "29.5.12", "@types/multer": "^1.4.13", "@types/node": "^20.11.19", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "axios": "^1.9.0", "eslint": "^8.56.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "jest": "^29.7.0", "prettier": "^3.2.5", "socket.io-client": "^4.8.1", "supertest": "^6.3.4", "ts-jest": "29.1.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleDirectories": ["node_modules", "src"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}}