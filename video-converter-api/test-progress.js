const { io } = require('socket.io-client');
const axios = require('axios');

const API_BASE_URL = 'http://localhost:8083';
const SOCKET_URL = 'http://localhost:8083';

async function testVideoConversionWithProgress() {
  console.log('🎬 Testing video conversion with WebSocket progress updates...\n');
  
  return new Promise((resolve, reject) => {
    const socketId = 'test-socket-' + Date.now();
    let progressUpdates = [];
    let conversionComplete = false;
    
    // Create WebSocket connection
    const socket = io(SOCKET_URL, {
      transports: ['websocket', 'polling'],
      timeout: 10000
    });

    socket.on('connect', async () => {
      console.log(`✅ WebSocket connected with ID: ${socket.id}`);
      
      // Listen for progress updates
      socket.on('video_conversion_progress', (data) => {
        console.log('📊 Progress update:', {
          filePath: data.filePath,
          percent: data.progress.percent,
          currentTime: data.progress.currentTime,
          targetTime: data.progress.targetTime,
          currentFps: data.progress.currentFps,
          currentKbps: data.progress.currentKbps
        });
        progressUpdates.push(data.progress);
      });

      socket.on('video_conversion_complete', (data) => {
        console.log('✅ Conversion completed:', data);
        conversionComplete = true;
        socket.disconnect();
        
        console.log(`\n📈 Progress Summary:`);
        console.log(`Total progress updates received: ${progressUpdates.length}`);
        console.log(`Final progress: ${progressUpdates[progressUpdates.length - 1]?.percent || 0}%`);
        
        resolve({
          success: true,
          progressUpdates: progressUpdates.length,
          completed: conversionComplete
        });
      });

      socket.on('video_conversion_error', (data) => {
        console.error('❌ Conversion error:', data);
        socket.disconnect();
        resolve({
          success: false,
          error: data.error,
          progressUpdates: progressUpdates.length
        });
      });

      // Simulate a conversion request with the socket ID
      try {
        console.log('🚀 Starting mock conversion request...');
        
        // This would normally be a real video file path
        // For testing, we'll use a mock request that should trigger the progress callback
        const mockConversionRequest = {
          filePath: '/mock/test-video.mp4',
          toPath: '/mock/output/test-video-converted.mp4',
          size: '1280x720',
          socketId: socket.id // Use the actual socket ID
        };

        console.log('📤 Sending conversion request with socketId:', socket.id);
        
        // Note: This will fail because the file doesn't exist, but it should still
        // demonstrate the WebSocket connection and error handling
        const response = await axios.post(
          `${API_BASE_URL}/video-converter/convert`,
          mockConversionRequest,
          { timeout: 30000 }
        );
        
        console.log('✅ Conversion request accepted:', response.data);
        
      } catch (error) {
        console.log('⚠️  Expected error (mock file doesn\'t exist):', error.response?.data?.message || error.message);
        
        // Even though the conversion failed, we should have established the WebSocket connection
        setTimeout(() => {
          socket.disconnect();
          resolve({
            success: true, // Success in terms of WebSocket connection
            progressUpdates: progressUpdates.length,
            completed: false,
            note: 'WebSocket connection established successfully, conversion failed due to mock file'
          });
        }, 2000);
      }
    });

    socket.on('connect_error', (error) => {
      console.error('❌ WebSocket connection failed:', error.message);
      reject(error);
    });

    socket.on('disconnect', () => {
      console.log('🔌 WebSocket disconnected');
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      if (!conversionComplete) {
        socket.disconnect();
        resolve({
          success: false,
          error: 'Test timeout',
          progressUpdates: progressUpdates.length
        });
      }
    }, 30000);
  });
}

async function runProgressTest() {
  console.log('🧪 Starting WebSocket Progress Test\n');
  
  try {
    // First verify the API is running
    const healthResponse = await axios.get(`${API_BASE_URL}/video-converter/health`);
    console.log('✅ API Health Check:', healthResponse.data.message);
    console.log('');
    
    // Run the progress test
    const result = await testVideoConversionWithProgress();
    
    console.log('\n📋 Test Results:');
    console.log(`Success: ${result.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Progress Updates: ${result.progressUpdates}`);
    console.log(`Completed: ${result.completed ? '✅ YES' : '❌ NO'}`);
    
    if (result.error) {
      console.log(`Error: ${result.error}`);
    }
    
    if (result.note) {
      console.log(`Note: ${result.note}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
if (require.main === module) {
  runProgressTest();
}

module.exports = {
  testVideoConversionWithProgress
};
