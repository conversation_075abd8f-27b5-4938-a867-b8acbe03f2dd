FROM node:18-alpine

# Install FFmpeg
RUN apk add --no-cache ffmpeg

# Create app directory
WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./
COPY yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Build the application
RUN yarn build

# Create uploads and temp directories
RUN mkdir -p uploads temp

# Expose port
EXPOSE 8083

# Start the application
CMD ["yarn", "start:prod"]
