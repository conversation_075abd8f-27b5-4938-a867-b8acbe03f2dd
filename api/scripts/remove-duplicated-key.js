const { DB, COLLECTION } = require('../migrations/lib');

module.exports = async () => {
  const [userReq, performerReq] = await Promise.all([
    DB.collection(COLLECTION.SETTING)
      .find({ key: 'requireEmailVerificationUser' })
      .toArray(),
    DB.collection(COLLECTION.SETTING)
      .find({ key: 'requireEmailVerificationPerformer' })
      .toArray()
  ]);
  userReq.shift();
  performerReq.shift();
  const settingIds = [...userReq, ...performerReq].map(
    (setting) => setting._id
  );
  await DB.collection(COLLECTION.SETTING).deleteMany({
    _id: { $in: settingIds }
  });
};
