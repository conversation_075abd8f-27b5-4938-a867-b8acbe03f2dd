/* eslint-disable no-await-in-loop */
const { DB } = require('../migrations/lib');

const generateModelsCode = async () => {
  const limit = 10;
  let offset = 0;
  let hasRecord = true;

  do {
    const items = await DB.collection('performers').find({
      referralCode: null
    })
      .sort({
        _id: -1
      })
      .skip(offset)
      .limit(limit);

    offset += limit;
    if (!items.length) {
      hasRecord = false;
      break;
    }

    await items.reduce(async (ap, item) => {
      await ap;
      const referralCode = Math.random().toString(32).slice(3);
      await DB.collection('performers').updateOne({ _id: item._id }, {
        $set: {
          referralCode
        }
      });
      return Promise.resolve();
    }, Promise.resolve());
  } while (hasRecord);
};

const generateUsersCode = async () => {
  const limit = 10;
  let offset = 0;
  let hasRecord = true;

  do {
    const items = await DB.collection('users').find({
      referralCode: null
    })
      .sort({
        _id: -1
      })
      .skip(offset)
      .limit(limit);

    offset += limit;
    if (!items.length) {
      hasRecord = false;
      break;
    }

    await items.reduce(async (ap, item) => {
      await ap;
      const referralCode = Math.random().toString(32).slice(3);
      await DB.collection('users').updateOne({ _id: item._id }, {
        $set: {
          referralCode
        }
      });
      return Promise.resolve();
    }, Promise.resolve());
  } while (hasRecord);
};

module.exports = async () => {
  await generateUsersCode();
  await generateModelsCode();
};
