const { DB, COLLECTION } = require('../migrations/lib');

function convert(inputString) {
  const replacementMap = {
    MODEL: 'CREATOR',
    Model: 'Creator',
    model: 'creator',
    models: 'creators',
    Models: 'Creators',
    MODELS: 'MODELS'
  };

  Object.keys(replacementMap).forEach((word) => {
    const regex = new RegExp(`\\b${word}\\b`, 'g');
    // eslint-disable-next-line no-param-reassign
    inputString = inputString.replace(regex, replacementMap[word]);
  });

  return inputString;
}
module.exports = async () => {
  const settings = await DB.collection(COLLECTION.SETTING)
    .find({
      $or: [
        { name: { $regex: /model/i } },
        {
          description: { $regex: /model/i }
        }
      ]
    })
    .toArray();
  const bulkOperations = settings.map((setting) => ({
    updateOne: {
      filter: {
        _id: setting._id
      },
      update: {
        $set: {
          name: convert(setting.name),
          description: convert(setting.description)
        }
      }
    }
  }));
  await DB.collection(COLLECTION.SETTING).bulkWrite(bulkOperations);
};
