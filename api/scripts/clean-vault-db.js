const {
  DB
} = require('../migrations/lib');

module.exports = async () => {
  const vaults = await DB.collection('media_items').find({}).toArray();
  await vaults.reduce(async (cb, vault) => {
    await cb;
    const file = await DB.collection('files').findOne({ _id: vault.fileId });
    if (!file) await DB.collection('media_items').deleteOne({ _id: vault._id });
    return Promise.resolve();
  }, Promise.resolve());
};
