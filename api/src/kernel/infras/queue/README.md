# Queue Event Service - Multiple Instances Implementation

## Overview

The QueueEventService has been enhanced to support multiple instances per topic with a maximum limit of 10 instances. This allows for better scalability and load distribution when processing queue events.

## Key Features

### 1. Multiple Instances Support
- Each topic can now have up to 10 queue instances
- Each subscription creates a new instance (up to the limit)
- Instances are named with a suffix: `{channel}_{topic}_{instanceIndex}`

### 2. Round-Robin Load Distribution
- Published events are distributed across instances using round-robin algorithm
- Ensures even load distribution across all available instances
- Automatic failover if an instance becomes unavailable

### 3. Maximum Instance Limit
- Hard limit of 10 instances per topic to prevent resource exhaustion
- Configurable via the `MAX_INSTANCES` constant
- Graceful handling when limit is reached

### 4. Statistics and Monitoring
- `getQueueStats()` method provides detailed statistics
- Shows instance count, current index, and maximum instances per topic
- Useful for monitoring and debugging

## Architecture Changes

### Before (Single Instance)
```
{
  channel: {
    topic: BeeQueue instance
  }
}
```

### After (Multiple Instances)
```
{
  channel: {
    topic: {
      instances: [BeeQueue instance, BeeQueue instance, ...],
      currentIndex: number
    }
  }
}
```

## Usage Examples

### Basic Subscription (Creates New Instance)
```typescript
// Each call creates a new instance (up to 10)
await queueEventService.subscribe('VIDEO_PROCESSING', 'CONVERT', handler1);
await queueEventService.subscribe('VIDEO_PROCESSING', 'CONVERT', handler2);
await queueEventService.subscribe('VIDEO_PROCESSING', 'CONVERT', handler3);
```

### Publishing Events (Round-Robin Distribution)
```typescript
// Events will be distributed across all instances
await queueEventService.publish(new QueueEvent({
  channel: 'VIDEO_PROCESSING',
  eventName: 'CONVERT',
  data: { videoId: 'video_123' }
}));
```

### Monitoring Statistics
```typescript
const stats = queueEventService.getQueueStats();
console.log(stats);
// Output:
// {
//   "VIDEO_PROCESSING": {
//     "CONVERT": {
//       "instanceCount": 3,
//       "currentIndex": 1,
//       "maxInstances": 10
//     }
//   }
// }
```

## Benefits

### 1. Improved Scalability
- Multiple workers can process the same type of job simultaneously
- Better resource utilization across multiple CPU cores
- Reduced bottlenecks in high-throughput scenarios

### 2. Better Fault Tolerance
- If one instance fails, others continue processing
- Graceful degradation under load
- No single point of failure per topic

### 3. Load Distribution
- Even distribution of work across instances
- Prevents any single instance from being overwhelmed
- Better overall system performance

### 4. Resource Management
- Maximum instance limit prevents resource exhaustion
- Predictable memory and connection usage
- Better system stability

## Configuration

### Environment Variables
The queue service uses standard Redis configuration:
- `REDIS_HOST`: Redis server host (default: 127.0.0.1)
- `REDIS_PORT`: Redis server port (default: 6379)
- `REDIS_DB`: Redis database number (default: 0)
- `REDIS_PASSWORD`: Redis password (optional)
- `REDIS_PREFIX`: Redis key prefix (default: 'bq')

### Maximum Instances
The maximum number of instances per topic is set to 10 and can be modified by changing the `MAX_INSTANCES` constant in the `QueueEventService` class.

## Error Handling

### Maximum Instances Reached
When attempting to create more than 10 instances:
- Warning is logged to console
- `subscribe()` method returns early without creating instance
- Error is logged indicating maximum instances reached

### Job Processing Errors
- Errors in job handlers are caught and logged
- Failed jobs are re-thrown to maintain bee-queue error handling
- Instance continues processing other jobs

### Publishing Errors
- Individual job creation failures are caught
- Error is logged with channel information
- Promise is rejected to notify caller

## Testing

Comprehensive test suite covers:
- Multiple instance creation
- Maximum instance limit enforcement
- Round-robin distribution
- Error handling scenarios
- Statistics accuracy

Run tests with:
```bash
npm test -- queue-event.service.spec.ts
```

## Migration Notes

### Backward Compatibility
- Existing code continues to work without changes
- Old single-instance behavior is maintained for existing subscriptions
- No breaking changes to the public API

### Performance Considerations
- Each instance creates a separate Redis connection
- Memory usage increases with number of instances
- Monitor Redis connection limits in production

## Best Practices

### 1. Instance Planning
- Consider your workload when deciding number of instances
- Start with fewer instances and scale up as needed
- Monitor performance and adjust accordingly

### 2. Error Handling
- Implement proper error handling in job handlers
- Use try-catch blocks for critical operations
- Log errors appropriately for debugging

### 3. Monitoring
- Regularly check queue statistics
- Monitor Redis memory usage
- Set up alerts for failed jobs

### 4. Resource Management
- Don't create unnecessary instances
- Clean up resources when shutting down
- Monitor system resource usage

## Future Enhancements

Potential improvements for future versions:
- Dynamic instance scaling based on load
- Instance health monitoring
- Custom load balancing strategies
- Instance-specific configuration options
- Metrics and monitoring integration
