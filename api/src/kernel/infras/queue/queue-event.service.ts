import { QueueEvent } from 'src/kernel/events';
import { QueueEventServiceAbstract } from './queue-event-abstract.service';
import { QueueService } from './queue.service';

// queue service base on redis / bee-queue
// support for subscribers in multiple instances, but just one subscribe will receive message
// by using bee-queue
// simplify by using redis among system instead of rabbitMQ for normal cases
// idea is build a queue message like Kafka but use redis
export class QueueEventService extends QueueEventServiceAbstract {
  /**
  * channel with topic and multiple instances
  * {
  *    channel: {
  *       topic: {
  *         instances: [BeeQueue instance],
  *         currentIndex: number
  *       }
  *    }
  * }
  */
  private queueInstances = {} as any;

  // Maximum number of instances per topic
  private readonly MAX_INSTANCES = 10;

  constructor(private queueService: QueueService) {
    super();
  }

  // Create a new queue instance for a topic
  private createQueueInstance(channel: string, topic: string): any {
    if (!this.queueInstances[channel]) {
      this.queueInstances[channel] = {};
    }
    if (!this.queueInstances[channel][topic]) {
      this.queueInstances[channel][topic] = {
        instances: [],
        currentIndex: 0
      };
    }

    const topicData = this.queueInstances[channel][topic];

    // Check if we've reached the maximum number of instances
    if (topicData.instances.length >= this.MAX_INSTANCES) {
      // eslint-disable-next-line no-console
      console.warn(`Maximum instances (${this.MAX_INSTANCES}) reached for topic ${topic} in channel ${channel}`);
      return null;
    }

    const instanceIndex = topicData.instances.length;
    const queueName = `${channel}_${topic}_${instanceIndex}`;
    const queueInstance = this.queueService.createInstance(queueName);

    topicData.instances.push(queueInstance);

    // eslint-disable-next-line no-console
    console.log(`Created queue instance ${instanceIndex + 1}/${this.MAX_INSTANCES} for topic ${topic} in channel ${channel}`);

    return queueInstance;
  }

  public subscribe(
    channel: string,
    topic: string,
    handler: Function
  ): Promise<void> {
    // Create a new queue instance for this subscription
    const queue = this.createQueueInstance(channel, topic);

    if (!queue) {
      // eslint-disable-next-line no-console
      console.error(`Failed to create queue instance for topic ${topic} in channel ${channel}. Maximum instances reached.`);
      return;
    }

    queue.process(async (job: any) => {
      try {
        // TODO - define for other config like retry
        // or add log, etc...
        await handler(job.data);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error(`Error processing job in topic ${topic}, channel ${channel}:`, error);
        throw error;
      }
    });

    // eslint-disable-next-line no-console
    console.log(`Subscribed to topic ${topic} in channel ${channel}. Total instances: ${this.queueInstances[channel][topic].instances.length}`);
  }

  async publish(event: QueueEvent): Promise<void> {
    if (!this.queueInstances[event.channel]) {
      // eslint-disable-next-line no-console
      console.warn(`No subscriber for channel ${event.channel}`);
      return;
    }

    const publishPromises: Promise<any>[] = [];

    Object.keys(this.queueInstances[event.channel]).forEach((topic: string) => {
      const topicData = this.queueInstances[event.channel][topic];

      if (topicData && topicData.instances && topicData.instances.length > 0) {
        // Get the next instance in round-robin fashion
        const instance = topicData.instances[topicData.currentIndex];
        topicData.currentIndex = (topicData.currentIndex + 1) % topicData.instances.length;

        // Create job on the selected instance
        const jobPromise = instance.createJob(event).save();
        publishPromises.push(jobPromise);

        // eslint-disable-next-line no-console
        console.log(`Published event to topic ${topic} in channel ${event.channel}, instance ${topicData.currentIndex}`);
      }
    });

    try {
      await Promise.all(publishPromises);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Error publishing event to channel ${event.channel}:`, error);
      throw error;
    }
  }

  // Get statistics about queue instances
  public getQueueStats(): Record<string, any> {
    const stats: Record<string, any> = {};

    Object.keys(this.queueInstances).forEach((channel) => {
      stats[channel] = {};
      Object.keys(this.queueInstances[channel]).forEach((topic) => {
        const topicData = this.queueInstances[channel][topic];
        stats[channel][topic] = {
          instanceCount: topicData.instances ? topicData.instances.length : 0,
          currentIndex: topicData.currentIndex || 0,
          maxInstances: this.MAX_INSTANCES
        };
      });
    });

    return stats;
  }
}
