/**
 * Example usage of the QueueEventService with multiple instances
 * 
 * This example demonstrates how to:
 * 1. Subscribe multiple handlers to the same topic (up to 10 instances)
 * 2. Publish events that will be distributed across instances
 * 3. Monitor queue statistics
 */

import { QueueEventService } from './queue-event.service';
import { QueueService } from './queue.service';
import { QueueEvent } from 'src/kernel/events';

export class QueueUsageExample {
  private queueEventService: QueueEventService;

  constructor() {
    // Initialize the queue service with Redis configuration
    const queueService = new QueueService({
      prefix: 'example_bq',
      stallInterval: 5000,
      nearTermWindow: 1200000,
      delayedDebounce: 1000,
      redis: {
        host: process.env.REDIS_HOST || '127.0.0.1',
        port: parseInt(process.env.REDIS_PORT, 10) || 6379,
        db: parseInt(process.env.REDIS_DB, 10) || 0,
        password: process.env.REDIS_PASSWORD || undefined,
        options: {}
      },
      isWorker: true,
      getEvents: true,
      sendEvents: true,
      storeJobs: false,
      ensureScripts: true,
      activateDelayedJobs: false,
      removeOnSuccess: true,
      removeOnFailure: true,
      redisScanCount: 100
    });

    this.queueEventService = new QueueEventService(queueService);
  }

  /**
   * Example: Setting up multiple video processing workers
   */
  async setupVideoProcessingWorkers() {
    console.log('Setting up video processing workers...');

    // Create multiple instances for video processing
    // Each instance can handle video processing independently
    for (let i = 0; i < 5; i++) {
      await this.queueEventService.subscribe(
        'VIDEO_PROCESSING',
        'CONVERT_VIDEO',
        async (data: any) => {
          console.log(`Worker ${i + 1} processing video:`, data.videoId);
          // Simulate video processing work
          await this.simulateWork(2000 + Math.random() * 3000);
          console.log(`Worker ${i + 1} completed video:`, data.videoId);
        }
      );
    }

    console.log('Video processing workers setup complete!');
  }

  /**
   * Example: Setting up multiple notification handlers
   */
  async setupNotificationHandlers() {
    console.log('Setting up notification handlers...');

    // Create multiple instances for handling notifications
    // This allows for better throughput when sending notifications
    for (let i = 0; i < 3; i++) {
      await this.queueEventService.subscribe(
        'NOTIFICATIONS',
        'SEND_EMAIL',
        async (data: any) => {
          console.log(`Email handler ${i + 1} sending to:`, data.email);
          // Simulate email sending
          await this.simulateWork(1000 + Math.random() * 2000);
          console.log(`Email handler ${i + 1} sent to:`, data.email);
        }
      );
    }

    console.log('Notification handlers setup complete!');
  }

  /**
   * Example: Publishing video processing jobs
   */
  async publishVideoJobs() {
    console.log('Publishing video processing jobs...');

    for (let i = 1; i <= 10; i++) {
      await this.queueEventService.publish(
        new QueueEvent({
          channel: 'VIDEO_PROCESSING',
          eventName: 'CONVERT_VIDEO',
          data: {
            videoId: `video_${i}`,
            inputPath: `/uploads/video_${i}.mp4`,
            outputPath: `/processed/video_${i}_converted.mp4`,
            quality: 'high'
          }
        })
      );
    }

    console.log('Video processing jobs published!');
  }

  /**
   * Example: Publishing notification jobs
   */
  async publishNotificationJobs() {
    console.log('Publishing notification jobs...');

    const emails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    for (const email of emails) {
      await this.queueEventService.publish(
        new QueueEvent({
          channel: 'NOTIFICATIONS',
          eventName: 'SEND_EMAIL',
          data: {
            email,
            subject: 'Welcome to our platform!',
            template: 'welcome',
            data: { username: email.split('@')[0] }
          }
        })
      );
    }

    console.log('Notification jobs published!');
  }

  /**
   * Example: Monitoring queue statistics
   */
  monitorQueueStats() {
    console.log('Queue Statistics:');
    const stats = this.queueEventService.getQueueStats();
    console.log(JSON.stringify(stats, null, 2));

    // Log summary
    Object.keys(stats).forEach(channel => {
      console.log(`\nChannel: ${channel}`);
      Object.keys(stats[channel]).forEach(topic => {
        const topicStats = stats[channel][topic];
        console.log(`  Topic: ${topic}`);
        console.log(`    Instances: ${topicStats.instanceCount}/${topicStats.maxInstances}`);
        console.log(`    Current Index: ${topicStats.currentIndex}`);
      });
    });
  }

  /**
   * Example: Demonstrating maximum instance limit
   */
  async demonstrateMaxInstanceLimit() {
    console.log('Demonstrating maximum instance limit...');

    // Try to create 12 instances (should only create 10)
    for (let i = 0; i < 12; i++) {
      await this.queueEventService.subscribe(
        'TEST_CHANNEL',
        'TEST_TOPIC',
        async (data: any) => {
          console.log(`Handler ${i + 1} processing:`, data);
        }
      );
    }

    // Check stats to see only 10 instances were created
    const stats = this.queueEventService.getQueueStats();
    console.log('Max instance test stats:', stats.TEST_CHANNEL?.TEST_TOPIC);
  }

  /**
   * Run the complete example
   */
  async runExample() {
    try {
      // Setup workers
      await this.setupVideoProcessingWorkers();
      await this.setupNotificationHandlers();

      // Demonstrate max instance limit
      await this.demonstrateMaxInstanceLimit();

      // Show initial stats
      console.log('\n=== Initial Queue Statistics ===');
      this.monitorQueueStats();

      // Publish jobs
      await this.publishVideoJobs();
      await this.publishNotificationJobs();

      // Show final stats
      console.log('\n=== Final Queue Statistics ===');
      this.monitorQueueStats();

      console.log('\nExample completed successfully!');
    } catch (error) {
      console.error('Example failed:', error);
    }
  }

  /**
   * Simulate work with a delay
   */
  private simulateWork(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Usage:
// const example = new QueueUsageExample();
// example.runExample();
