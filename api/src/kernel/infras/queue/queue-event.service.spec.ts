import { QueueEventService } from './queue-event.service';
import { QueueService } from './queue.service';
import { QueueEvent } from 'src/kernel/events';

// Mock bee-queue
const mockQueue = {
  process: jest.fn(),
  createJob: jest.fn().mockReturnValue({
    save: jest.fn().mockResolvedValue(true)
  })
};

const mockQueueService = {
  createInstance: jest.fn().mockReturnValue(mockQueue)
};

describe('QueueEventService', () => {
  let service: QueueEventService;
  let queueService: QueueService;

  beforeEach(() => {
    jest.clearAllMocks();
    queueService = mockQueueService as any;
    service = new QueueEventService(queueService);
  });

  describe('subscribe', () => {
    it('should create a new queue instance for subscription', () => {
      const handler = jest.fn();
      
      service.subscribe('TEST_CHANNEL', 'TEST_TOPIC', handler);
      
      expect(queueService.createInstance).toHaveBeenCalledWith('TEST_CHANNEL_TEST_TOPIC_0');
      expect(mockQueue.process).toHaveBeenCalled();
    });

    it('should create multiple instances for the same topic', () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      const handler3 = jest.fn();
      
      service.subscribe('TEST_CHANNEL', 'TEST_TOPIC', handler1);
      service.subscribe('TEST_CHANNEL', 'TEST_TOPIC', handler2);
      service.subscribe('TEST_CHANNEL', 'TEST_TOPIC', handler3);
      
      expect(queueService.createInstance).toHaveBeenCalledTimes(3);
      expect(queueService.createInstance).toHaveBeenNthCalledWith(1, 'TEST_CHANNEL_TEST_TOPIC_0');
      expect(queueService.createInstance).toHaveBeenNthCalledWith(2, 'TEST_CHANNEL_TEST_TOPIC_1');
      expect(queueService.createInstance).toHaveBeenNthCalledWith(3, 'TEST_CHANNEL_TEST_TOPIC_2');
    });

    it('should not create more than 10 instances per topic', () => {
      const handler = jest.fn();
      
      // Create 11 subscriptions (should only create 10)
      for (let i = 0; i < 11; i++) {
        service.subscribe('TEST_CHANNEL', 'TEST_TOPIC', handler);
      }
      
      expect(queueService.createInstance).toHaveBeenCalledTimes(10);
    });

    it('should handle errors in job processing', async () => {
      const handler = jest.fn().mockRejectedValue(new Error('Test error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      service.subscribe('TEST_CHANNEL', 'TEST_TOPIC', handler);
      
      // Get the process callback and simulate a job
      const processCallback = mockQueue.process.mock.calls[0][0];
      const mockJob = { data: { test: 'data' } };
      
      await expect(processCallback(mockJob)).rejects.toThrow('Test error');
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('publish', () => {
    it('should publish to all instances in round-robin fashion', async () => {
      const handler = jest.fn();
      
      // Create 3 instances
      service.subscribe('TEST_CHANNEL', 'TEST_TOPIC', handler);
      service.subscribe('TEST_CHANNEL', 'TEST_TOPIC', handler);
      service.subscribe('TEST_CHANNEL', 'TEST_TOPIC', handler);
      
      const event = new QueueEvent({
        channel: 'TEST_CHANNEL',
        eventName: 'TEST_EVENT',
        data: { test: 'data' }
      });
      
      await service.publish(event);
      
      expect(mockQueue.createJob).toHaveBeenCalledWith(event);
    });

    it('should warn when no subscribers exist for channel', async () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      const event = new QueueEvent({
        channel: 'NONEXISTENT_CHANNEL',
        eventName: 'TEST_EVENT',
        data: { test: 'data' }
      });
      
      await service.publish(event);
      
      expect(consoleSpy).toHaveBeenCalledWith('No subscriber for channel NONEXISTENT_CHANNEL');
      consoleSpy.mockRestore();
    });

    it('should handle publish errors', async () => {
      const handler = jest.fn();
      service.subscribe('TEST_CHANNEL', 'TEST_TOPIC', handler);
      
      // Mock createJob to throw an error
      mockQueue.createJob.mockReturnValue({
        save: jest.fn().mockRejectedValue(new Error('Publish error'))
      });
      
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      const event = new QueueEvent({
        channel: 'TEST_CHANNEL',
        eventName: 'TEST_EVENT',
        data: { test: 'data' }
      });
      
      await expect(service.publish(event)).rejects.toThrow('Publish error');
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('getQueueStats', () => {
    it('should return correct statistics', () => {
      const handler = jest.fn();
      
      // Create instances for different channels and topics
      service.subscribe('CHANNEL1', 'TOPIC1', handler);
      service.subscribe('CHANNEL1', 'TOPIC1', handler);
      service.subscribe('CHANNEL1', 'TOPIC2', handler);
      service.subscribe('CHANNEL2', 'TOPIC1', handler);
      
      const stats = service.getQueueStats();
      
      expect(stats).toEqual({
        CHANNEL1: {
          TOPIC1: {
            instanceCount: 2,
            currentIndex: 0,
            maxInstances: 10
          },
          TOPIC2: {
            instanceCount: 1,
            currentIndex: 0,
            maxInstances: 10
          }
        },
        CHANNEL2: {
          TOPIC1: {
            instanceCount: 1,
            currentIndex: 0,
            maxInstances: 10
          }
        }
      });
    });

    it('should return empty stats when no instances exist', () => {
      const stats = service.getQueueStats();
      expect(stats).toEqual({});
    });
  });
});
