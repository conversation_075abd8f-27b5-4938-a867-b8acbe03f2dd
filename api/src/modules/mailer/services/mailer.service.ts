import { HttpException, Injectable } from '@nestjs/common';
import {
  EntityNotFoundException, QueueService, SearchRequest, StringHelper
} from 'src/kernel';
import { createTransport } from 'nodemailer';
import { SettingService } from 'src/modules/settings';
import { existsSync, readFileSync } from 'fs';
import { join } from 'path';
import { render } from 'mustache';
import { SETTING_KEYS } from 'src/modules/settings/constants';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { DBLoggerService } from 'src/modules/logger';
import { IMail } from '../interfaces';
import { EmailTemplateUpdatePayload } from '../payloads/email-template-update.payload';
import { EmailTemplate } from '../schemas/email-template.schema';

const TEMPLATE_DIR = join(process.env.TEMPLATE_DIR, 'emails');
@Injectable()
export class MailerService {
  private mailerQueue;

  constructor(
    @InjectModel(EmailTemplate.name) private readonly EmailTemplateModel: Model<EmailTemplate>,
    private readonly queueService: QueueService,
    private readonly settingService: SettingService,
    private readonly logger: DBLoggerService
  ) {
    this.init();
  }

  private async init() {
    this.mailerQueue = this.queueService.createInstance('MAILER_QUEUE');
    this.mailerQueue.process(
      process.env.MAILER_CONCURRENCY || 1,
      this.process.bind(this)
    );
  }

  private async getTransport() {
    const smtp = await this.settingService.getKeyValue(SETTING_KEYS.SMTP_TRANSPORTER);
    if (!smtp || !smtp.host || !smtp.auth || !smtp.port || !smtp.auth.user || !smtp.auth.pass) {
      throw new HttpException('Invalid confirguration!', 400);
    }
    smtp.port = parseInt(smtp.port, 10);
    smtp.tls = {
      rejectUnauthorized: false
    };
    return createTransport(smtp);
  }

  private async getTemplate(template = 'default', isLayout = false): Promise<string> {
    const layout = await this.EmailTemplateModel.findOne({
      key: isLayout ? `layouts/${template}` : template
    });
    if (layout) return layout.content;

    // eslint-disable-next-line no-param-reassign
    template = StringHelper.getFileName(template, true);

    if (template === 'blank') {
      return isLayout ? '[[BODY]]' : '';
    }

    const layoutFile = isLayout ? join(TEMPLATE_DIR, 'layouts', `${template}.html`) : join(TEMPLATE_DIR, `${template}.html`);
    if (!existsSync(layoutFile)) {
      return isLayout ? '[[BODY]]' : '';
    }

    return readFileSync(layoutFile, 'utf8');
  }

  private async process(job: any, done: Function) {
    try {
      const transport = await this.getTransport();
      const data = job.data as IMail;
      let { html } = data;
      let layout = '[[BODY]]';
      let subject = '';
      if (!html && data.template) {
        // html = await this.getTemplate(data.template);
        const template = await this.EmailTemplateModel.findOne({
          key: {
            $in: [
              data.template,
              `${data.template}.html`
            ]
          }
        });
        if (!template) {
          html = '';
          layout = await this.getTemplate(data.layout, true);
        } else {
          html = template.content;
          subject = template.subject;
          layout = template.layout ? await this.getTemplate(template.layout, true) : '[[BODY]]';
        }
      }
      const siteName = await this.settingService.getKeyValue(SETTING_KEYS.SITE_NAME);
      const logoUrl = await this.settingService.getKeyValue(SETTING_KEYS.LOGO_URL);

      const settings = SettingService._settingCache;
      const body = html ? render(html, {
        ...data.data,
        siteName,
        logoUrl,
        settings
      }) : '';

      html = render(layout, {
        siteName: siteName || process.env.SITENAME || process.env.DOMAIN,
        logoUrl,
        subject: subject || data.subject
      }).replace('[[BODY]]', body);

      const senderConfig = await this.settingService.getKeyValue(SETTING_KEYS.SENDER_EMAIL);
      const senderEmail = senderConfig || process.env.SENDER_EMAIL;
      await transport.sendMail({
        from: senderEmail,
        to: Array.isArray(data.to) ? data.to.join(',') : data.to,
        cc: Array.isArray(data.cc) ? data.cc.join(',') : data.cc,
        bcc: Array.isArray(data.cc) ? data.cc.join(',') : data.cc,
        subject: subject || data.subject,
        html
      });
    } catch (e) {
      this.logger.error(e.stack || e, { context: 'MaillerService' });
    } finally {
      done();
    }
  }

  public async send(email: IMail) {
    await this.mailerQueue.createJob(email).save();
  }

  public async verify() {
    try {
      const transport = await this.getTransport();
      const siteName = await this.settingService.getKeyValue(SETTING_KEYS.SITE_NAME) || process.env.DOMAIN;
      const senderEmail = await this.settingService.getKeyValue(SETTING_KEYS.SENDER_EMAIL) || process.env.SENDER_EMAIL;
      const adminEmail = await this.settingService.getKeyValue(SETTING_KEYS.ADMIN_EMAIL) || process.env.ADMIN_EMAIL;
      return transport.sendMail({
        from: senderEmail,
        to: adminEmail,
        subject: `Test email ${siteName}`,
        html: 'Hello, this is test email!'
      });
    } catch (e) {
      return {
        hasError: true,
        error: e
      };
    }
  }

  public async getAllTemplates(req: SearchRequest) {
    const query: Record<string, any> = {};
    if (req.q) {
      const regexp = new RegExp(
        req.q.toLowerCase().replace(/[^a-zA-Z0-9 ]/g, ''),
        'i'
      );
      query.$or = [
        {
          name: { $regex: regexp }
        },
        {
          subject: { $regex: regexp }
        }
      ];
    }
    let sort = {};
    if (req.sort && req.sortBy) {
      sort = {
        [req.sortBy]: req.sort
      };
    }
    return this.EmailTemplateModel.find(query).sort(sort).lean();
  }

  public async findOne(id: string) {
    return this.EmailTemplateModel.findById(id).lean();
  }

  public async updateTemplate(id: string, payload: EmailTemplateUpdatePayload) {
    const template = await this.EmailTemplateModel.findById(id);
    if (!template) throw new EntityNotFoundException();

    template.subject = payload.subject;
    template.content = payload.content;
    template.layout = payload.layout;
    template.updatedAt = new Date();
    return template.save();
  }
}
