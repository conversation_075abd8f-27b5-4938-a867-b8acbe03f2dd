import { Expose, Transform } from 'class-transformer';
import { ObjectId } from 'mongodb';

export class ForgotDto {
  @Expose()
  @Transform(({ obj }) => obj._id)
  _id?: ObjectId;

  @Expose()
  @Transform(({ obj }) => obj.authId)
  authId: ObjectId;

  @Expose()
  source: string;

  @Expose()
  @Transform(({ obj }) => obj.sourceId)
  sourceId: ObjectId;

  @Expose()
  token: string;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}
