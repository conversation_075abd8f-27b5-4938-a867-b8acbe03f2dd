import {
  <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ot<PERSON><PERSON>y, IsBoolean, Is<PERSON><PERSON>al, <PERSON><PERSON><PERSON><PERSON>
} from 'class-validator';

export class LoginPayload {
  @IsString()
  @IsNotEmpty()
  username: string;

  @IsString()
  @MinLength(6, { message: 'Please recheck the password entered' })
  @MaxLength(100)
  @IsNotEmpty()
  password: string;

  @IsBoolean()
  @IsOptional()
  remember: boolean;
}
