import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { ScheduledTaskController } from './controllers/scheduled-task.controller';
import { ScheduledTaskService } from './services/scheduled-task.service';
import {
  ScheduledTask,
  ScheduledTaskSchema
} from './schemas/scheduled-task.schema';
import { FeedModule } from '../feed/feed.module';
import { FeedSchema, Feed } from '../feed/schemas';
import { Message, MessageSchema } from '../message/schemas';
import { Conversation, ConversationSchema } from '../message/schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: ScheduledTask.name,
        schema: ScheduledTaskSchema
      },
      {
        name: Feed.name,
        schema: FeedSchema
      },
      {
        name: Message.name,
        schema: MessageSchema
      },
      {
        name: Conversation.name,
        schema: ConversationSchema
      }
    ]),
    forwardRef(() => FeedModule)
  ],
  controllers: [ScheduledTaskController],
  providers: [ScheduledTaskService],
  exports: [ScheduledTaskService]
})
export class ScheduledTaskModule {}
