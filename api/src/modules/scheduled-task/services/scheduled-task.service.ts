import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException
} from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { ScheduledTask } from '../schemas/scheduled-task.schema';
import { CreateScheduledTaskDto } from '../dto/create-scheduled-task.dto';
import { Feed } from 'src/modules/feed/schemas';
import { EVENT, STATUS } from 'src/kernel/constants';
import { FeedService } from 'src/modules/feed/services';
import { QueueEvent, QueueEventService, StringHelper } from 'src/kernel';
import { PERFORMER_FEED_CHANNEL } from 'src/modules/feed/constants';
import { Message } from 'src/modules/message/schemas';
import { plainToInstance } from 'class-transformer';
import { MessageDto } from 'src/modules/message/dtos';
import { Conversation } from 'src/modules/message/schemas';
import { MESSAGE_CHANNEL, MESSAGE_EVENT } from 'src/modules/message/constants';

@Injectable()
export class ScheduledTaskService {
  constructor(
    @InjectModel(ScheduledTask.name)
    private readonly ScheduledTaskModel: Model<ScheduledTask>,
    @InjectModel(Feed.name) private readonly FeedModel: Model<Feed>,
    @InjectModel(Message.name) private readonly MessageModel: Model<Message>,
    @InjectModel(Conversation.name)
    private readonly ConversationModel: Model<Conversation>,

    @Inject(forwardRef(() => FeedService))
    private readonly feedService: FeedService,
    private readonly queueEventService: QueueEventService
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async handleCron() {
    const now = new Date();
    const tasksToExecute = await this.ScheduledTaskModel.find({
      scheduledAt: { $lte: now }
    }).exec();

    for (const task of tasksToExecute) {
      try {
        // Execute task based on type
        await this.executeTask(task);
        // Delete task after successful execution
        await this.ScheduledTaskModel.findByIdAndDelete(task._id).exec();
      } catch (error) {
        console.error(`Failed to execute task ${task._id}:`, error);
      }
    }
  }

  private async executeTask(task: ScheduledTask): Promise<void> {
    switch (task.type) {
      case 'post':
        // Update feed status to active and clear scheduledAt
        await this.FeedModel.updateOne(
          { _id: task.sourceId },
          {
            $set: {
              status: STATUS.ACTIVE,
              scheduledAt: null
            }
          }
        );

        // Publish feed updated event
        const updatedFeed = await this.feedService.findById(task.sourceId);
        await this.queueEventService.publish(
          new QueueEvent({
            channel: PERFORMER_FEED_CHANNEL,
            eventName: EVENT.CREATED,
            data: {
              ...updatedFeed,
              oldStatus: STATUS.SCHEDULED
            }
          })
        );
        break;
      case 'message':
        // Check if message still exists before processing
        const scheduledMessage = await this.MessageModel.findById(
          task.sourceId
        );
        if (!scheduledMessage) {
          // Delete the scheduled task since the source message no longer exists
          await this.ScheduledTaskModel.findByIdAndDelete(task._id);
          console.log(
            `Deleted orphaned scheduled task ${task._id} - message ${task.sourceId} not found`
          );
          return;
        }

        // Update message status to active
        const updatedMessage = await this.MessageModel.findOneAndUpdate(
          { _id: task.sourceId },
          {
            $set: {
              isScheduled: false
            }
          },
          { new: true }
        );

        if (!updatedMessage) {
          throw new Error(`Message ${task.sourceId} not found after update`);
        }
        const dto = plainToInstance(MessageDto, updatedMessage.toObject());

        // Update the message DTO to reflect the change
        dto.isScheduled = false;

        // Update conversation's last message
        await this.ConversationModel.updateOne(
          { _id: dto.conversationId },
          {
            $set: {
              lastMessage: StringHelper.truncate(dto.text || '', 30),
              lastSenderId: dto.senderId,
              lastMessageCreatedAt: new Date(),
              isSale: dto.isSale,
              isBought: dto.isBought
            }
          }
        );

        await this.queueEventService.publish({
          channel: MESSAGE_CHANNEL,
          eventName: MESSAGE_EVENT.CREATED,
          data: dto
        });
        break;
      // Add more task types as needed
      default:
        break;
    }
  }

  async create(
    createScheduledTaskDto: CreateScheduledTaskDto
  ): Promise<ScheduledTask> {
    const createdTask = new this.ScheduledTaskModel(createScheduledTaskDto);
    return createdTask.save();
  }

  async findAll(): Promise<ScheduledTask[]> {
    return this.ScheduledTaskModel.find().exec();
  }

  async findOne(id: string): Promise<ScheduledTask> {
    const task = await this.ScheduledTaskModel.findById(id).exec();
    if (!task) {
      throw new NotFoundException(`Scheduled task with ID ${id} not found`);
    }
    return task;
  }

  async findByType(type: string): Promise<ScheduledTask[]> {
    return this.ScheduledTaskModel.find({ type }).exec();
  }

  async remove(id: string): Promise<ScheduledTask> {
    const deletedTask =
      await this.ScheduledTaskModel.findByIdAndDelete(id).exec();
    if (!deletedTask) {
      throw new NotFoundException(`Scheduled task with ID ${id} not found`);
    }
    return deletedTask;
  }

  async removeBySourceId(sourceId: string): Promise<void> {
    const deletedTasks = await this.ScheduledTaskModel.deleteMany({
      sourceId
    }).exec();
    if (deletedTasks.deletedCount === 0) {
      throw new NotFoundException(
        `No scheduled tasks found with source ID ${sourceId}`
      );
    }
  }
}
