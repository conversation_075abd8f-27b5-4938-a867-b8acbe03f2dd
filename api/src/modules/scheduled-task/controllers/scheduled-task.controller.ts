import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  HttpCode,
  HttpStatus,
  UsePipes,
  ValidationPipe
} from '@nestjs/common';

import { DataResponse } from 'src/kernel';
import { ScheduledTaskService } from '../services/scheduled-task.service';
import { CreateScheduledTaskDto } from '../dto/create-scheduled-task.dto';

@Controller('scheduled-tasks')
export class ScheduledTaskController {
  constructor(private readonly scheduledTaskService: ScheduledTaskService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @UsePipes(new ValidationPipe())
  async create(@Body() createScheduledTaskDto: CreateScheduledTaskDto) {
    const task = await this.scheduledTaskService.create(createScheduledTaskDto);
    return DataResponse.ok(task);
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  async findAll() {
    const tasks = await this.scheduledTaskService.findAll();
    return DataResponse.ok(tasks);
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  async findOne(@Param('id') id: string) {
    const task = await this.scheduledTaskService.findOne(id);
    return DataResponse.ok(task);
  }

  @Get('type/:type')
  @HttpCode(HttpStatus.OK)
  async findByType(@Param('type') type: string) {
    const tasks = await this.scheduledTaskService.findByType(type);
    return DataResponse.ok(tasks);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  async remove(@Param('id') id: string) {
    const task = await this.scheduledTaskService.remove(id);
    return DataResponse.ok(task);
  }

  @Delete('source/:sourceId')
  @HttpCode(HttpStatus.OK)
  async removeBySourceId(@Param('sourceId') sourceId: string) {
    await this.scheduledTaskService.removeBySourceId(sourceId);
    return DataResponse.ok({ success: true });
  }
}
