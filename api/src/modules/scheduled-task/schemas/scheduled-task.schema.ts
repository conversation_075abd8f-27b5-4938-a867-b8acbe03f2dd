import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type ScheduledTaskDocument = ScheduledTask & Document;

@Schema({
  collection: 'scheduled_task'
})
export class ScheduledTask {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    auto: true
  })
  _id: MongooseSchema.Types.ObjectId;

  @Prop({
    type: Date,
    required: true
  })
  scheduledAt: Date;

  @Prop({
    type: String,
    required: true
  })
  type: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: true
  })
  sourceId: MongooseSchema.Types.ObjectId;
}

export const ScheduledTaskSchema = SchemaFactory.createForClass(ScheduledTask);

ScheduledTaskSchema.index({ scheduledAt: 1 });
