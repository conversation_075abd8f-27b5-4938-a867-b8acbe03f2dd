import {
  Controller,
  Injectable,
  UseGuards,
  Body,
  Post,
  HttpCode,
  HttpStatus,
  UsePipes,
  ValidationPipe,
  Put,
  Param,
  Delete,
  Get,
  Query,
  Request,
  forwardRef,
  Inject,
  UseInterceptors
} from '@nestjs/common';
import { RoleGuard } from 'src/modules/auth/guards';
import { DataResponse, PageableData } from 'src/kernel';
import { CurrentUser, Roles } from 'src/modules/auth';
import { UserDto } from 'src/modules/user/dtos';
import { AuthService } from 'src/modules/auth/services';
import { MediaItemInterceptor } from 'src/modules/media-library/interceptors';
import { SelectedMediaObject } from 'src/modules/media-library/decorators';
import {
  FeedCreatePayload,
  FeedSearchRequest,
  PollCreatePayload
} from '../payloads';
import { FeedDto } from '../dtos';
import { FeedService } from '../services';

@Injectable()
@Controller('feeds/performers')
export class PerformerFeedController {
  constructor(
    private readonly feedService: FeedService,
    @Inject(forwardRef(() => AuthService))
    private readonly authService: AuthService
  ) {}

  @Post('/')
  @Roles('performer')
  @UseGuards(RoleGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @UseInterceptors(
    MediaItemInterceptor({
      inputFields: ['fileIds', 'teaserId', 'thumbnailId'],
      includingFileInfo: true
    })
  )
  async create(
    @Body() body: FeedCreatePayload,
    @SelectedMediaObject() mediaObject: Record<string, any>,
    @CurrentUser() user: UserDto
  ): Promise<DataResponse<any>> {
    const fileIds = (mediaObject?.fileIds || []).map((m) => m?.file._id);
    const teaserId = mediaObject?.teaserId?.length
      ? mediaObject?.teaserId[0].file._id
      : null;
    const thumbnailId = mediaObject?.thumbnailId?.length
      ? mediaObject?.thumbnailId[0].file._id
      : null;
    const payload = body;
    payload.fileIds = fileIds;
    payload.teaserId = teaserId;
    payload.thumbnailId = thumbnailId;
    const data = await this.feedService.create(payload, user);

    return DataResponse.ok(data);
  }

  @Get('/')
  @Roles('performer')
  @UseGuards(RoleGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async getMyFeeds(
    @Query() query: FeedSearchRequest,
    @CurrentUser() performer: UserDto,
    @Request() req: any
  ): Promise<DataResponse<PageableData<any>>> {
    const auth = {
      _id: req.authUser.authId,
      source: req.authUser.source,
      sourceId: req.authUser.sourceId
    };
    const jwToken = await this.authService.generateJWT(auth, {
      expiresIn: 4 * 60 * 60
    });
    const data = await this.feedService.search(query, performer, jwToken);
    return DataResponse.ok(data);
  }

  @Get('/:id')
  @Roles('performer')
  @UseGuards(RoleGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async getPerformerFeed(
    @CurrentUser() user: UserDto,
    @Param('id') id: string,
    @Request() req: any
  ): Promise<DataResponse<FeedDto>> {
    const auth = {
      _id: req.authUser.authId,
      source: req.authUser.source,
      sourceId: req.authUser.sourceId
    };
    const jwToken = await this.authService.generateJWT(auth, {
      expiresIn: 4 * 60 * 60
    });
    const data = await this.feedService.findOne(id, user, jwToken);
    return DataResponse.ok(data);
  }

  @Put('/:id')
  @Roles('performer')
  @UseGuards(RoleGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @UseInterceptors(
    MediaItemInterceptor({
      inputFields: ['fileIds', 'teaserId', 'thumbnailId'],
      includingFileInfo: true
    })
  )
  async updateFeed(
    @CurrentUser() user: UserDto,
    @Param('id') id: string,
    @Body() body: FeedCreatePayload,
    @SelectedMediaObject() mediaObject: Record<string, any>
  ): Promise<DataResponse<any>> {
    const fileIds = (mediaObject?.fileIds || []).map((m) => m.file._id);
    const teaserId = mediaObject?.teaserId?.length
      ? mediaObject?.teaserId[0].file._id
      : null;
    const thumbnailId = mediaObject?.thumbnailId?.length
      ? mediaObject?.thumbnailId[0].file._id
      : null;
    const payload = body;
    payload.fileIds = fileIds.length ? fileIds : undefined;
    payload.teaserId = teaserId || undefined;
    payload.thumbnailId = thumbnailId || undefined;

    const data = await this.feedService.updateFeed(id, user, payload);
    return DataResponse.ok(data);
  }

  @Delete('/:id')
  @Roles('performer')
  @UseGuards(RoleGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async deletePerformerFeed(
    @CurrentUser() user: UserDto,
    @Param('id') id: string
  ): Promise<DataResponse<any>> {
    const data = await this.feedService.deleteFeed(id, user);
    return DataResponse.ok(data);
  }

  @Post('/polls')
  @Roles('performer')
  @UseGuards(RoleGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createPollFeed(
    @Body() payload: PollCreatePayload,
    @CurrentUser() user: UserDto
  ): Promise<DataResponse<any>> {
    const data = await this.feedService.createPoll(payload, user);
    return DataResponse.ok(data);
  }

  @Post('/pin-to-profile/:id')
  @Roles('performer')
  @UseGuards(RoleGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async pinToProfile(
    @CurrentUser() user: UserDto,
    @Param('id') id: string
  ): Promise<DataResponse<any>> {
    await this.feedService.pinToProfile(id, user);
    return DataResponse.ok({ status: 'ok' });
  }

  @Post('/unpin-to-profile/:id')
  @Roles('performer')
  @UseGuards(RoleGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async unpinToProfile(
    @CurrentUser() user: UserDto,
    @Param('id') id: string
  ): Promise<DataResponse<any>> {
    await this.feedService.unpinToProfile(id, user);
    return DataResponse.ok({ status: 'ok' });
  }

  @Get('/scheduled/list')
  @Roles('performer')
  @UseGuards(RoleGuard)
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async getScheduledFeeds(
    @Query() query: FeedSearchRequest,
    @CurrentUser() performer: UserDto
  ): Promise<DataResponse<PageableData<any>>> {
    const data = await this.feedService.getScheduledFeeds(query, performer);
    return DataResponse.ok(data);
  }
}
