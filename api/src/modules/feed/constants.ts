/* eslint-disable no-shadow */
export const FEED_TYPES = ['text', 'video', 'photo'];

export enum FEED_TYPE {
  TEXT = 'text',
  VIDEO = 'video',
  PHOTO = 'photo'
}

export const FEED_SOURCE = {
  PERFORMER: 'performer'
};

export const POLL_TARGET_SOURCE = {
  FEED: 'feed'
};

export const VOTE_TARGET_SOURCE = {
  POLL_FEED: 'POLL_FEED'
};

export const PERFORMER_FEED_CHANNEL = 'PERFORMER_FEED_CHANNEL';

export const VOTE_FEED_CHANNEL = 'VOTE_FEED_CHANNEL';

export const FEED_VIDEO_CHANNEL = 'FEED_VIDEO_CHANNEL';

export const FEED_TEASER_CHANNEL = 'FEED_TEASER_CHANNEL';
