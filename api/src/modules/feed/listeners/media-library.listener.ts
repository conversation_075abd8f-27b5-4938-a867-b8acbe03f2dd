import { Injectable } from '@nestjs/common';
import { QueueEventService, QueueEvent } from 'src/kernel';
import { EVENT } from 'src/kernel/constants';
import {
  MEDIA_LIBRARY_CHANNEL, REF_FEED_MAIN_FILE, REF_FEED_TEASER_FILE, REF_FEED_THUMBNAIL_FILE
} from 'src/modules/media-library/constants';
import { MediaItemDto } from 'src/modules/media-library/dtos';
import { FeedService } from '../services';

const HANDLE_FEED_FILES = 'HANDLE_FEED_FILES';

@Injectable()
export class FeedMediaLibraryListener {
  constructor(
    private readonly feedService: FeedService,
    private readonly queueEventService: QueueEventService
  ) {
    this.queueEventService.subscribe(
      MEDIA_LIBRARY_CHANNEL,
      HANDLE_FEED_FILES,
      this.handler.bind(this)
    );
  }

  public async handler(event: QueueEvent) {
    try {
      if (![EVENT.DELETED].includes(event.eventName)) return;

      const dto: MediaItemDto = event.data;
      const { refItems } = dto;
      // find and filter for related data
      await refItems.reduce(async (lp, refItem) => {
        await lp;

        // video
        switch (refItem.itemType) {
          case REF_FEED_MAIN_FILE: {
            // TODO - check and update logic here
            await this.feedService.removeMainFile(refItem.itemId, dto.fileId);
            break;
          }
          case REF_FEED_TEASER_FILE: {
            await this.feedService.removeTeaserFile(refItem.itemId);
            break;
          }
          case REF_FEED_THUMBNAIL_FILE: {
            await this.feedService.removeThumbnailFile(refItem.itemId);
            break;
          }
          default:
            break;
        }

        return Promise.resolve();
      }, Promise.resolve());
    } catch (e) {
      console.log('feed vault error', e);
    }
  }
}
