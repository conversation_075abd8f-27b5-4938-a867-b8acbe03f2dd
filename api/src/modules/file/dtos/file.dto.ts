import { ObjectId } from 'mongodb';
import { getConfig } from 'src/kernel';
import { isUrl } from 'src/kernel/helpers/string.helper';
import { Expose, Transform } from 'class-transformer';
import { generateFileHashKey } from '../shared';

interface IRefItem {
  itemType: string;
  itemId: ObjectId | any;
}

export class FileDto {
  @Expose()
  @Transform(({ obj }) => obj._id)
  _id: ObjectId;

  @Expose()
  type: string; // video, podcast, file, etc...

  @Expose()
  name: string;

  @Expose()
  description: string;

  @Expose()
  mimeType: string;

  @Expose()
  server: string; // eg: local, aws, etc... we can create a helper to filter and get direct link

  @Expose()
  path: string; // path of key in local or server

  @Expose()
  absolutePath: string;

  @Expose()
  width: number; // for video, img

  @Expose()
  height: number; // for video, img

  @Expose()
  duration: number; // for video, podcast

  @Expose()
  size: number; // in byte

  @Expose()
  status: string;

  @Expose()
  encoding: string;

  @Expose()
  thumbnails: Record<string, any>[];

  @Expose()
  blurImagePath: string;

  @Expose()
  @Transform(({ obj }) => obj.refItems)
  refItems: IRefItem[];

  @Expose()
  @Transform(({ obj }) => obj.createdBy)
  createdBy: ObjectId;

  @Expose()
  @Transform(({ obj }) => obj.updatedBy)
  updatedBy: ObjectId;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;

  public getPublicPath() {
    if (this.absolutePath) {
      return this.absolutePath.replace(getConfig('file').publicDir, '');
    }

    return this.path || '';
  }

  public getUrl(authenticated = false): string {
    if (!this.path) return '';
    if (isUrl(this.path)) return this.path;

    const url = new URL(this.path, getConfig('app').baseUrl);
    if (!authenticated) return url.href;

    url.searchParams.append('token', generateFileHashKey(this._id));
    return url.href;
  }

  public getThumbnails(): string[] {
    if (!this.thumbnails || !this.thumbnails.length) {
      return [];
    }

    return this.thumbnails.map((t) => {
      if (isUrl(t.path)) return t.path;

      return new URL(
        t.path,
        getConfig('app').baseUrl
      ).href;
    });
  }

  /**
   * get single thumbnail
   * @returns
   */
  public getThumbnail(): string {
    const thumbnails = this.getThumbnails();
    if (!thumbnails.length) return null;
    return thumbnails[0];
  }

  public getBlurImage(): string {
    if (!this.blurImagePath) return '';
    if (isUrl(this.blurImagePath)) return this.blurImagePath;

    return new URL(
      this.blurImagePath,
      getConfig('app').baseUrl
    ).href;
  }

  static getPublicUrl(filePath: string): string {
    if (!filePath) return '';
    if (isUrl(filePath)) return filePath;
    return new URL(filePath, getConfig('app').baseUrl).href;
  }

  public isVideo() {
    return (this.mimeType || '').toLowerCase().includes('video');
  }

  public isImage() {
    return (this.mimeType || '').toLowerCase().includes('image');
  }

  public getMediaType() {
    if (this.isVideo()) return 'video';
    if (this.isImage()) return 'image';
    // todo - define others?
    return 'media';
  }

  public toResponse() {
    const data = { ...this };
    delete data.absolutePath;
    delete data.path;
    return data;
  }

  public toPublicResponse(authenticated = false) {
    return {
      _id: this._id,
      name: this.name,
      type: this.type,
      mimeType: this.mimeType,
      url: this.getUrl(authenticated),
      thumbnails: this.getThumbnails(),
      blurImage: this.getBlurImage(),
      preview: this.getBlurImage(),
      width: this.width,
      height: this.height,
      size: this.size,
      duration: this.duration,
      status: this.status,
      mediaType: this.getMediaType()
    };
  }
}
