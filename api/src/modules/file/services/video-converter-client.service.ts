import { Injectable } from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import axios from 'axios';
import * as FormData from 'form-data';
import { createReadStream, existsSync, createWriteStream } from 'fs';
import { join } from 'path';
import { StringHelper } from 'src/kernel';
import { io, Socket } from 'socket.io-client';
import {
  IConvertOptions,
  IConvertProgress,
  IConvertResponse
} from './video.service';

@Injectable()
export class VideoConverterClientService {
  private readonly videoConverterApiUrl: string;

  private readonly videoConverterSocketUrl: string;

  private socketConnections = new Map<string, Socket>();

  constructor(private readonly config: ConfigService) {
    // Get video converter API URL from config
    this.videoConverterApiUrl = this.config.get('app.videoConverterApiUrl');
    // Derive WebSocket URL from API URL (replace http with ws)
    this.videoConverterSocketUrl = this.videoConverterApiUrl.replace(/^https?/, 'ws');
  }

  /**
   * Create a WebSocket connection to the video converter API
   * @param tempId Temporary identifier (will be replaced with actual socket.id)
   * @param onProgress Progress callback function
   * @returns Promise that resolves when connection is established
   */
  private createSocketConnection(
    tempId: string,
    onProgress?: (progress: IConvertProgress) => void
  ): Promise<Socket> {
    return new Promise((resolve, reject) => {
      const socket = io(this.videoConverterSocketUrl, {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: false
      });

      socket.on('connect', () => {
        console.log(`Connected to video converter WebSocket: ${socket.id}`);
        // Store socket using its actual ID
        this.socketConnections.set(socket.id, socket);
        resolve(socket);
      });

      socket.on('connect_error', (error) => {
        console.error('Failed to connect to video converter WebSocket:', error);
        reject(error);
      });

      socket.on('video_conversion_progress', (data) => {
        if (onProgress && data.progress) {
          onProgress(data.progress);
        }
      });

      socket.on('video_conversion_complete', (data) => {
        console.log('Video conversion completed:', data);
        this.cleanupSocketConnection(socket.id);
      });

      socket.on('video_conversion_error', (data) => {
        console.error('Video conversion error:', data);
        this.cleanupSocketConnection(socket.id);
      });

      socket.on('disconnect', () => {
        console.log(`Disconnected from video converter WebSocket: ${socket.id}`);
        this.cleanupSocketConnection(socket.id);
      });
    });
  }

  /**
   * Clean up a WebSocket connection
   * @param socketId Socket identifier to clean up
   */
  private cleanupSocketConnection(socketId: string): void {
    const socket = this.socketConnections.get(socketId);
    if (socket) {
      socket.disconnect();
      this.socketConnections.delete(socketId);
    }
  }

  /**
   * Convert video using the video-converter-api service
   * @param filePath Path to the video file to convert
   * @param options Conversion options
   * @returns Conversion result
   */
  public async convert2Mp4(
    filePath: string,
    options = {} as IConvertOptions
  ): Promise<IConvertResponse> {
    let socketId: string | null = null;
    let socket: Socket | null = null;

    try {
      if (!existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Create WebSocket connection for progress updates
      if (options.onProgress) {
        try {
          socket = await this.createSocketConnection('temp', options.onProgress);
          socketId = socket.id; // Use the actual socket client ID
          console.log(`Using socket ID for video conversion: ${socketId}`);
        } catch (socketError) {
          console.warn('Failed to establish WebSocket connection, proceeding without progress updates:', socketError);
        }
      }

      // Upload file to video converter API
      const uploadResult = await this.uploadVideoToConverter(filePath);

      // Request conversion with socket ID for progress updates
      const convertResult = await this.requestConversion(
        uploadResult.data.path,
        { ...options, socketId }
      );

      // Download converted file back to main API
      const downloadResult = await this.downloadConvertedVideo(
        convertResult.data.toPath,
        filePath
      );

      // Clean up socket connection
      if (socketId) {
        this.cleanupSocketConnection(socketId);
      }

      return downloadResult;
    } catch (error) {
      // Clean up socket connection on error
      if (socketId) {
        this.cleanupSocketConnection(socketId);
      }
      throw new Error(`Video conversion failed: ${error.message}`);
    }
  }

  /**
   * Upload video file to video converter API
   * @param filePath Path to the video file
   * @returns Upload result
   */
  private async uploadVideoToConverter(filePath: string): Promise<any> {
    const formData = new FormData();
    formData.append('video', createReadStream(filePath));

    const response = await axios.post(
      `${this.videoConverterApiUrl}/video-converter/upload`,
      formData,
      {
        headers: {
          ...formData.getHeaders()
        },
        timeout: 300000 // 5 minutes timeout for upload
      }
    );

    return response.data;
  }

  /**
   * Request video conversion from the video converter API
   * @param remotePath Path to the uploaded file on converter API
   * @param options Conversion options with socketId
   * @returns Conversion result
   */
  private async requestConversion(
    remotePath: string,
    options: IConvertOptions & { socketId?: string }
  ): Promise<any> {
    const convertRequest = {
      filePath: remotePath,
      size: options.size,
      socketId: options.socketId // Include socketId for WebSocket progress updates
    };

    const response = await axios.post(
      `${this.videoConverterApiUrl}/video-converter/convert`,
      convertRequest,
      {
        timeout: 1800000 // 30 minutes timeout for conversion
      }
    );

    return response.data;
  }

  /**
   * Download converted video from video converter API
   * @param remoteConvertedPath Path to converted file on converter API
   * @param originalFilePath Original file path to determine local storage location
   * @returns Local file information
   */
  private async downloadConvertedVideo(
    remoteConvertedPath: string,
    originalFilePath: string
  ): Promise<IConvertResponse> {
    // Generate local path for converted file
    const fileName = `${StringHelper.randomString(5)}_${StringHelper.getFileName(originalFilePath, true)}.mp4`;
    const localPath = join(
      StringHelper.getFilePath(originalFilePath),
      fileName
    );

    // Download file from video converter API
    const response = await axios.get(
      `${this.videoConverterApiUrl}/video-converter/files/download`,
      {
        params: { path: remoteConvertedPath },
        responseType: 'stream',
        timeout: 300000 // 5 minutes timeout for download
      }
    );

    // Save to local file system
    const writer = createWriteStream(localPath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        // Clean up remote files after successful download
        this.cleanupRemoteFiles(remoteConvertedPath).catch(console.error);

        resolve({
          fileName,
          toPath: localPath
        });
      });
      writer.on('error', reject);
    });
  }

  /**
   * Clean up remote files on video converter API
   * @param remotePath Path to file to clean up
   */
  private async cleanupRemoteFiles(remotePath: string): Promise<void> {
    try {
      await axios.delete(
        `${this.videoConverterApiUrl}/video-converter/files/cleanup`,
        {
          data: { path: remotePath },
          timeout: 30000 // 30 seconds timeout
        }
      );
    } catch (error) {
      console.warn(
        `Failed to cleanup remote file ${remotePath}:`,
        error.message
      );
    }
  }

  /**
   * Get video metadata using video converter API
   * @param filePath Path to video file
   * @returns Video metadata
   */
  public async getMetaData(filePath: string): Promise<any> {
    try {
      // Upload file first
      const uploadResult = await this.uploadVideoToConverter(filePath);

      // Get metadata
      const response = await axios.post(
        `${this.videoConverterApiUrl}/video-converter/metadata`,
        { filePath: uploadResult.data.path },
        { timeout: 60000 }
      );

      // Clean up uploaded file
      this.cleanupRemoteFiles(uploadResult.data.path).catch(console.error);

      return response.data.data;
    } catch (error) {
      throw new Error(`Failed to get video metadata: ${error.message}`);
    }
  }

  /**
   * Create video thumbnails using video converter API
   * @param filePath Path to video file
   * @param options Thumbnail options
   * @returns Array of thumbnail file names
   */
  public async createThumbs(
    filePath: string,
    options: {
      toFolder: string;
      count?: number;
      size?: string;
    }
  ): Promise<string[]> {
    try {
      // Upload file first
      const uploadResult = await this.uploadVideoToConverter(filePath);

      // Create thumbnails
      const response = await axios.post(
        `${this.videoConverterApiUrl}/video-converter/thumbnails`,
        {
          filePath: uploadResult.data.path,
          toFolder: options.toFolder,
          count: options.count || 3,
          size: options.size || '480x?'
        },
        { timeout: 120000 }
      );

      // Download thumbnails to local folder
      const thumbnailNames = response.data.data;
      const localThumbnails = [];

      for (const thumbName of thumbnailNames) {
        const localThumbPath = join(options.toFolder, thumbName);
        await this.downloadFile(
          `${this.videoConverterApiUrl}/video-converter/files/download?path=${thumbName}`,
          localThumbPath
        );
        localThumbnails.push(thumbName);
      }

      // Clean up remote files
      this.cleanupRemoteFiles(uploadResult.data.path).catch(console.error);
      for (const thumbName of thumbnailNames) {
        this.cleanupRemoteFiles(thumbName).catch(console.error);
      }

      return localThumbnails;
    } catch (error) {
      throw new Error(`Failed to create video thumbnails: ${error.message}`);
    }
  }

  /**
   * Download a file from URL to local path
   * @param url URL to download from
   * @param localPath Local path to save file
   */
  private async downloadFile(url: string, localPath: string): Promise<void> {
    const response = await axios.get(url, {
      responseType: 'stream',
      timeout: 60000
    });

    const writer = require('fs').createWriteStream(localPath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
  }

  /**
   * Check if video supports HTML5 playback
   * @param filePath Path to video file
   * @returns True if supports HTML5
   */
  public async isSupportHtml5(filePath: string): Promise<boolean> {
    try {
      const metadata = await this.getMetaData(filePath);
      if (!metadata?.streams?.length) return false;

      const videoStream = metadata.streams.find(
        (s) => s.codec_type === 'video'
      );
      return ['h264', 'vp8'].includes(videoStream?.codec_name);
    } catch (error) {
      console.warn(
        `Failed to check HTML5 support for ${filePath}:`,
        error.message
      );
      return false;
    }
  }
}
