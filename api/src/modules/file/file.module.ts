import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from '../auth/auth.module';
import { SocketModule } from '../socket/socket.module';
import { FileController } from './controllers/file.controller';
import { FileService, FileVideoService } from './services';
import { ImageService } from './services/image.service';
import { VideoConverterClientService } from './services/video-converter-client.service';
import { File, FileSchema } from './schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: File.name,
        schema: FileSchema
      }
    ]),
    forwardRef(() => AuthModule),
    forwardRef(() => SocketModule)
  ],
  providers: [FileService, ImageService, FileVideoService, VideoConverterClientService],
  controllers: [FileController],
  exports: [FileService, ImageService, FileVideoService, VideoConverterClientService]
})
export class FileModule {}
