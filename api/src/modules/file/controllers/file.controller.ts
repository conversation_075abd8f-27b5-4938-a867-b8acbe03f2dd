import {
  HttpC<PERSON>,
  HttpStatus,
  Controller,
  Get,
  Query,
  Res,
  ForbiddenException
} from '@nestjs/common';
import { FileService } from '../services';
import { verifyHash } from '../shared';

@Controller('files')
export class FileController {
  constructor(
    private readonly fileService: FileService
  ) { }

  @Get('download')
  @HttpCode(HttpStatus.OK)
  public async downloadFile(
    @Res() response: any,
    @Query('key') key: string
  ): Promise<any> {
    const info = await this.fileService.getStreamToDownload(key);
    response.setHeader(
      'Content-Disposition',
      `attachment; filename=${info.file.name}`
    );

    info.stream.pipe(response);
  }

  @Get('auth/check')
  @HttpCode(HttpStatus.OK)
  public async checkAuth(
    @Query('token') token: string
  ): Promise<any> {
    if (!token) return false;
    const isValid = verifyHash(token);
    if (!isValid) throw new ForbiddenException();
    return true;
  }
}
