import * as jwt from 'jsonwebtoken';
import { ObjectId } from 'mongodb';

export const generateFileHashKey = (fileId: string | ObjectId, options: any = {}): string => {
  const newOptions = {
    expiresIn: 60 * 60, // expire in 1h
    ...(options || {})
  };
  return jwt.sign(
    {
      fileId
    },
    process.env.TOKEN_SECRET,
    {
      expiresIn: newOptions.expiresIn
    }
  );
};

export const verifyHash = (token: string) => {
  try {
    return jwt.verify(token, process.env.TOKEN_SECRET);
  } catch (e) {
    return false;
  }
};
