import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { User } from '../schemas/user.schema';

@Injectable()
export class UserStatusService implements OnModuleInit {
  constructor(
    @InjectModel(User.name) private readonly UserModel: Model<User>
  ) {}

  async onModuleInit() {
    await this.resetAllUsersOnlineStatus();
  }

  private async resetAllUsersOnlineStatus() {
    await this.UserModel.updateMany(
      {},
      {
        $set: {
          isOnline: false,
          onlineAt: null
        }
      }
    );
  }
}
