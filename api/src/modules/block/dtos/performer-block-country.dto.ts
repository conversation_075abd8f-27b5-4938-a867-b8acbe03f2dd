import { ObjectId } from 'mongodb';
import { Expose, Transform } from 'class-transformer';

export class PerformerBlockCountryDto {
  @Expose()
  @Transform(({ obj }) => obj._id)
  _id: ObjectId;

  @Expose()
  @Transform(({ obj }) => obj.sourceId)
  sourceId: ObjectId;

  @Expose()
  source: string;

  @Expose()
  countryCodes: string[];

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}
