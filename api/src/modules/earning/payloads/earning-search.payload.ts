import { SearchRequest } from 'src/kernel/common';
import { ObjectId } from 'mongodb';
import {
  IsString,
  IsOptional,
  IsNotEmpty,
  IsBooleanString,
  IsMongoId
} from 'class-validator';
// import { IsValidDateString } from 'src/modules/utils/decorators/is-valid-date-string';

export class EarningSearchRequest extends SearchRequest {
  @IsString()
  @IsOptional()
  @IsMongoId()
  userId: string | ObjectId;

  @IsString()
  @IsOptional()
  @IsMongoId()
  performerId: string | ObjectId;

  @IsString()
  @IsOptional()
  @IsMongoId()
  transactionId: string | ObjectId;

  @IsString()
  @IsOptional()
  sourceType: string;

  @IsString()
  @IsOptional()
  type: string;

  @IsString()
  @IsOptional()
  payoutStatus: string;

  @IsOptional()
  // @IsValidDateString()
  fromDate: string;

  @IsOptional()
  // @IsValidDateString()
  toDate: string;

  @IsOptional()
  // @IsValidDateString()
  paidAt: string;

  @IsBooleanString()
  @IsOptional()
  isPaid: boolean;

  @IsString()
  @IsOptional()
  paymentStatus: string;

  @IsString()
  @IsOptional()
  userUsername: string;

  @IsString()
  @IsOptional()
  performerUsername: string;
}

export class UpdateEarningStatusPayload {
  @IsString()
  @IsOptional()
  @IsMongoId()
  performerId?: string;

  @IsNotEmpty()
  // @IsValidDateString()
  fromDate?: string;

  @IsNotEmpty()
  // @IsValidDateString()
  toDate?: string;

  @IsString()
  @IsOptional()
  payoutStatus?: string;

  @IsString()
  @IsOptional()
  paymentStatus?: string;
}
