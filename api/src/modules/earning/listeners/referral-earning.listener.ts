import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { Model } from 'mongoose';
import { QueueEventService, QueueEvent } from 'src/kernel';
import { EVENT } from 'src/kernel/constants';
import { SettingService } from 'src/modules/settings/services';
import { SETTING_KEYS } from 'src/modules/settings/constants';
import { PerformerService } from 'src/modules/performer/services';
import { PAYMENT_GATEWAY } from 'src/modules/payment/constants';
import { UserService } from 'src/modules/user/services';
import { InjectModel } from '@nestjs/mongoose';
import { Earning } from '../schemas/earning.schema';

const REFERRAL_EARNING_CHANNEL = 'REFERRAL_EARNING_CHANNEL';

@Injectable()
export class ReferralEarningListener {
  constructor(
    @InjectModel(Earning.name) private readonly EarningModel: Model<Earning>,
    private readonly queueEventService: QueueEventService,
    private readonly settingService: SettingService,
    @Inject(forwardRef(() => UserService))
    private readonly performerService: PerformerService,
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService
  ) {
    this.queueEventService.subscribe(
      'REFERRAL_CHANNEL',
      REFERRAL_EARNING_CHANNEL,
      this.handleListen.bind(this)
    );
  }

  public async handleListen(event: QueueEvent) {
    // DISABLED: Referral reward system - only tracking signups now, no commission rewards
    // This listener is disabled to prevent automatic referral signup bonus payments
    return;

    // const {
    //   eventName,
    //   data: { inviterId, member, memberRoles }
    // } = event;
    // if (eventName !== EVENT.CREATED) {
    //   return;
    // }
    // // if inviter is performer => add earning
    // const inviter = await this.performerService.findById(inviterId);
    // if (!inviter || !inviter._id || member.isPaidFlatFeeForInviter) {
    //   return;
    // }
    // try {
    //   const reward = memberRoles === 'user'
    //     ? await this.settingService.getKeyValue(
    //       SETTING_KEYS.INVITE_USER_FLAT_FEE
    //     )
    //     : await this.settingService.getKeyValue(
    //       SETTING_KEYS.INVITE_PERFORMER_FLAT_FEE
    //     );

    //   const newEarning = new this.EarningModel();
    //   newEarning.set('commission', 1);
    //   newEarning.set('grossPrice', reward);
    //   newEarning.set('netPrice', reward);
    //   newEarning.set('performerId', inviter._id);
    //   newEarning.set('userId', member._id);
    //   newEarning.set('transactionId', null);
    //   newEarning.set('orderId', null);
    //   newEarning.set('type', 'referral');
    //   newEarning.set(
    //     'sourceType',
    //     memberRoles === 'user' ? 'invite_user' : 'invite_model'
    //   );
    //   newEarning.set('createdAt', new Date());
    //   newEarning.set('isPaid', false);
    //   newEarning.set('status', 'success');
    //   newEarning.set('transactionStatus', 'success');
    //   newEarning.set('paymentMethod', PAYMENT_GATEWAY.WALLET);
    //   newEarning.set('paymentStatus', 'success');
    //   newEarning.set('payoutStatus', 'pending');

    //   await newEarning.save();
    //   await this.performerService.increaseBalance(inviter._id, reward);
    //   await this.performerService.updateStats(inviter._id, {
    //     'stats.totalTokenEarned': reward
    //   });

    //   memberRoles === 'user'
    //     ? await this.userService.updatePaidFlatFeeForInviterStatus(
    //       member._id,
    //       true
    //     )
    //     : await this.performerService.updatePaidFlatFeeForInviterStatus(
    //       member._id,
    //       true
    //     );
    // } catch (error) {
    //   const err = await Promise.resolve(error);
    //   // eslint-disable-next-line no-console
    //   console.log('Referral channel for earning error', err);
    // }
  }
}
