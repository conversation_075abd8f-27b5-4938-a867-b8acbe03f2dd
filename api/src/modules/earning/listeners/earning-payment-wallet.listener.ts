import { QueueEvent, QueueEventService } from 'src/kernel';
import { Injectable } from '@nestjs/common';
import { UserService } from 'src/modules/user/services';
import { Model } from 'mongoose';
import { SocketUserService } from 'src/modules/socket/services/socket-user.service';
import { PerformerService } from 'src/modules/performer/services';
import { EVENT } from 'src/kernel/constants';
import { ConversationService } from 'src/modules/message/services';
import { MESSAGE_TYPE } from 'src/modules/message/constants';
import { UserDto } from 'src/modules/user/dtos';
import { v4 as uuidv4 } from 'uuid';
import {
  PAYMENT_GATEWAY,
  PAYMENT_STATUS, PAYMENT_TYPE, PAYMENT_WALLET_CHANNEL, PRODUCT_TYPE
} from 'src/modules/payment/constants';
import { STATUSES } from 'src/modules/payout-request/constants';
import { InjectModel } from '@nestjs/mongoose';
import { DBLoggerService } from 'src/modules/logger';
import { SettingService } from 'src/modules/settings';
import { ReferralService } from 'src/modules/referral/services/referral.service';
import { RECEIVED_PAID_TOKEN_EVENT } from '../constants';
import { Earning } from '../schemas/earning.schema';
import { EarningService } from '../services/earning.service';

const TIPPED = 'TIPPED';
const EARNING_WALLET_ORDER_TOPIC = 'EARNING_WALLET_ORDER_TOPIC';

@Injectable()
export class EarningPaymentWalletListener {
  constructor(
    @InjectModel(Earning.name) private readonly EarningModel: Model<Earning>,
    private readonly userService: UserService,
    private readonly performerService: PerformerService,
    private readonly queueEventService: QueueEventService,
    private readonly socketUserService: SocketUserService,
    private readonly conversationService: ConversationService,
    private readonly logger: DBLoggerService,
    private readonly settingService: SettingService,
    private readonly earningService: EarningService,
    private readonly referralService:ReferralService

  ) {
    this.queueEventService.subscribe(
      PAYMENT_WALLET_CHANNEL,
      EARNING_WALLET_ORDER_TOPIC,
      this.handler.bind(this)
    );
  }

  async handler(event: QueueEvent) {
    const { eventName } = event;
    const {
      order, orderDetail, conversationId, transactionId
    } = event.data;
    try {
      if (eventName !== EVENT.CREATED) {
        return;
      }

      const performerId = order.sellerId;
      const userId = order.buyerId;
      const { type } = order;
      const [user, performer] = await Promise.all([
        this.userService.findById(userId),
        this.performerService.findById(performerId)
      ]);

      if (!user || !performer) return;

      let commission = 0.2;
      let sourceType = 'n/a';
      const defaultCommission = 0.2;
      const performerCommission = await this.performerService.getMyCommissions(performerId);

      switch (type) {
        case PRODUCT_TYPE.PRIVATE_CHAT:
          commission = performerCommission.privateChatCommission || defaultCommission;
          sourceType = 'stream_private';
          break;
        case PRODUCT_TYPE.TIP:
          commission = performerCommission.tokenTipCommission || defaultCommission;
          sourceType = 'tip';
          break;
        default:
          break;
      }

      await this.createEarning({
        orderDetail,
        commission,
        transactionId,
        sourceType
      });

      if (type === PAYMENT_TYPE.TIP) {
        await this.notifyTip({
          order,
          user,
          performer,
          conversationId
        });
      }

      if (conversationId && ['stream_private', 'stream_group'].includes(type)) {
        const netPrice = ((orderDetail.totalPrice - (orderDetail.totalPrice * commission)) * 100) / 100;
        await this.notifyStreamChat({
          conversationId,
          performerId,
          netPrice
        });
      }

      // DISABLED: Referral reward system - only tracking signups now, no commission rewards
      // check referral for inviter
      // only performer - performer
      // if (performer.invitedById) {
      //   const [inviterIsPerformer] = await Promise.all([
      //     this.performerService.findById(performer.invitedById)
      //   ]);
      //   if (inviterIsPerformer) {
      //     await this.earningService.createReferralEarning(
      //       order,
      //       transactionId,
      //       user, // buyer
      //       performer, // seller
      //       inviterIsPerformer, // inviter
      //       performer, // invited member
      //       'performer' // invited member role
      //     );
      //   }
      // }
    } catch (e) {
      this.logger.error(e.stack || e, { context: 'EarningPaymentWalletListener' });
    }
  }

  private async createEarning({
    orderDetail,
    commission,
    transactionId,
    sourceType
  }) {
    const netPrice = ((orderDetail.totalPrice - (orderDetail.totalPrice * commission)) * 100) / 100;
    // eslint-disable-next-line new-cap
    const newEarning = new this.EarningModel();
    newEarning.set('commission', commission);
    newEarning.set('grossPrice', orderDetail.totalPrice);
    newEarning.set('netPrice', netPrice);
    newEarning.set('performerId', orderDetail.sellerId);
    newEarning.set('userId', orderDetail.buyerId);
    newEarning.set('transactionId', transactionId);
    newEarning.set('orderId', orderDetail._id);
    newEarning.set('type', orderDetail.productType);
    newEarning.set('sourceType', sourceType);
    newEarning.set('createdAt', orderDetail.createdAt);
    newEarning.set('isPaid', false);
    newEarning.set('transactionStatus', PAYMENT_STATUS.SUCCESS);
    newEarning.set('paymentMethod', PAYMENT_GATEWAY.WALLET);
    newEarning.set('paymentStatus', orderDetail.paymentStatus);
    newEarning.set('payoutStatus', STATUSES.PENDING);

    try {
      // update model and user username
      const [user, performer] = await Promise.all([
        this.userService.findById(orderDetail.buyerId),
        this.performerService.findById(orderDetail.sellerId)
      ]);
      newEarning.set('userUsername', user?.username);
      newEarning.set('performerUsername', performer?.username);
    } catch {
      newEarning.set('userUsername', 'N/A');
      newEarning.set('performerUsername', 'N/A');
    }

    // eslint-disable-next-line no-await-in-loop
    await newEarning.save();
    await this.performerService.increaseBalance(orderDetail.sellerId, netPrice);
  }

  private async notifyTip({
    order,
    user,
    performer,
    conversationId
  }) {
    try {
      const senderInfo = new UserDto(user).toResponse();
      await this.socketUserService.emitToUsers(performer._id, TIPPED, {
        _id: order._id,
        senderInfo,
        sourceId: user._id,
        targetId: performer._id,
        createdAt: new Date(),
        totalPrice: order.totalPrice.toFixed(2)
      });
      if (conversationId) {
        const conversation = await this.conversationService.findById(
          conversationId
        );
        if (!conversationId) {
          return;
        }

        const message = {
          _id: uuidv4(),
          senderInfo,
          conversationId,
          text: `has tipped $${order.totalPrice.toFixed(2)}`,
          type: MESSAGE_TYPE.TIP
        };
        const roomName = conversation.getRoomName();
        await this.socketUserService.emitToRoom(
          roomName,
          `message_created_conversation_${conversation._id}`,
          message
        );
      }
    } catch (e) {
      this.logger.error(e.stack || e, { context: 'EarningPaymentWalletListener' });
    }
  }

  private async notifyStreamChat({
    conversationId,
    performerId,
    netPrice
  }) {
    await this.socketUserService.emitToUsers(
      performerId,
      RECEIVED_PAID_TOKEN_EVENT,
      { token: netPrice, conversationId }
    );
  }
}
