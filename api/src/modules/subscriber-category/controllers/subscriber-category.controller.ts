import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
  UsePipes,
  ValidationPipe,
  Query
} from '@nestjs/common';

import { Roles } from 'src/modules/auth';
import { RoleGuard } from 'src/modules/auth/guards';
import { CurrentUser } from 'src/modules/auth';
import { DataResponse } from 'src/kernel';
import { PerformerDto } from '../../performer/dtos/performer.dto';
import { SubscriberCategoryService } from '../services/subscriber-category.service';
import { CreateSubscriberCategoryDto } from '../dto/create-subscriber-category.dto';
import { UpdateSubscriberCategoryDto } from '../dto/update-subscriber-category.dto';

@Controller('subscriber-category')
export class SubscriberCategoryController {
  constructor(
    private readonly subscriberCategoryService: SubscriberCategoryService
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @Roles('performer')
  @UseGuards(RoleGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async create(
    @Body() createSubscriberCategoryDto: CreateSubscriberCategoryDto,
    @CurrentUser() performer: PerformerDto
  ): Promise<DataResponse<any>> {
    const data = await this.subscriberCategoryService.create({
      ...createSubscriberCategoryDto,
      creatorId: performer._id
    });
    return DataResponse.ok(data);
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async findAll(): Promise<DataResponse<any>> {
    const data = await this.subscriberCategoryService.findAll();
    return DataResponse.ok(data);
  }

  @Get('/my-categories')
  @HttpCode(HttpStatus.OK)
  @Roles('performer')
  @UseGuards(RoleGuard)
  async findMyCategories(
    @CurrentUser() performer: PerformerDto,
    @Query('includeAvatars') includeAvatars?: boolean
  ): Promise<DataResponse<any>> {
    const categories = await this.subscriberCategoryService.findByCreatorId(
      performer._id
    );
    const categoriesWithStats =
      await this.subscriberCategoryService.getSubscribersByCategories(
        categories,
        includeAvatars
      );

    return DataResponse.ok(categoriesWithStats);
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async findOne(@Param('id') id: string): Promise<DataResponse<any>> {
    const data = await this.subscriberCategoryService.findOne(id);
    return DataResponse.ok(data);
  }

  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @Roles('performer')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async update(
    @Param('id') id: string,
    @Body() updateSubscriberCategoryDto: UpdateSubscriberCategoryDto
  ): Promise<DataResponse<any>> {
    const data = await this.subscriberCategoryService.update(
      id,
      updateSubscriberCategoryDto
    );
    return DataResponse.ok(data);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @Roles('performer')
  async remove(@Param('id') id: string): Promise<DataResponse<any>> {
    const data = await this.subscriberCategoryService.remove(id);
    return DataResponse.ok(data);
  }
}
