import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { SubscriberCategoryController } from './controllers/subscriber-category.controller';
import { SubscriberCategoryService } from './services/subscriber-category.service';
import {
  SubscriberCategory,
  SubscriberCategorySchema
} from './schemas/subscriber-category.schema';
import { AuthModule } from '../auth/auth.module';
import { SubscriptionModule } from '../subscription/subscription.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: SubscriberCategory.name,
        schema: SubscriberCategorySchema
      }
    ]),
    forwardRef(() => AuthModule),
    forwardRef(() => SubscriptionModule)
  ],
  controllers: [SubscriberCategoryController],
  providers: [SubscriberCategoryService],
  exports: [SubscriberCategoryService]
})
export class SubscriberCategoryModule {}
