import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type SubscriberCategoryDocument = SubscriberCategory & Document;

@Schema({
  collection: 'subscriber_categories'
})
export class SubscriberCategory {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    auto: true
  })
  _id: MongooseSchema.Types.ObjectId;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: true
  })
  creatorId: MongooseSchema.Types.ObjectId;

  @Prop({
    type: String,
    required: true,
    trim: true
  })
  name: string;

  @Prop({
    type: String,
    trim: true
  })
  description?: string;

  @Prop({
    type: Boolean,
    default: true
  })
  isActive: boolean;

  @Prop({
    type: Date,
    default: Date.now
  })
  createdAt: Date;

  @Prop({
    type: Date,
    default: Date.now
  })
  updatedAt: Date;
}

export const SubscriberCategorySchema =
  SchemaFactory.createForClass(SubscriberCategory);

SubscriberCategorySchema.index({ creatorId: 1 });
