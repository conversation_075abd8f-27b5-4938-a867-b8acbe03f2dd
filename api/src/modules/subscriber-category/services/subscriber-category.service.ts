import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ObjectId } from 'mongodb';

import { SubscriberCategory } from '../schemas/subscriber-category.schema';
import { CreateSubscriberCategoryDto } from '../dto/create-subscriber-category.dto';
import { UpdateSubscriberCategoryDto } from '../dto/update-subscriber-category.dto';
import { SubscriptionService } from 'src/modules/subscription/services/subscription.service';

interface CategorySubscriberInfo {
  category: SubscriberCategory;
  subscriberCount: number;
  subscribers?: {
    id: string;
    avatar?: string;
  }[];
}

@Injectable()
export class SubscriberCategoryService {
  constructor(
    @InjectModel(SubscriberCategory.name)
    private subscriberCategoryModel: Model<SubscriberCategory>,
    private subscriptionService: SubscriptionService
  ) {}

  async create(
    createSubscriberCategoryDto: CreateSubscriberCategoryDto
  ): Promise<SubscriberCategory> {
    const createdCategory = new this.subscriberCategoryModel(
      createSubscriberCategoryDto
    );
    return createdCategory.save();
  }

  async findAll(): Promise<SubscriberCategory[]> {
    return this.subscriberCategoryModel.find().exec();
  }

  async findOne(id: string): Promise<SubscriberCategory> {
    const category = await this.subscriberCategoryModel.findById(id).exec();
    if (!category) {
      throw new NotFoundException(
        `Subscriber category with ID ${id} not found`
      );
    }
    return category;
  }

  async update(
    id: string,
    updateSubscriberCategoryDto: UpdateSubscriberCategoryDto
  ): Promise<SubscriberCategory> {
    const updatedCategory = await this.subscriberCategoryModel
      .findByIdAndUpdate(
        id,
        { ...updateSubscriberCategoryDto, updatedAt: new Date() },
        { new: true }
      )
      .exec();

    if (!updatedCategory) {
      throw new NotFoundException(
        `Subscriber category with ID ${id} not found`
      );
    }
    return updatedCategory;
  }

  async remove(id: string): Promise<void> {
    const result = await this.subscriberCategoryModel
      .findByIdAndDelete(id)
      .exec();
    if (!result) {
      throw new NotFoundException(
        `Subscriber category with ID ${id} not found`
      );
    }
  }

  async findByCreatorId(creatorId: ObjectId): Promise<SubscriberCategory[]> {
    return this.subscriberCategoryModel
      .find({ creatorId, isActive: true })
      .sort({ updatedAt: -1 })
      .exec();
  }

  async getSubscribersByCategories(
    categories: SubscriberCategory[],
    includeAvatars: boolean = false
  ): Promise<CategorySubscriberInfo[]> {
    const results: CategorySubscriberInfo[] = [];

    for (const category of categories) {
      // Get subscribers from subscription service
      const subscribers =
        await this.subscriptionService.getSubscriptionsByCategory(
          category._id.toString()
        );

      const subscriberInfo: CategorySubscriberInfo = {
        category,
        subscriberCount: subscribers.length
      };

      // Include subscriber details if avatars are requested
      if (includeAvatars) {
        subscriberInfo.subscribers = subscribers.map((sub) => ({
          id: sub.userId.toString(),
          avatar: sub.userInfo?.avatar
        }));
      }

      results.push(subscriberInfo);
    }

    return results;
  }
}
