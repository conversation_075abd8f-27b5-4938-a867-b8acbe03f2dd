import { Injectable, Logger } from '@nestjs/common';
import { EVENT } from 'src/kernel/constants';
import { NOTIFICATION_CHANNEL } from '../notification.constant';
import { NotificationService } from '../services';
import { QueueEvent, QueueEventService } from 'src/kernel';
import { FileDto } from 'src/modules/file';


const HANDLE_SEND_TIP_FEED_NOTIFICATION = 'HANDLE_SEND_TIP_FEED_NOTIFICATION';
const SEND_TIP_FEED_CHANNEL = "SEND_TIP_FEED_CHANNEL";
@Injectable()
export class SendTipFeedNotificationListener {
    private logger = new Logger(SendTipFeedNotificationListener.name);
    constructor(
        private readonly notificationService: NotificationService,
        private readonly queueEventService: QueueEventService,
    ) {
        this.queueEventService.subscribe(
            SEND_TIP_FEED_CHANNEL,
            HANDLE_SEND_TIP_FEED_NOTIFICATION,
            this.handler.bind(this)
        );
    }

    private async getAvatarUrl(avatarPath: string): Promise<string> {
        return FileDto.getPublicUrl(avatarPath);
    }
    async handler(event: QueueEvent) {
        try {
            const { eventName } = event;
            if (eventName !== EVENT.CREATED) return;
            const { order, user } = event.data;
            const avatarUrl = await this.getAvatarUrl(user?.avatarPath);
            const title = `You received a ${order.totalPrice}$ tip from ${user.username}`;
            const notification = await this.notificationService.create({
                type: 'tip',
                action: 'created',
                userId: order.sellerId,
                refId: null,
                createdBy: order.buyerId,
                title,
                message: '',
                thumbnail: avatarUrl
            });
            await this.queueEventService.publish({
                eventName: EVENT.CREATED,
                channel: NOTIFICATION_CHANNEL,
                data: notification
            });
        } catch (e) {
            this.logger.error(e);
        }
    }
}