import {
  IsString,
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  IsDateString,
  IsMongoId
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { IsFutureDate } from './message-create.payload';

export class MassMessagePayload {
  @ApiProperty()
  @IsString()
  @IsOptional()
  message: string;

  @ApiProperty()
  @IsArray()
  @IsNotEmpty()
  recipientIds: string[];

  @ApiProperty()
  @IsArray()
  @IsOptional()
  fileIds?: string[];

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  price?: number;

  @ApiProperty({ required: false })
  @IsDateString()
  @IsFutureDate()
  @IsOptional()
  scheduledFor?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsMongoId()
  @IsOptional()
  teaserId?: string;
}
