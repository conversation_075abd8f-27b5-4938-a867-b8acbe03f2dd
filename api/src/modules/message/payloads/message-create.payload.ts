import {
  IsString,
  IsOptional,
  ValidateIf,
  IsNumber,
  IsArray,
  IsMongoId,
  ArrayMaxSize,
  IsDateString,
  ValidationOptions,
  registerDecorator,
  ValidationArguments
} from 'class-validator';

import { MESSAGE_TYPE } from '../constants';

export class MessageCreatePayload {
  @IsString()
  @IsOptional()
  type = MESSAGE_TYPE.TEXT;

  @IsNumber()
  @IsOptional()
  price: number;

  @ValidateIf((o) => o.type === MESSAGE_TYPE.TEXT)
  text?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsMongoId({ each: true })
  @ArrayMaxSize(20)
  @ValidateIf((o) => !!o.fileId)
  fileId: string[];

  @IsOptional()
  @IsDateString()
  @IsFutureDate()
  scheduledFor?: string;

  @IsOptional()
  @IsString()
  @IsMongoId()
  teaserId?: string;
}

export function IsFutureDate(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isFutureDate',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value) return true; // Skip if not provided (use with @IsOptional)
          const date = new Date(value);
          return (
            date instanceof Date && !isNaN(date.getTime()) && date > new Date()
          );
        },
        defaultMessage(args: ValidationArguments) {
          return 'Scheduled date must be in the future';
        }
      }
    });
  };
}
