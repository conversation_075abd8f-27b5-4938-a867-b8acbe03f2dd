import { IsString, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>N<PERSON><PERSON> } from 'class-validator';

import { ObjectId } from 'mongodb';
import { MessageCreatePayload } from './message-create.payload';

export class PrivateMessageCreatePayload extends MessageCreatePayload {
  @IsNotEmpty()
  @IsString()
  // @IsMongoId()
  recipientId: string | ObjectId;

  @IsNotEmpty()
  @IsString()
  recipientType: string;

  @IsOptional()
  @IsNumber()
  price: number;
}
