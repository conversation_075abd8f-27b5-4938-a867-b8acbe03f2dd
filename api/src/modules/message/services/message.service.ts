import {
  Injectable,
  ForbiddenException,
  HttpException,
  Inject,
  forwardRef
} from '@nestjs/common';
import { Model } from 'mongoose';
import { ObjectId } from 'mongodb';
import {
  QueueEventService,
  EntityNotFoundException,
  PageableData,
  StringHelper
} from 'src/kernel';
import { UserDto } from 'src/modules/user/dtos';
import { FileDto } from 'src/modules/file';
import { FileService } from 'src/modules/file/services';
import { REF_TYPE } from 'src/modules/file/constants';
import { UserService } from 'src/modules/user/services';
import { PerformerService } from 'src/modules/performer/services';
import { uniq } from 'lodash';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';

import { CheckPaymentService } from 'src/modules/payment/services';
import { MessageCreatePayload } from '../payloads/message-create.payload';
import {
  CONVERSATION_TYPE,
  MESSAGE_CHANNEL,
  MESSAGE_EVENT,
  MESSAGE_PRIVATE_STREAM_CHANNEL,
  MESSAGE_TYPE,
  PAID_MESSAGE_CHANNEL
} from '../constants';
import { MessageDto } from '../dtos';
import { ConversationService, IRecipient } from './conversation.service';
import { MessageListRequest } from '../payloads/message-list.payload';
import { Conversation, Message } from '../schemas';
import { ScheduledTaskService } from 'src/modules/scheduled-task/services/scheduled-task.service';

export interface IMassMessagePayload {
  recipientIds: string[];
  message: string;
  performerId: string | ObjectId;
  fileIds?: string[];
  price?: number;
  scheduledFor?: string;
  teaserId?: string;
}

@Injectable()
export class MessageService {
  constructor(
    @InjectModel(Message.name) private readonly MessageModel: Model<Message>,
    @InjectModel(Conversation.name)
    private readonly ConversationModel: Model<Conversation>,
    private readonly queueEventService: QueueEventService,
    private readonly fileService: FileService,
    private readonly conversationService: ConversationService,
    private readonly userService: UserService,
    private readonly performerService: PerformerService,
    @Inject(forwardRef(() => CheckPaymentService))
    private readonly checkPaymentService: CheckPaymentService,
    @Inject(forwardRef(() => ScheduledTaskService))
    private readonly scheduledTaskService: ScheduledTaskService
  ) {}

  public async findById(id: string | ObjectId): Promise<MessageDto> {
    const message = await this.MessageModel.findById(id);
    return MessageDto.fromModel(message);
  }

  public async updateBoughtMessage(id: string | ObjectId, isBought: boolean) {
    await this.MessageModel.updateOne({ _id: id }, { $set: { isBought } });
  }

  public async createPrivateMessage(
    conversationId: string | ObjectId,
    payload: MessageCreatePayload,
    sender: IRecipient
  ): Promise<MessageDto> {
    const conversation =
      await this.conversationService.findById(conversationId);
    if (!conversation) {
      throw new EntityNotFoundException();
    }
    const found = conversation.recipients.find(
      (recipient) =>
        recipient.sourceId.toString() === sender.sourceId.toString()
    );
    if (!found) {
      throw new EntityNotFoundException();
    }

    let fileIds = [];
    if (payload.fileId) {
      fileIds = Array.isArray(payload.fileId)
        ? payload.fileId
        : [payload.fileId];
    }

    let type = payload.type || 'text';
    if (payload.text === '' && fileIds.length) {
      type = MESSAGE_TYPE.SEND_FILE;
    }
    if (payload.text && fileIds.length) {
      type = MESSAGE_TYPE.SEND_FILE_TEXT;
    }
    const isSale = payload.price > 0 && type !== 'text';
    const message = await this.MessageModel.create({
      ...payload,
      isScheduled: !!payload.scheduledFor,
      type,
      text: payload.text || '',
      isSale,
      senderId: sender.sourceId,
      senderSource: sender.source,
      conversationId: conversation._id,
      createdAt: payload.scheduledFor || new Date(),
      updatedAt: payload.scheduledFor || new Date()
    });

    const dto = plainToInstance(MessageDto, message.toObject());
    if (payload.scheduledFor) {
      const scheduledDate = new Date(payload.scheduledFor);

      await this.scheduledTaskService.create({
        scheduledAt: scheduledDate,
        sourceId: message._id,
        type: 'message'
      });
    }

    // load files data if any
    if (message.fileId?.length) {
      const files = await this.fileService.findByIds(message.fileId);

      dto.files = files.map((file) => file.toPublicResponse(!isSale));
      dto.imageUrl = files.map((file) => file.getThumbnail()).join(', ');
    }

    if (message.teaserId) {
      const teaserFile = await this.fileService.findById(message.teaserId);
      if (teaserFile) {
        dto.teaserUrl = teaserFile.getUrl(true);
      }
    }

    if (!payload.scheduledFor) {
      await this.queueEventService.publish({
        channel: MESSAGE_CHANNEL,
        eventName: MESSAGE_EVENT.CREATED,
        data: dto
      });
    }

    return dto;
  }

  public async createPrivateFileMessage(
    sender: IRecipient,
    recipient: IRecipient,
    files: FileDto[],
    payload: MessageCreatePayload
  ): Promise<MessageDto> {
    const conversation =
      await this.conversationService.createPrivateConversation(
        sender,
        recipient
      );
    if (!files || !files.length)
      throw new HttpException('Files are required!', 400);
    const fileIds = files.map((file) => file._id);
    const isSale = payload.price > 0;
    const message = await this.MessageModel.create({
      ...payload,
      isScheduled: !!payload.scheduledFor,
      type: files.some((file) => file.isVideo())
        ? MESSAGE_TYPE.VIDEO
        : MESSAGE_TYPE.PHOTO,
      senderId: sender.sourceId,
      fileId: fileIds,
      senderSource: sender.source,
      conversationId: conversation._id,
      isSale,
      price: payload.price,
      createdAt: payload.scheduledFor || new Date(),
      updatedAt: payload.scheduledFor || new Date()
    });

    if (payload.scheduledFor) {
      const scheduledDate = new Date(payload.scheduledFor);

      await this.scheduledTaskService.create({
        scheduledAt: scheduledDate,
        sourceId: message._id,
        type: 'message'
      });
    }

    await Promise.all(
      files.map((file) =>
        this.fileService.addRef(file._id, {
          itemType: REF_TYPE.MESSAGE,
          itemId: message._id
        })
      )
    );

    const dto = plainToInstance(MessageDto, message.toObject());
    dto.imageUrl = files.map((file) => `${file.getUrl(!isSale)}`).join(', ');

    if (payload.teaserId) {
      const teaserFile = await this.fileService.findById(payload.teaserId);
      if (teaserFile) {
        dto.teaserUrl = teaserFile.getUrl(true);
      }
    }

    if (isSale) {
      message.ok = false;
      await message.save();
      await Promise.all(
        files.map((file) =>
          this.fileService.queueProcessPhoto(file._id, {
            meta: { id: message._id },
            thumbnailSize: { width: 250, blur: 15 },
            publishChannel: PAID_MESSAGE_CHANNEL
          })
        )
      );
    }

    dto.files = files.map((file) => file.toPublicResponse(!isSale));

    if (!payload.scheduledFor) {
      await this.queueEventService.publish({
        channel: MESSAGE_CHANNEL,
        eventName: MESSAGE_EVENT.CREATED,
        data: dto
      });
    }

    return dto;
  }

  public async createPrivateFileMessage2(
    conversationId: string | ObjectId,
    senderId: string | ObjectId,
    file: FileDto
  ): Promise<any> {
    const hasPermission = await this.conversationService.hasMessagePermission(
      conversationId,
      senderId
    );
    if (!hasPermission) {
      await this.fileService.remove(file._id);
      throw new ForbiddenException();
    }

    if (!file) throw new HttpException('File is valid!', 400);
    // support photo and video only
    if (!file.isImage() && !file.isVideo()) {
      await this.fileService.remove(file._id);
      throw new HttpException('Invalid image!', 400);
    }
    // process file immediately?
    if (file.isImage()) await this.fileService.queueProcessPhoto(file._id);
    else if (file.isVideo()) await this.fileService.queueProcessVideo(file._id);
    return file.toPublicResponse();
  }

  public async loadMessages(
    req: MessageListRequest,
    user: UserDto
  ): Promise<PageableData<MessageDto>> {
    const conversation = await this.conversationService.findById(
      req.conversationId
    );
    if (!conversation) {
      throw new EntityNotFoundException();
    }

    const found = conversation.recipients.find(
      (recipient) => recipient.sourceId.toString() === user._id.toString()
    );
    if (!found) {
      throw new EntityNotFoundException();
    }

    const query = { conversationId: conversation._id };
    const [data, total] = await Promise.all([
      this.MessageModel.find(query)
        .sort({ createdAt: -1 })
        .lean()
        .limit(req.limit ? Number(req.limit) : 10)
        .skip(Number(req.offset)),
      this.MessageModel.countDocuments(query)
    ]);

    const fileIds = data.flatMap((d) => d.fileId);
    const teaserIds = data.filter((d) => d.teaserId).map((d) => d.teaserId);
    const senderIds = uniq(data.map((d) => d.senderId));
    const [files, teaserFiles] = await Promise.all([
      this.fileService.findByIds(fileIds),
      this.fileService.findByIds(teaserIds)
    ]);
    const senders = await Promise.all([
      this.userService.findByIds(senderIds),
      this.performerService.findByIds(senderIds)
    ]);
    const productIds = data
      .filter((message) => message.isSale)
      .map((message) => message._id);
    const transactions =
      await this.checkPaymentService.findPaidProductsByBuyerId(
        user._id,
        productIds
      );

    const messages = data.map((m) => {
      const message = MessageDto.fromModel(m);
      if (message.isSale) {
        const transaction = transactions.find((t) =>
          t.productId.equals(message._id)
        );
        message.isBought = !!transaction;
      }

      // if (
      //   message.isSale
      //   && message.type === 'text'
      //   && !message.isBought
      // ) {
      //   const isOwner = user?.isPerformer
      //     && String(user._id) === String(message.senderId);
      //   if (!isOwner) {
      //     message.text = 'Unlock To View';
      //   }
      // }

      if (message.fileId && message.fileId.length) {
        const messageFiles = files.filter((f) =>
          message.fileId.some((id) => id.toString() === f._id.toString())
        );

        // Set file status information
        message.files = messageFiles.map((file) =>
          file.toPublicResponse(
            !(
              message.isSale &&
              !(message.isBought || message.senderId.equals(user._id))
            )
          )
        );

        message.imageUrl = messageFiles
          .map((file) =>
            message.isSale &&
            !(message.isBought || message.senderId.equals(user._id))
              ? file.getThumbnail()
              : file.getUrl(true)
          )
          .join(', ');
      }

      if (message.teaserId) {
        const teaserFile = teaserFiles.find(
          (f) => f._id.toString() === message.teaserId.toString()
        );
        if (teaserFile) {
          message.teaserUrl = teaserFile.getUrl(true);
        }
      }

      const senderInfo =
        message.senderSource === 'user'
          ? senders[0].find((u) => u._id.equals(message.senderId))
          : senders[1].find((p) => p._id.equals(message.senderId));
      message.setSenderInfo(senderInfo);
      return message;
    });

    return { data: messages, total };
  }

  public async deleteMessage(
    messageId: string,
    user: UserDto
  ): Promise<MessageDto> {
    const message = await this.MessageModel.findById(messageId);
    if (!message) {
      throw new EntityNotFoundException();
    }
    if (
      user.roles &&
      !user.roles.includes('admin') &&
      message.senderId.toString() !== user._id.toString()
    ) {
      throw new ForbiddenException();
    }

    // Get the conversation before deleting the message
    const conversation = await this.ConversationModel.findById(
      message.conversationId
    );
    if (!conversation) {
      throw new EntityNotFoundException('Conversation not found');
    }

    await message.deleteOne();
    if (message.type === MESSAGE_TYPE.PHOTO) {
      message.fileId && (await this.fileService.remove(message.fileId));
    }

    // If private conversation and deleted message was the last message
    if (
      conversation.type === CONVERSATION_TYPE.PRIVATE &&
      conversation.lastMessageCreatedAt?.getTime() ===
        message.createdAt.getTime()
    ) {
      // Find the new last message
      const lastMessage = await this.MessageModel.findOne(
        {
          conversationId: conversation._id,
          isScheduled: { $ne: true },
          createdAt: { $lt: new Date() } // only consider messages that are not scheduled for future
        },
        {},
        { sort: { createdAt: -1 } }
      );

      // Update conversation with new last message details
      await this.ConversationModel.updateOne(
        { _id: conversation._id },
        {
          $set: {
            lastMessage: lastMessage
              ? StringHelper.truncate(lastMessage.text || '', 30)
              : null,
            lastMessageCreatedAt: lastMessage ? lastMessage.createdAt : null,
            lastSenderId: lastMessage ? lastMessage.senderId : null,
            updatedAt: new Date()
          }
        }
      );
    }

    const dto = plainToInstance(MessageDto, message.toObject());

    await this.queueEventService.publish({
      channel: MESSAGE_CHANNEL,
      eventName: MESSAGE_EVENT.DELETED,
      data: dto
    });

    await this.queueEventService.publish({
      channel: MESSAGE_PRIVATE_STREAM_CHANNEL,
      eventName: MESSAGE_EVENT.DELETED,
      data: dto
    });
    return dto;
  }

  public async deleteAllMessageInConversation(
    conversationId: string,
    user: any
  ) {
    const conversation =
      await this.conversationService.findById(conversationId);
    if (!conversation) {
      throw new EntityNotFoundException();
    }
    if (
      user.isPerformer &&
      conversation.performerId.toString() !== user._id.toString()
    ) {
      throw new ForbiddenException();
    }

    await this.MessageModel.deleteMany({ conversationId: conversation._id });
    return { success: true };
  }

  public async createPublicStreamMessageFromConversation(
    conversationId: string | ObjectId,
    payload: MessageCreatePayload,
    sender: IRecipient,
    user: UserDto
  ) {
    const conversation =
      await this.conversationService.findById(conversationId);
    if (!conversation) {
      throw new EntityNotFoundException();
    }

    const message = await this.MessageModel.create({
      ...payload,
      senderId: sender.sourceId,
      senderSource: sender.source,
      conversationId: conversation._id
    });
    await message.save();

    const dto = plainToInstance(MessageDto, message.toObject());
    dto.setSenderInfo(user);
    await this.queueEventService.publish({
      channel: MESSAGE_PRIVATE_STREAM_CHANNEL,
      eventName: MESSAGE_EVENT.CREATED,
      data: dto
    });
    return dto;
  }

  public async createStreamMessageFromConversation(
    conversationId: string | ObjectId,
    payload: MessageCreatePayload,
    sender: IRecipient
  ) {
    const conversation =
      await this.conversationService.findById(conversationId);
    if (!conversation) {
      throw new EntityNotFoundException();
    }

    const found = conversation.recipients.find(
      (recipient) =>
        recipient.sourceId.toString() === sender.sourceId.toString()
    );
    if (!found) {
      throw new EntityNotFoundException();
    }

    const message = await this.MessageModel.create({
      ...payload,
      senderId: sender.sourceId,
      senderSource: sender.source,
      conversationId: conversation._id
    });
    await message.save();

    const dto = plainToInstance(MessageDto, message.toObject());
    await this.queueEventService.publish({
      channel: MESSAGE_PRIVATE_STREAM_CHANNEL,
      eventName: MESSAGE_EVENT.CREATED,
      data: dto
    });
    return dto;
  }

  public async loadPublicMessages(req: MessageListRequest) {
    const conversation = await this.conversationService.findById(
      req.conversationId
    );
    if (!conversation) {
      throw new EntityNotFoundException();
    }

    // check this for private as well
    if (conversation.type === CONVERSATION_TYPE.PRIVATE) {
      throw new ForbiddenException();
    }

    const sort = {
      [req.sortBy || 'updatedAt']: req.sort
    };

    const query = { conversationId: conversation._id };
    const [data, total] = await Promise.all([
      this.MessageModel.find(query)
        .sort(sort)
        .lean()
        .limit(Number(req.limit))
        .skip(Number(req.offset)),
      this.MessageModel.countDocuments(query)
    ]);

    const senderIds = uniq(data.map((d) => d.senderId));
    const [users, performers] = await Promise.all([
      senderIds.length ? this.userService.findByIds(senderIds) : [],
      senderIds.length ? this.performerService.findByIds(senderIds) : []
    ]);

    const messages = data.map((message) => {
      const dto = plainToInstance(MessageDto, message);
      let user = null;
      user = users.find(
        (u) => u._id.toString() === message.senderId.toString()
      );
      if (!user) {
        user = performers.find(
          (p) => p._id.toString() === message.senderId.toString()
        );
      }

      dto.setSenderInfo(user);
      return dto;
    });

    return {
      data: messages,
      total
    };
  }

  public async resetAllDataInConversation(conversationId) {
    await this.MessageModel.deleteMany({ conversationId });
  }

  public async checkAuth(req: any, user: UserDto) {
    const { query } = req;
    if (!query.id) {
      throw new ForbiddenException();
    }
    const message = await this.MessageModel.findById(query.id);
    if (!message) throw new EntityNotFoundException();
    if (
      user._id.toString() === message.senderId.toString() ||
      !message.isSale
    ) {
      return true;
    }
    if (message.isSale) {
      const transactions =
        await this.checkPaymentService.findPaidProductsByBuyerId(user._id, [
          message._id
        ]);
      if (!transactions.length) {
        throw new ForbiddenException();
      }
      return true;
    }
    throw new ForbiddenException();
  }

  public async removeFile(
    messageId: string | ObjectId,
    fileId: string | ObjectId
  ) {
    // TODO - here is not supported multi files yet
    const message = await this.MessageModel.findById(messageId);
    if (!message) return;
    if (message.text) {
      if (Array.isArray(message.fileId)) {
        await this.MessageModel.updateOne(
          { _id: messageId },
          {
            $pull: {
              fileIds: fileId
            }
          }
        );
      } else {
        await this.MessageModel.updateOne(
          { _id: messageId },
          {
            $set: {
              fileId: null
            }
          }
        );
      }
    } else if (Array.isArray(message.fileId)) {
      await this.MessageModel.updateOne(
        { _id: messageId },
        {
          $pull: {
            fileIds: fileId
          }
        }
      );
    } else {
      await this.MessageModel.updateOne(
        { _id: messageId },
        {
          $set: {
            fileId: [],
            text: 'FILE_DELETED'
          }
        }
      );
    }
  }

  public async sendMassMessage(payload: IMassMessagePayload): Promise<{
    success: number;
    failed: number;
    errors: Array<{ recipientId: string; error: string }>;
  }> {
    const {
      recipientIds,
      message,
      performerId,
      fileIds = [],
      price,
      scheduledFor,
      teaserId
    } = payload;

    const results = {
      success: 0,
      failed: 0,
      errors: [] as Array<{ recipientId: string; error: string }>
    };

    const performer = await this.performerService.findById(performerId);
    if (!performer) {
      throw new EntityNotFoundException('Performer not found');
    }

    const sender: IRecipient = {
      source: 'performer',
      sourceId: performer._id
    };

    const messagePayload: MessageCreatePayload = {
      text: message,
      type: fileIds.length ? MESSAGE_TYPE.SEND_FILE_TEXT : MESSAGE_TYPE.TEXT,
      fileId: fileIds.length ? fileIds : undefined,
      price,
      scheduledFor,
      teaserId
    };

    // Process messages in batches to prevent overwhelming the system
    const batchSize = 10;
    for (let i = 0; i < recipientIds.length; i += batchSize) {
      const batch = recipientIds.slice(i, i + batchSize);

      await Promise.all(
        batch.map(async (recipientId) => {
          try {
            const recipient: IRecipient = {
              source: 'user',
              sourceId: new ObjectId(recipientId)
            };

            // Check for existing conversation or create new one
            let conversation = await this.conversationService.findOne({
              type: CONVERSATION_TYPE.PRIVATE,
              'recipients.sourceId': {
                $all: [performer._id, new ObjectId(recipientId)]
              }
            });

            if (!conversation) {
              conversation =
                await this.conversationService.createPrivateConversation(
                  sender,
                  recipient
                );
            }

            await this.createPrivateMessage(
              conversation._id,
              messagePayload,
              sender
            );

            results.success += 1;
          } catch (error) {
            results.failed += 1;
            results.errors.push({
              recipientId,
              error: error.message || 'Unknown error occurred'
            });
          }
        })
      );

      // Add a small delay between batches to prevent overload
      if (i + batchSize < recipientIds.length) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    // Emit event for mass message completion
    await this.queueEventService.publish({
      channel: MESSAGE_CHANNEL,
      eventName: MESSAGE_EVENT.MASS_MESSAGE_SENT,
      data: {
        performerId,
        results,
        totalRecipients: recipientIds.length,
        timestamp: new Date()
      }
    });

    return results;
  }

  public async getScheduledMessagesByConversationId(
    conversationId: string | ObjectId,
    userId: string | ObjectId
  ): Promise<MessageDto[]> {
    const conversation =
      await this.conversationService.findById(conversationId);
    if (!conversation) {
      throw new EntityNotFoundException('Conversation not found');
    }

    // Check if user is part of conversation
    const found = conversation.recipients.find(
      (recipient) => recipient.sourceId.toString() === userId.toString()
    );
    if (!found) {
      throw new ForbiddenException(
        'User not authorized to access this conversation'
      );
    }

    const query = {
      conversationId: conversation._id,
      isScheduled: true,
      createdAt: { $gt: new Date() },
      senderId: userId
    };

    const messages = await this.MessageModel.find(query)
      .sort({ createdAt: 1 })
      .lean();

    const messagesDtos = await Promise.all(
      messages.map(async (message) => {
        const dto = MessageDto.fromModel(message);

        // Load files if message has attachments
        if (message.fileId?.length) {
          const files = await this.fileService.findByIds(message.fileId);
          dto.files = files.map((file) =>
            file.toPublicResponse(!message.isSale)
          );
          dto.imageUrl = files.map((file) => file.getThumbnail()).join(', ');
        }

        return dto;
      })
    );

    return messagesDtos;
  }

  public async updateScheduledMessage(
    messageId: string | ObjectId,
    userId: string | ObjectId,
    payload: { text: string }
  ): Promise<MessageDto> {
    const message = await this.MessageModel.findOne({
      _id: messageId,
      senderId: userId,
      isScheduled: true,
      createdAt: { $gt: new Date() }
    });

    if (!message) {
      throw new EntityNotFoundException('Scheduled message not found');
    }

    message.text = payload.text;
    await message.save();

    const dto = MessageDto.fromModel(message);

    // Load files if message has attachments
    if (message.fileId?.length) {
      const files = await this.fileService.findByIds(message.fileId);
      dto.files = files.map((file) => file.toPublicResponse(!message.isSale));
      dto.imageUrl = files.map((file) => file.getThumbnail()).join(', ');
    }

    return dto;
  }

  public async deleteScheduledMessage(
    messageId: string | ObjectId,
    userId: string | ObjectId
  ): Promise<boolean> {
    const message = await this.MessageModel.findOne({
      _id: messageId,
      senderId: userId,
      isScheduled: true,
      createdAt: { $gt: new Date() }
    });

    if (!message) {
      throw new EntityNotFoundException('Scheduled message not found');
    }

    await message.deleteOne();

    // If message had files, remove file references
    if (message.fileId?.length) {
      await Promise.all(
        message.fileId.map((fileId) => this.fileService.remove(fileId))
      );
    }

    return true;
  }

  public async getAllScheduledMessages(
    userId: string | ObjectId
  ): Promise<MessageDto[]> {
    const query = {
      senderId: userId,
      isScheduled: true,
      createdAt: { $gt: new Date() }
    };

    const messages = await this.MessageModel.find(query)
      .sort({ createdAt: 1 })
      .lean();

    // Get all file IDs to fetch them in bulk
    const fileIds = messages.flatMap((m) => m.fileId || []);
    const files = fileIds.length
      ? await this.fileService.findByIds(fileIds)
      : [];

    // Get all conversation IDs
    const conversationIds = messages.map((m) => m.conversationId);
    const conversations = await this.ConversationModel.find({
      _id: { $in: conversationIds }
    }).lean();

    // Get all recipient IDs from conversations
    const recipientIds = conversations.flatMap((c) =>
      c.recipients
        .filter((r) => r.sourceId.toString() !== userId.toString())
        .map((r) => r.sourceId)
    );

    // Get recipient names
    const [users, performers] = await Promise.all([
      this.userService.findByIds(recipientIds),
      this.performerService.findByIds(recipientIds)
    ]);

    const messagesDtos = messages.map((message) => {
      const dto = MessageDto.fromModel(message);

      // Add files if message has attachments
      if (message.fileId?.length) {
        const messageFiles = files.filter((file) =>
          message.fileId.some((id) => id.toString() === file._id.toString())
        );
        dto.files = messageFiles.map((file) =>
          file.toPublicResponse(!message.isSale)
        );
        dto.imageUrl = messageFiles
          .map((file) => file.getThumbnail())
          .join(', ');
      }

      // Add recipient name
      const conversation = conversations.find(
        (c) => c._id.toString() === message.conversationId.toString()
      );
      if (conversation) {
        const recipientInfo = conversation.recipients.find(
          (r) => r.sourceId.toString() !== userId.toString()
        );
        if (recipientInfo) {
          const user = users.find(
            (u) => u._id.toString() === recipientInfo.sourceId.toString()
          );
          const performer = performers.find(
            (p) => p._id.toString() === recipientInfo.sourceId.toString()
          );
          const recipient = user || performer;
          if (recipient) {
            dto.recipientName =
              recipient.name ||
              `${recipient.firstName || ''} ${recipient.lastName || ''}`.trim();
          }
        }
      }

      return dto;
    });

    return messagesDtos;
  }
}
