import { Injectable } from '@nestjs/common';
import { QueueEventService, QueueEvent } from 'src/kernel';
import { EVENT } from 'src/kernel/constants';
import {
  MEDIA_LIBRARY_CHANNEL, REF_MESSAGE_FILE
} from 'src/modules/media-library/constants';
import { MediaItemDto } from 'src/modules/media-library/dtos';
import { MessageService } from '../services';

const HANDLE_MESSAGE_FILE = 'HANDLE_MESSAGE_FILE';

@Injectable()
export class MessageMediaLibraryListener {
  constructor(
    private readonly messageService: MessageService,
    private readonly queueEventService: QueueEventService
  ) {
    this.queueEventService.subscribe(
      MEDIA_LIBRARY_CHANNEL,
      HANDLE_MESSAGE_FILE,
      this.handler.bind(this)
    );
  }

  public async handler(event: QueueEvent) {
    try {
      if (![EVENT.DELETED].includes(event.eventName)) return;

      const dto: MediaItemDto = event.data;
      const { refItems } = dto;
      // find and filter for related data
      await refItems.reduce(async (lp, refItem) => {
        await lp;

        // video
        switch (refItem.itemType) {
          case REF_MESSAGE_FILE: {
            // TODO - check and update logic here
            await this.messageService.removeFile(refItem.itemId, dto.fileId);
            break;
          }
          default:
            break;
        }

        return Promise.resolve();
      }, Promise.resolve());
    } catch (e) {
      console.log('delete vault message error', e);
    }
  }
}
