import {
  Injectable
} from '@nestjs/common';
import { Model } from 'mongoose';
import {
  QueueEventService, QueueEvent
} from 'src/kernel';
import { FILE_EVENT, FileService } from 'src/modules/file/services';
import { InjectModel } from '@nestjs/mongoose';
import { DBLoggerService } from 'src/modules/logger';
import {
  MESSAGE_CHANNEL,
  MESSAGE_EVENT,
  PAID_MESSAGE_CHANNEL
} from '../constants';
import { MessageDto } from '../dtos';
import { Message } from '../schemas';

@Injectable()
export class MessagePaidListener {
  constructor(
    @InjectModel(Message.name) private readonly MessageModel: Model<Message>,
    private readonly queueEventService: QueueEventService,
    private readonly fileService: FileService,
    private readonly logger: DBLoggerService
  ) {
    this.queueEventService.subscribe(PAID_MESSAGE_CHANNEL, 'PROCESS_PAID_MESSAGE', this.handler.bind(this));
  }

  async handler(event: QueueEvent) {
    try {
      const { eventName } = event;
      if (eventName !== FILE_EVENT.PHOTO_PROCESSED) return;
      const { id } = event.data.meta;
      const { fileId } = event.data;
      const message = await this.MessageModel.findById(id);
      if (!message) return;
      const file = await this.fileService.findById(fileId);
      const dto = MessageDto.fromModel(message);
      dto.imageUrl = file.getThumbnail();
      message.ok = true;
      await message.save();
      await this.queueEventService.publish({
        channel: MESSAGE_CHANNEL,
        eventName: MESSAGE_EVENT.CREATED,
        data: dto
      });
    } catch (e) {
      this.logger.error(e);
    }
  }
}
