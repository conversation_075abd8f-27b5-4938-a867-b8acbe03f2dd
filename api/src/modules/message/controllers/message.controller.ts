import {
  Controller,
  Injectable,
  HttpCode,
  HttpStatus,
  UseGuards,
  Request,
  Post,
  UsePipes,
  ValidationPipe,
  Body,
  ForbiddenException,
  Get,
  Query,
  Param,
  Delete,
  UseInterceptors,
  HttpException,
  Put
} from '@nestjs/common';
import { DataResponse, getConfig } from 'src/kernel';
import { AuthGuard, RoleGuard } from 'src/modules/auth/guards';
import { FilesUploaded, MultiFileUploadInterceptor } from 'src/modules/file';
import { AuthService, CurrentUser, Roles } from 'src/modules/auth';
import { UserDto } from 'src/modules/user/dtos';
import { MediaItemInterceptor } from 'src/modules/media-library/interceptors';
import { SelectedMediaObject } from 'src/modules/media-library/decorators';
import { MessageService, NotificationMessageService } from '../services';
import {
  MessageListRequest,
  MessageCreatePayload,
  PrivateMessageCreatePayload
} from '../payloads';
import { MessageDto } from '../dtos';
import { MassMessagePayload } from '../payloads/mass-message.payload';

@Injectable()
@Controller('messages')
export class MessageController {
  constructor(
    private readonly messageService: MessageService,
    private readonly notificationMessageService: NotificationMessageService,
    private readonly authService: AuthService
  ) {}

  @Post('/conversations/:conversationId')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createMessage(
    @Body() payload: MessageCreatePayload,
    @Param('conversationId') conversationId: string,
    @Request() req: any
  ): Promise<DataResponse<any>> {
    const data = await this.messageService.createPrivateMessage(
      conversationId,
      payload,
      {
        source: req.authUser.source,
        sourceId: req.authUser.sourceId
      }
    );
    return DataResponse.ok(data);
  }

  @Post('/conversations/:conversationId/files')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UseInterceptors(
    // TODO - check and support multiple files!!!
    MultiFileUploadInterceptor([
      {
        type: 'message-file',
        fieldName: 'file',
        options: {
          destination: getConfig('file').fileDir,
          uploader: null // Will be set dynamically in the interceptor
        }
      }
    ])
  )
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createPrivateFileMessage2(
    @FilesUploaded() files: Record<string, any>,
    @CurrentUser() user: UserDto,
    @Param('conversationId') conversationId: string
  ): Promise<DataResponse<MessageDto>> {
    // TODO - check if only model is allow to create
    const message = await this.messageService.createPrivateFileMessage2(
      conversationId,
      user._id,
      files.file
    );
    return DataResponse.ok(message);
  }

  @Post('/private/file-vault')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UseInterceptors(
    MediaItemInterceptor({
      inputFields: ['photoId', 'videoId'],
      includingFileInfo: true
      // multiple: true
    })
  )
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createPrivateFileMessageFromVault(
    @Body() payload: PrivateMessageCreatePayload,
    @Request() req: any,
    @SelectedMediaObject() mediaObject: Record<string, any>
  ): Promise<DataResponse<MessageDto>> {
    if (req.authUser.sourceId.toString() === payload.recipientId.toString()) {
      throw new ForbiddenException();
    }
    if (!mediaObject?.photoId.length)
      throw new HttpException('Missing photo file!', 400);
    // const photo = mediaObject.photoId[0]?.file; // just get file for vault
    const photos = Array.isArray(mediaObject.photoId)
      ? mediaObject.photoId.map((item) => item.file)
      : [mediaObject.photoId.file];

    let videos = [];
    if (Array.isArray(mediaObject.videoId)) {
      videos = mediaObject.videoId.map((item) => item.file);
    } else if (mediaObject.videoId) {
      videos = [mediaObject.videoId.file];
    }

    const files = [...photos, ...videos];

    const message = await this.messageService.createPrivateFileMessage(
      {
        source: req.authUser.source,
        sourceId: req.authUser.sourceId
      },
      {
        source: payload.recipientType,
        sourceId: payload.recipientId
      },
      files,
      payload
    );
    return DataResponse.ok(message);
  }

  @Post('/read-all/:conversationId')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async readAllMessage(
    @Param('conversationId') conversationId: string,
    @CurrentUser() user: UserDto
  ): Promise<DataResponse<MessageDto>> {
    const message =
      await this.notificationMessageService.recipientReadAllMessageInConversation(
        user,
        conversationId
      );
    return DataResponse.ok(message);
  }

  @Get('/counting-not-read-messages')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async countTotalNotReadMessage(
    @CurrentUser() user: UserDto
  ): Promise<DataResponse<any>> {
    const data = await this.notificationMessageService.countTotalNotReadMessage(
      user._id
    );
    return DataResponse.ok(data);
  }

  @Get('/conversations/:conversationId')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async loadMessages(
    @Query() req: MessageListRequest,
    @Param('conversationId') conversationId: string,
    @CurrentUser() user: UserDto
  ): Promise<DataResponse<any>> {
    req.conversationId = conversationId;
    const data = await this.messageService.loadMessages(req, user);
    return DataResponse.ok(data);
  }

  @Delete('/:messageId')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async deletePublicMessage(
    @Param('messageId') messageId: string,
    @CurrentUser() user: UserDto
  ): Promise<DataResponse<any>> {
    const data = await this.messageService.deleteMessage(messageId, user);
    return DataResponse.ok(data);
  }

  @Delete('/:conversationId/remove-all-message')
  @HttpCode(HttpStatus.OK)
  @Roles('admin', 'performer')
  @UseGuards(RoleGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async deleteAllPublicMessage(
    @Param('conversationId') conversationId: string,
    @CurrentUser() user: any
  ): Promise<DataResponse<any>> {
    const data = await this.messageService.deleteAllMessageInConversation(
      conversationId,
      user
    );
    return DataResponse.ok(data);
  }

  @Post('/stream/conversations/:conversationId')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createStreamMessage(
    @Body() payload: MessageCreatePayload,
    @Param('conversationId') conversationId: string,
    @Request() req: any
  ): Promise<DataResponse<any>> {
    const data = await this.messageService.createStreamMessageFromConversation(
      conversationId,
      payload,
      {
        source: req.authUser.source,
        sourceId: req.authUser.sourceId
      }
    );
    return DataResponse.ok(data);
  }

  @Post('/stream/public/conversations/:conversationId')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createPublicStreamMessage(
    @Body() payload: MessageCreatePayload,
    @Param('conversationId') conversationId: string,
    @Request() req: any,
    @CurrentUser() user: UserDto
  ): Promise<DataResponse<any>> {
    const data =
      await this.messageService.createPublicStreamMessageFromConversation(
        conversationId,
        payload,
        {
          source: req.authUser.source,
          sourceId: req.authUser.sourceId
        },
        user
      );
    return DataResponse.ok(data);
  }

  @Get('/conversations/public/:conversationId')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async loadPublicMessages(
    @Query() req: MessageListRequest,
    @Param('conversationId') conversationId: string
  ): Promise<DataResponse<any>> {
    req.conversationId = conversationId;
    const data = await this.messageService.loadPublicMessages(req);
    return DataResponse.ok(data);
  }

  @Get('/auth/check')
  @HttpCode(HttpStatus.OK)
  async checkAuth(@Request() req: any) {
    if (!req.query.token) throw new ForbiddenException();
    const user = await this.authService.getSourceFromJWT(req.query.token);
    if (!user) {
      throw new ForbiddenException();
    }
    const valid = await this.messageService.checkAuth(req, user);
    return DataResponse.ok(valid);
  }

  @Post('/mass-message')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @Roles('performer')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async sendMassMessage(
    @Body() payload: MassMessagePayload,
    @Request() req: any
  ): Promise<DataResponse<any>> {
    const results = await this.messageService.sendMassMessage({
      ...payload,
      performerId: req.authUser.sourceId
    });

    return DataResponse.ok(results);
  }

  @Get('/scheduled/:conversationId')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async getScheduledMessages(
    @Param('conversationId') conversationId: string,
    @Request() req: any
  ): Promise<DataResponse<MessageDto[]>> {
    const messages =
      await this.messageService.getScheduledMessagesByConversationId(
        conversationId,
        req.authUser.sourceId
      );
    return DataResponse.ok(messages);
  }

  @Put('/scheduled/:messageId')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async updateScheduledMessage(
    @Param('messageId') messageId: string,
    @Body() payload: { text: string },
    @Request() req: any
  ): Promise<DataResponse<MessageDto>> {
    const message = await this.messageService.updateScheduledMessage(
      messageId,
      req.authUser.sourceId,
      payload
    );
    return DataResponse.ok(message);
  }

  @Delete('/scheduled/:messageId')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async deleteScheduledMessage(
    @Param('messageId') messageId: string,
    @Request() req: any
  ): Promise<DataResponse<boolean>> {
    const result = await this.messageService.deleteScheduledMessage(
      messageId,
      req.authUser.sourceId
    );
    return DataResponse.ok(result);
  }

  @Get('/scheduled')
  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async getAllScheduledMessages(
    @Request() req: any
  ): Promise<DataResponse<MessageDto[]>> {
    const messages = await this.messageService.getAllScheduledMessages(
      req.authUser.sourceId
    );
    return DataResponse.ok(messages);
  }
}
