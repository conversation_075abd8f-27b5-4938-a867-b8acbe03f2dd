import { ObjectId } from 'mongodb';

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema } from 'mongoose';

@Schema({
  collection: 'messages'
})
export class Message {
  @Prop({
    type: MongooseSchema.Types.ObjectId
  })
  conversationId: ObjectId;
  // text, file, etc...

  @Prop()
  type: string;

  @Prop({
    type: MongooseSchema.Types.Mixed
  })
  fileId: ObjectId[];

  @Prop()
  price: number;

  @Prop()
  text: string;

  @Prop()
  senderSource: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId
  })
  senderId: ObjectId;

  @Prop({
    type: MongooseSchema.Types.Mixed
  })
  meta: Record<string, any>;

  @Prop({
    default: true
  })
  ok: boolean;

  @Prop({
    default: false
  })
  isSale: boolean;

  @Prop({
    default: false
  })
  isBought: boolean;

  @Prop({
    type: Date,
    default: Date.now
  })
  createdAt: Date;

  @Prop({
    type: Date,
    default: Date.now
  })
  updatedAt: Date;

  @Prop({
    default: false
  })
  isScheduled: boolean;

  @Prop({
    type: MongooseSchema.Types.ObjectId
  })
  teaserId?: ObjectId;
}

export type MessageDocument = HydratedDocument<Message>;

export const MessageSchema = SchemaFactory.createForClass(Message);

MessageSchema.index(
  { conversationId: 1 },
  {
    name: 'idx_conversationId'
  }
);

MessageSchema.index(
  { conversationId: 1, createdAt: -1 },
  {
    name: 'idx_conversationId_createdAt'
  }
);
