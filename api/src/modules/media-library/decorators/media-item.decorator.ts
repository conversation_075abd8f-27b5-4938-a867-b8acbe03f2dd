import { createParamDecorator } from '@nestjs/common';

export const SelectedMedia = createParamDecorator((data, req) => ({
  media: req.media || req.args[0].media || null,
  mediaObject: req.mediaObject || req.args[0].mediaObject || null
}));

export const SelectedMediaObject = createParamDecorator((data, req) => req.mediaObject || req.args[0].mediaObject || null);

export const SelectedMediaArray = createParamDecorator((data, req) => req.media || req.args[0].media || null);
