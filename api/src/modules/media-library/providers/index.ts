import { Connection } from 'mongoose';
import { MONGO_DB_PROVIDER } from 'src/kernel';
import { MediaItemSchema } from '../schemas';

export const MEDIA_ITEM_MODEL_PROVIDER = 'MEDIA_ITEM_MODEL_PROVIDER';

export const mediaItemProviders = [
  {
    provide: MEDIA_ITEM_MODEL_PROVIDER,
    useFactory: (connection: Connection) => connection.model('MediaItem', MediaItemSchema),
    inject: [MONGO_DB_PROVIDER]
  }
];
