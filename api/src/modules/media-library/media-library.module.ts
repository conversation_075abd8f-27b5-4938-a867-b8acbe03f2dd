import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from '../auth/auth.module';
import { MediaLibraryService } from './services';
import {
  MediaItem, MediaItemSchema
} from './schemas';
import { FileModule } from '../file/file.module';
import { PerformerMediaLibraryController } from './controllers';
import {
  FeedUpdateListener, MessageUpdateListener, PhotoUpdateListener, VideoUpdateListener
} from './listeners';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: MediaItem.name,
        schema: MediaItemSchema
      }
    ]),
    forwardRef(() => AuthModule),
    forwardRef(() => FileModule)
  ],
  providers: [
    MediaLibraryService,
    FeedUpdateListener,
    VideoUpdateListener,
    PhotoUpdateListener,
    MessageUpdateListener
  ],
  controllers: [
    PerformerMediaLibraryController
  ],
  exports: [
    MediaLibraryService
  ]
})
export class MediaLibraryModule { }
