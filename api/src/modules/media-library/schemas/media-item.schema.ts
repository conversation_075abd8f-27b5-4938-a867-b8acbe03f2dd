import { ObjectId } from 'mongodb';

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema } from 'mongoose';

@Schema({
  _id: false
})
export class FileRefItem {
  @Prop({
    type: MongooseSchema.Types.ObjectId
  })
  itemId: ObjectId;

  @Prop()
  itemType: string;
}

const FileRefItemSchema = SchemaFactory.createForClass(FileRefItem);

@Schema({
  collection: 'media_items'
})
export class MediaItem {
  @Prop()
  source: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    index: true
  })
  sourceId: ObjectId;

  @Prop()
  mediaType: string; // photo, video, etc...

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    index: true,
    unique: true
  })
  fileId: ObjectId; // ref file ID

  @Prop()
  name: string;

  @Prop()
  description: string;

  @Prop({
    type: MongooseSchema.Types.Mixed
  })
  meta: Record<string, any>;

  @Prop({
    type: [FileRefItemSchema],
    _id: false
  })
  refItems: FileRefItem[];

  @Prop({
    type: Date,
    default: Date.now
  })
  createdAt: Date;

  @Prop({
    type: Date,
    default: Date.now
  })
  updatedAt: Date;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    index: true,
    required: false
  })
  categoryId?: ObjectId;
}

export type MediaItemDocument = HydratedDocument<MediaItem>;

export const MediaItemSchema = SchemaFactory.createForClass(MediaItem);
