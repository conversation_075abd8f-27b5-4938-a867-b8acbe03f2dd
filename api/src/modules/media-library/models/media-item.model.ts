import { Document } from 'mongoose';
import { ObjectId } from 'mongodb';

export interface IRefItem {
  itemType: string;
  itemId: ObjectId;
}

export class MediaItemDocument extends Document {
  source: string;

  sourceId: ObjectId;

  mediaType: string; // photo, video, etc...

  fileId: ObjectId;

  name: string;

  description: string;

  meta: Record<string, any>;

  refItems: IRefItem[];

  createdAt: Date;

  updatedAt: Date;
}
