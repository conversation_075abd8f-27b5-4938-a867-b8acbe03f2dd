import { IsIn, IsOptional, IsString, ValidateIf } from 'class-validator';
import { SearchRequest } from 'src/kernel';

export class MediaItemSearchPayload extends SearchRequest {
  @IsString()
  @IsIn(['video', 'image'])
  @ValidateIf((o) => !!o.mediaType)
  mediaType: string;

  @IsString()
  @IsOptional()
  fromDate: string | Date;

  @IsString()
  @IsOptional()
  toDate: string | Date;

  @IsString()
  @IsOptional()
  categoryId: string;
}
