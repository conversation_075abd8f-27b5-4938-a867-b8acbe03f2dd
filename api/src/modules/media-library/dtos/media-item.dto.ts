import { ObjectId } from 'mongodb';
import { Expose, plainToInstance, Transform } from 'class-transformer';
import { FileDto } from 'src/modules/file';

interface IRefItem {
  itemType: string;
  itemId: ObjectId | any;
}

export class MediaItemDto {
  @Expose()
  @Transform(({ obj }) => obj._id)
  _id: ObjectId;

  @Expose()
  source: string;

  @Expose()
  @Transform(({ obj }) => obj.sourceId)
  sourceId: ObjectId;

  @Expose()
  mediaType: string;

  @Expose()
  @Transform(({ obj }) => obj.fileId)
  fileId: ObjectId;

  @Expose()
  name: string;

  @Expose()
  description: string;

  @Expose()
  @Transform(({ obj }) => obj.refItems)
  refItems: IRefItem[];

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;

  @Expose()
  file: Record<string, any>;

  @Expose()
  @Transform(({ obj }) => obj.categoryId)
  categoryId: ObjectId;

  public static fromModel(model) {
    if (!model) return null;
    return plainToInstance(
      MediaItemDto,
      typeof model.toObject === 'function' ? model.toObject() : model
    );
  }

  public setFile(file: FileDto) {
    if (!file) return;

    this.file = file;
  }

  public toResponse() {
    return {
      _id: this._id,
      mediaType: this.mediaType,
      fileId: this.fileId,
      categoryId: this.categoryId,
      file: this.file?.toPublicResponse(true),
      name: this.name,
      description: this.description,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  public getFile() {
    return this.file;
  }
}
