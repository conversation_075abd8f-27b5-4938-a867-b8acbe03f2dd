import { Injectable } from '@nestjs/common';
import { QueueEventService, QueueEvent } from 'src/kernel';
import { EVENT } from 'src/kernel/constants';
import { PhotoDto } from 'src/modules/performer-assets/dtos';
import { PERFORMER_COUNT_PHOTO_CHANNEL } from 'src/modules/performer-assets/services';
import { MediaLibraryService } from '../services';
import { REF_PHOTO_MAIN_FILE } from '../constants';

const UPDATE_VAULT_PHOTO_TOPIC = 'UPDATE_VAULT_PHOTO_TOPIC';

@Injectable()
export class PhotoUpdateListener {
  constructor(
    private readonly queueEventService: QueueEventService,
    private readonly mediaLibraryService: MediaLibraryService
  ) {
    this.queueEventService.subscribe(
      PERFORMER_COUNT_PHOTO_CHANNEL, // TODO - should check and rename this
      UPDATE_VAULT_PHOTO_TOPIC,
      this.handler.bind(this)
    );
  }

  public async handler(event: QueueEvent) {
    // add / remove ref if any
    switch (event.eventName) {
      case EVENT.CREATED: {
        const photo: PhotoDto = event.data;
        const { fileId } = photo;
        if (fileId) await this.mediaLibraryService.addRefByFileId(fileId, REF_PHOTO_MAIN_FILE, photo._id);

        break;
      }
      case EVENT.DELETED: {
        const photo: PhotoDto = event.data;
        const { fileId } = photo;

        if (fileId) this.mediaLibraryService.removeRefByFileId(fileId, photo._id);

        break;
      }
      default: break;
    }
  }
}
