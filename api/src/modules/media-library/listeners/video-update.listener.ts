import { Injectable } from '@nestjs/common';
import { QueueEventService, QueueEvent } from 'src/kernel';
import { EVENT } from 'src/kernel/constants';
import { VideoDto } from 'src/modules/performer-assets/dtos';
import { PERFORMER_VIDEO_CHANNEL } from 'src/modules/performer-assets/services';
import { MediaLibraryService } from '../services';
import { REF_VIDEO_MAIN_FILE, REF_VIDEO_TEASER_FILE, REF_VIDEO_THUMBNAIL_FILE } from '../constants';

const UPDATE_VAULT_VIDEO_TOPIC = 'UPDATE_VAULT_VIDEO_TOPIC';

@Injectable()
export class VideoUpdateListener {
  constructor(
    private readonly queueEventService: QueueEventService,
    private readonly mediaLibraryService: MediaLibraryService
  ) {
    this.queueEventService.subscribe(
      PERFORMER_VIDEO_CHANNEL,
      UPDATE_VAULT_VIDEO_TOPIC,
      this.handler.bind(this)
    );
  }

  public async handler(event: QueueEvent) {
    // add / remove ref if any
    switch (event.eventName) {
      case EVENT.CREATED: {
        const video: VideoDto = event.data;
        const { fileId, teaserId, thumbnailId } = video;
        if (fileId) await this.mediaLibraryService.addRefByFileId(fileId, REF_VIDEO_MAIN_FILE, video._id);
        if (teaserId) await this.mediaLibraryService.addRefByFileId(teaserId, REF_VIDEO_TEASER_FILE, video._id);
        if (thumbnailId) await this.mediaLibraryService.addRefByFileId(thumbnailId, REF_VIDEO_THUMBNAIL_FILE, video._id);

        break;
      }
      case EVENT.UPDATED: {
        const video: VideoDto = event.data;
        const oldVideo = event.data.oldData || {};
        const { fileId } = video;
        const { fileId: oldFileId } = oldVideo;

        if (fileId.toString() !== oldFileId.toString()) {
          await this.mediaLibraryService.removeRefByFileId(oldFileId, video._id);
          await this.mediaLibraryService.addRefByFileId(fileId, REF_VIDEO_MAIN_FILE, video._id);
        }

        if (oldVideo.teaserId) {
          if (!video.teaserId) {
            await this.mediaLibraryService.removeRefByFileId(oldVideo.teaserId, video._id);
          } else if (video.teaserId && video.teaserId.toString() !== oldVideo.teaserId) {
            await this.mediaLibraryService.removeRefByFileId(oldVideo.teaserId, video._id);
            await this.mediaLibraryService.addRefByFileId(video.teaserId, REF_VIDEO_TEASER_FILE, video._id);
          }
        } else if (video.teaserId) {
          await this.mediaLibraryService.addRefByFileId(video.teaserId, REF_VIDEO_TEASER_FILE, video._id);
        }

        if (oldVideo.thumbnailId) {
          if (!video.thumbnailId) {
            await this.mediaLibraryService.removeRefByFileId(oldVideo.thumbnailId, video._id);
          } else if (video.thumbnailId && video.thumbnailId.toString() !== oldVideo.thumbnailId) {
            await this.mediaLibraryService.removeRefByFileId(oldVideo.thumbnailId, video._id);
            await this.mediaLibraryService.addRefByFileId(video.thumbnailId, REF_VIDEO_THUMBNAIL_FILE, video._id);
          }
        } else if (video.thumbnailId) {
          await this.mediaLibraryService.addRefByFileId(video.thumbnailId, REF_VIDEO_THUMBNAIL_FILE, video._id);
        }

        break;
      }
      case EVENT.DELETED: {
        const video: VideoDto = event.data;
        const { fileId, teaserId, thumbnailId } = video;

        if (fileId) this.mediaLibraryService.removeRefByFileId(fileId, video._id);
        if (teaserId) this.mediaLibraryService.removeRefByFileId(teaserId, video._id);
        if (thumbnailId) this.mediaLibraryService.removeRefByFileId(thumbnailId, video._id);

        break;
      }
      default: break;
    }
  }
}
