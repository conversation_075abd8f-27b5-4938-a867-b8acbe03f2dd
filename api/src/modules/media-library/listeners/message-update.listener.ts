import { Injectable } from '@nestjs/common';
import { QueueEventService, QueueEvent } from 'src/kernel';
import { EVENT } from 'src/kernel/constants';
import { MESSAGE_CHANNEL } from 'src/modules/message/constants';
import { MessageDto } from 'src/modules/message/dtos';
import { MediaLibraryService } from '../services';
import {
  REF_MESSAGE_FILE
} from '../constants';

const UPDATE_VAULT_MESSAGE_TOPIC = 'UPDATE_VAULT_MESSAGE_TOPIC';

@Injectable()
export class MessageUpdateListener {
  constructor(
    private readonly queueEventService: QueueEventService,
    private readonly mediaLibraryService: MediaLibraryService
  ) {
    this.queueEventService.subscribe(
      MESSAGE_CHANNEL,
      UPDATE_VAULT_MESSAGE_TOPIC,
      this.handler.bind(this)
    );
  }

  public async handler(event: QueueEvent) {
    // add / remove ref if any
    switch (event.eventName) {
      case EVENT.CREATED: {
        const message: MessageDto = event.data;
        const { fileId } = message;
        if (fileId) {
          await Promise.all(
            (Array.isArray(fileId) ? fileId : [fileId])
              .map(
                (fId) => this.mediaLibraryService.addRefByFileId(fId, REF_MESSAGE_FILE, message._id)
              )
          );
        }

        break;
      }
      case EVENT.DELETED: {
        const message: MessageDto = event.data;
        const { fileId } = message;
        if (fileId) {
          const _fileIds = Array.isArray(fileId) ? fileId : [fileId];
          await Promise.all(
            _fileIds.map(
              (fId) => this.mediaLibraryService.removeRefByFileId(fId, message._id)
            )
          );
        }

        break;
      }
      default: break;
    }
  }
}
