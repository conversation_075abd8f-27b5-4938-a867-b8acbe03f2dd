import { Injectable } from '@nestjs/common';
import { QueueEventService, QueueEvent } from 'src/kernel';
import { EVENT } from 'src/kernel/constants';
import { PERFORMER_FEED_CHANNEL } from 'src/modules/feed/constants';
import { FeedDto } from 'src/modules/feed/dtos';
import { MediaLibraryService } from '../services';
import { REF_FEED_MAIN_FILE, REF_FEED_TEASER_FILE, REF_FEED_THUMBNAIL_FILE } from '../constants';

const UPDATE_VAULT_FEED_TOPIC = 'UPDATE_VAULT_FEED_TOPIC';

@Injectable()
export class FeedUpdateListener {
  constructor(
    private readonly queueEventService: QueueEventService,
    private readonly mediaLibraryService: MediaLibraryService
  ) {
    this.queueEventService.subscribe(
      PERFORMER_FEED_CHANNEL,
      UPDATE_VAULT_FEED_TOPIC,
      this.handler.bind(this)
    );
  }

  public async handler(event: QueueEvent) {
    // add / remove ref if any
    switch (event.eventName) {
      case EVENT.CREATED: {
        const feed: FeedDto = event.data;
        const { fileIds, teaserId, thumbnailId } = feed;
        if (fileIds?.length) {
          await Promise.all(fileIds.map((fileId) => this.mediaLibraryService.addRefByFileId(fileId, REF_FEED_MAIN_FILE, feed._id)));
        }
        if (teaserId) await this.mediaLibraryService.addRefByFileId(teaserId, REF_FEED_TEASER_FILE, feed._id);
        if (thumbnailId) await this.mediaLibraryService.addRefByFileId(thumbnailId, REF_FEED_TEASER_FILE, feed._id);

        break;
      }
      case EVENT.UPDATED: {
        const feed: FeedDto = event.data;
        const oldFeed = event.data.oldData || {};
        const { fileIds = [] } = feed;
        const fileIdsStr = fileIds.map((f) => f.toString());
        const { fileIds: oldFileIds = [] } = oldFeed;
        const oldFileIdsStr = oldFileIds.map((f) => f.toString());
        // filter removed items
        const removedIds = oldFileIdsStr.filter((id) => !fileIdsStr.includes(id));
        const newFileIds = fileIdsStr.filter((id) => !oldFileIdsStr.includes(id));

        if (removedIds.length) {
          await Promise.all(fileIds.map((fileId) => this.mediaLibraryService.removeRef(fileId, feed._id)));
        }

        if (newFileIds.length) {
          await Promise.all(fileIds.map((fileId) => this.mediaLibraryService.addRefByFileId(fileId, REF_FEED_MAIN_FILE, feed._id)));
        }

        if (oldFeed.teaserId) {
          if (!feed.teaserId) {
            await this.mediaLibraryService.removeRef(oldFeed.teaserId, feed._id);
          } else if (feed.teaserId && feed.teaserId.toString() !== oldFeed.teaserId) {
            await this.mediaLibraryService.removeRef(oldFeed.teaserId, feed._id);
            await this.mediaLibraryService.addRefByFileId(feed.teaserId, REF_FEED_TEASER_FILE, feed._id);
          }
        } else if (feed.teaserId) {
          await this.mediaLibraryService.addRefByFileId(feed.teaserId, REF_FEED_TEASER_FILE, feed._id);
        }

        if (oldFeed.thumbnailId) {
          if (!feed.thumbnailId) {
            await this.mediaLibraryService.removeRef(oldFeed.thumbnailId, feed._id);
          } else if (feed.thumbnailId && feed.thumbnailId.toString() !== oldFeed.thumbnailId) {
            await this.mediaLibraryService.removeRef(oldFeed.thumbnailId, feed._id);
            await this.mediaLibraryService.addRefByFileId(feed.thumbnailId, REF_FEED_THUMBNAIL_FILE, feed._id);
          }
        } else if (feed.thumbnailId) {
          await this.mediaLibraryService.addRefByFileId(feed.thumbnailId, REF_FEED_THUMBNAIL_FILE, feed._id);
        }

        break;
      }
      case EVENT.DELETED: {
        const feed: FeedDto = event.data;
        const { fileIds = [], teaserId, thumbnailId } = feed;

        await Promise.all(fileIds.map((fileId) => this.mediaLibraryService.removeRef(fileId, feed._id)));
        if (teaserId) await this.mediaLibraryService.removeRef(teaserId, feed._id);
        if (thumbnailId) await this.mediaLibraryService.removeRef(thumbnailId, feed._id);

        break;
      }
      default: break;
    }
  }
}
