import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallH<PERSON><PERSON>,
  mixin,
  Type,
  HttpException
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { Request } from 'express';
import { isObjectId } from 'src/kernel/helpers/string.helper';
import { MediaLibraryService } from '../services';

export interface IMediaItemInterceptorOptions {
  /**
   * field to get / load media item
   */
  inputFields: string | string[];
  includingFileInfo?: boolean;
}

export function MediaItemInterceptor({
  inputFields,
  includingFileInfo = false
}: IMediaItemInterceptorOptions) {
  @Injectable()
  class MixinInterceptor implements NestInterceptor {
    constructor(
      private readonly mediaLibraryService: MediaLibraryService
    ) {}

    async intercept(
      context: ExecutionContext,
      next: CallHandler
    ): Promise<Observable<any>> {
      if (!inputFields) return next.handle();

      const ctx = context.switchToHttp();
      const req = ctx.getRequest<Request | any>();
      const { body, user } = req;

      // check logged in user!
      if (!user) return next.handle();
      // TODO - handle me
      // filter body fields with input field and add validation
      const _inputFields = Array.isArray(inputFields) ? inputFields : [inputFields];
      const mediaFieldIds = _inputFields.reduce((results, inputField) => {
        if (!body[inputField]) return results;

        const _inputData = Array.isArray(body[inputField]) ? body[inputField] : [body[inputField]];

        // validate mongodb ID
        results.push(
          ..._inputData
            .filter((f) => isObjectId(f))
        );

        return results;
      }, []);

      if (!mediaFieldIds.length) return next.handle();
      const validate = await this.mediaLibraryService.validateMine(user, mediaFieldIds, {
        includingFileInfo
      });
      if (!validate.isMine) throw new HttpException('Invalid input data', 400);
      // map mediaId with the current one
      req.media = validate.media;
      req.mediaObject = _inputFields.reduce((results, inputField) => {
        if (!body[inputField]) return results;

        const _results = results;

        // issue update file
        const _inputData = Array.isArray(body[inputField]) ? body[inputField] : [body[inputField]];
        const media = validate.media.filter((m) => _inputData.includes(m._id.toString()) || _inputData.includes(m.file?._id.toString()));
        // _results[inputField] = media;

        if (!_results[inputField]) {
          _results[inputField] = media;
        } else {
          _results[inputField] = [..._results[inputField], ...media];
        }
        return _results;
      }, {} as Record<string, any>);
      return next.handle();
    }
  }

  const Interceptor = mixin(MixinInterceptor);
  return Interceptor as Type<NestInterceptor>;
}
