import {
  Controller,
  Injectable,
  HttpCode,
  HttpStatus,
  UseGuards,
  Body,
  Post,
  UsePipes,
  ValidationPipe,
  UseInterceptors,
  Put,
  Param,
  Get,
  Delete,
  Query
} from '@nestjs/common';
import { RoleGuard } from 'src/modules/auth/guards';
import { CurrentUser, Roles } from 'src/modules/auth';
import { PerformerDto } from 'src/modules/performer/dtos';
import { DataResponse, getConfig } from 'src/kernel';
import { FileDto, FileUploaded, FileUploadInterceptor } from 'src/modules/file';
import { MediaItemCreatePayload } from '../payloads';
import { MediaLibraryService } from '../services';
import { MediaItemSearchPayload } from '../payloads/media-item-search.payload';

@Injectable()
@Controller('performer/media-libraries')
export class PerformerMediaLibraryController {
  constructor(private readonly mediaLibraryService: MediaLibraryService) {}

  @Post('/upload')
  @HttpCode(HttpStatus.OK)
  @UseGuards(RoleGuard)
  @Roles('performer')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @UseInterceptors(
    FileUploadInterceptor('media', 'file', {
      destination: getConfig('file').mediaDir
    })
  )
  async selfCreate(
    @Body() payload: MediaItemCreatePayload,
    @CurrentUser() performer: PerformerDto,
    @FileUploaded() file: FileDto
  ): Promise<DataResponse<any>> {
    const data = await this.mediaLibraryService.selfCreate(
      performer,
      file,
      payload
    );
    return DataResponse.ok(data);
  }

  @Put('/:id')
  @HttpCode(HttpStatus.OK)
  @UseGuards(RoleGuard)
  @Roles('performer')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async selfUpdate(
    @Body() payload: MediaItemCreatePayload,
    @CurrentUser() performer: PerformerDto,
    @Param('id') id: string
  ): Promise<DataResponse<any>> {
    const data = await this.mediaLibraryService.selfUpdate(
      id,
      performer,
      payload
    );
    return DataResponse.ok(data);
  }

  @Get('/search')
  @HttpCode(HttpStatus.OK)
  @UseGuards(RoleGuard)
  @Roles('performer')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async myList(
    @Query() payload: MediaItemSearchPayload,
    @CurrentUser() performer: PerformerDto
  ): Promise<DataResponse<any>> {
    const data = await this.mediaLibraryService.myList(performer, payload);
    return DataResponse.ok(data);
  }

  @Get('/:id')
  @HttpCode(HttpStatus.OK)
  @UseGuards(RoleGuard)
  @Roles('performer')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async myDetails(
    @CurrentUser() performer: PerformerDto,
    @Param('id') id: string
  ): Promise<DataResponse<any>> {
    const data = await this.mediaLibraryService.myDetails(id, performer);
    return DataResponse.ok(data);
  }

  @Delete('/:id')
  @HttpCode(HttpStatus.OK)
  @UseGuards(RoleGuard)
  @Roles('performer')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async selfDelete(
    @CurrentUser() performer: PerformerDto,
    @Param('id') id: string
  ): Promise<DataResponse<any>> {
    const data = await this.mediaLibraryService.selfDelete(id, performer);
    return DataResponse.ok(data);
  }

  @Get('/by-fileId/:id')
  @HttpCode(HttpStatus.OK)
  @UseGuards(RoleGuard)
  @Roles('performer')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async getDetailsByFileId(
    @CurrentUser() performer: PerformerDto,
    @Param('id') id: string
  ): Promise<DataResponse<any>> {
    const data = await this.mediaLibraryService.myDetailsByFileId(
      id,
      performer
    );
    return DataResponse.ok(data);
  }

  @Get('/stats/media')
  @HttpCode(HttpStatus.OK)
  @UseGuards(RoleGuard)
  @Roles('performer')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async getMediaStats(
    @CurrentUser() performer: PerformerDto
  ): Promise<DataResponse<any>> {
    const data = await this.mediaLibraryService.getMyMediaStats(performer);
    return DataResponse.ok(data);
  }

  @Post('/update-category')
  @HttpCode(HttpStatus.OK)
  @UseGuards(RoleGuard)
  @Roles('performer')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async updateMediaItemsCategory(
    @Body() payload: { categoryId: string; mediaItemIds: string[] }
  ): Promise<DataResponse<any>> {
    const { categoryId, mediaItemIds } = payload;
    const data = await this.mediaLibraryService.updateMediaItemsCategory(
      categoryId,
      mediaItemIds
    );
    return DataResponse.ok(data);
  }
}
