import { HttpException, Injectable } from '@nestjs/common';
import { ObjectId } from 'mongodb';
import mongoose, { FilterQuery, Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { UserDto } from 'src/modules/user/dtos';
import { FileDto } from 'src/modules/file';
import { PerformerDto } from 'src/modules/performer/dtos';
import {
  EntityNotFoundException,
  ForbiddenException,
  QueueEventService
} from 'src/kernel';
import { FileService } from 'src/modules/file/services';
import { EVENT } from 'src/kernel/constants';
import * as moment from 'moment';
import { toObjectId } from 'src/kernel/helpers/string.helper';
import { MediaItem } from '../schemas';
import { MediaItemCreatePayload } from '../payloads';
import { MediaItemDto } from '../dtos';
import { MediaItemSearchPayload } from '../payloads/media-item-search.payload';
import { MEDIA_LIBRARY_CHANNEL } from '../constants';

export interface IValidateMineOptions {
  /**
   * load file info along with media data
   */
  includingFileInfo?: boolean;

  /**
   * if input fields are empty, throw error
   */
  errorIfEmpty?: boolean;
}

@Injectable()
export class MediaLibraryService {
  constructor(
    @InjectModel(MediaItem.name)
    private readonly MediaItemModel: Model<MediaItem>,
    private readonly fileService: FileService,
    private readonly queueEventService: QueueEventService
  ) {}

  public async findById(id: string | ObjectId) {
    const model = await this.MediaItemModel.findById(id);
    return MediaItemDto.fromModel(model);
  }

  public async selfCreate(
    user: UserDto | PerformerDto,
    file: FileDto,
    payload: MediaItemCreatePayload
  ) {
    if (!file.isImage() && !file.isVideo()) {
      await this.fileService.remove(file._id);
      throw new HttpException('Invalid photo or video file', 400);
    }

    // TODO - check if need file permission
    // TODO - check if have this file already?
    const model = await this.MediaItemModel.create({
      source: user.isPerformer ? 'performer' : 'user',
      sourceId: user._id,
      mediaType: file.isImage() ? 'image' : 'video',
      fileId: file._id,
      name: payload.name || file.name,
      description: payload.description,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    if (file.isImage()) await this.fileService.queueProcessPhoto(file._id);
    else if (file.isVideo()) await this.fileService.queueProcessVideo(file._id);

    file.status = 'processing';

    const dto = MediaItemDto.fromModel(model);
    dto.setFile(file);
    return dto.toResponse();
  }

  public async selfUpdate(
    id: string | ObjectId,
    user: UserDto | PerformerDto,
    payload: MediaItemCreatePayload
  ) {
    const model = await this.MediaItemModel.findById(id);
    if (!model) throw new EntityNotFoundException();
    if (!model.sourceId.equals(user._id)) throw new ForbiddenException();

    model.name = payload.name;
    model.description = payload.description;
    model.updatedAt = new Date();
    await model.save();

    return MediaItemDto.fromModel(model).toResponse();
  }

  public async selfDelete(id: string | ObjectId, user: UserDto | PerformerDto) {
    const model = await this.MediaItemModel.findById(id);

    if (!model) throw new EntityNotFoundException();
    if (!model.sourceId.equals(user._id)) throw new ForbiddenException();

    // delete physical files
    await this.MediaItemModel.deleteOne({ _id: model._id });
    await this.fileService.remove(model.fileId);

    const dto = MediaItemDto.fromModel(model);

    await this.queueEventService.publish({
      channel: MEDIA_LIBRARY_CHANNEL,
      eventName: EVENT.DELETED,
      data: dto
    });

    return true;
  }

  public async myList(
    user: UserDto | PerformerDto,
    searchReq: MediaItemSearchPayload
  ) {
    const sort = {
      [searchReq.sortBy || 'createdAt']: searchReq.sort
    };

    const query: FilterQuery<MediaItem> = {
      sourceId: user._id
    };
    if (searchReq.mediaType) query.mediaType = searchReq.mediaType;
    if (searchReq.q) {
      const regexp = new RegExp(searchReq.q.toLowerCase().trim(), 'i');
      query.$or = [
        { name: { $regex: regexp } },
        { description: { $regex: regexp } }
      ];
    }

    if (searchReq.categoryId) {
      query.categoryId = toObjectId(searchReq.categoryId);
    }

    if (searchReq.fromDate && searchReq.toDate) {
      query.createdAt = {
        $gt: moment(searchReq.fromDate).startOf('day').toDate(),
        $lt: moment(searchReq.toDate).endOf('day').toDate()
      };
    }

    const [data, total] = await Promise.all([
      this.MediaItemModel.find(query)
        .sort(sort)
        .lean()
        .limit(Number(searchReq.limit))
        .skip(Number(searchReq.offset)),
      this.MediaItemModel.countDocuments(query)
    ]);

    // load files and populate data
    if (!data.length) {
      return {
        data,
        total
      };
    }

    const fileIds = data.map((d) => d.fileId);
    const files = await this.fileService.findByIds(fileIds);
    const dtos = data.map((d) => {
      const dto = MediaItemDto.fromModel(d);

      const file = files.find((f) => d.fileId.equals(f._id));
      dto.setFile(file);

      return dto.toResponse();
    });

    return {
      data: dtos,
      total
    };
  }

  public async myDetails(id: string | ObjectId, user: UserDto | PerformerDto) {
    const model = await this.MediaItemModel.findById(id);
    if (!model) throw new EntityNotFoundException();
    if (!model.sourceId.equals(user._id)) throw new ForbiddenException();

    const file = await this.fileService.findById(model.fileId);
    const dto = MediaItemDto.fromModel(model);
    dto.setFile(file);
    return dto.toResponse();
  }

  public async myDetailsByFileId(
    fileId: string | ObjectId,
    user: UserDto | PerformerDto
  ) {
    const model = await this.MediaItemModel.findById({
      fileId
    });
    if (!model) throw new EntityNotFoundException();
    if (!model.sourceId.equals(user._id)) throw new ForbiddenException();

    const file = await this.fileService.findById(model.fileId);
    const dto = MediaItemDto.fromModel(model);
    dto.setFile(file);
    return dto.toResponse();
  }

  public async getMyMediaStats(user: UserDto | PerformerDto) {
    const media = await this.MediaItemModel.find({
      sourceId: user._id
    })
      .sort({ createdAt: -1 })
      .lean();

    if (!media.length) {
      return {
        total: 0,
        images: 0,
        videos: 0,
        previews: []
      };
    }

    // Get files for the first 4 items to generate previews
    const previewItems = media.slice(0, 4);
    const fileIds = previewItems.map((m) => m.fileId);
    const files = await this.fileService.findByIds(fileIds);

    return {
      total: media.length,
      images: media.filter((m) => m.mediaType === 'image').length,
      videos: media.filter((m) => m.mediaType === 'video').length,
      previews: previewItems.map((m) => {
        const file = files
          .find((f) => m.fileId.equals(f._id))
          .toPublicResponse(true);
        return {
          url:
            m.mediaType === 'image'
              ? file?.url
              : file?.thumbnails?.[0] || '/video.jpg',
          type: m.mediaType
        };
      })
    };
  }

  public async validateMine(
    user: UserDto | PerformerDto,
    mediaIds: string | ObjectId | Array<string | ObjectId>,
    options: IValidateMineOptions = {}
  ) {
    const ids = Array.isArray(mediaIds)
      ? mediaIds.map((id) => new mongoose.Types.ObjectId(id))
      : [new mongoose.Types.ObjectId(mediaIds)];

    if (!ids.length) {
      if (options.errorIfEmpty)
        throw new HttpException('Missing media data ID to check', 400);
      return {
        isMine: true,
        media: []
      };
    }
    const media = await this.MediaItemModel.find({
      $or: [{ _id: { $in: ids } }, { fileId: { $in: ids } }]
    });
    if (!media.length) {
      return {
        isMine: false,
        media: []
      };
    }

    const found = media.find((m) => !m.sourceId.equals(user._id));
    if (found) throw new HttpException('Invalid media data', 400);

    if (!options?.includingFileInfo) {
      return {
        isMine: true,
        media: media.map((m) => MediaItemDto.fromModel(m))
      };
    }

    const fileIds = media.map((m) => m.fileId);
    const files = await this.fileService.findByIds(fileIds);
    const dtos = media.map((m) => {
      const dto = MediaItemDto.fromModel(m);
      const file = files.find((f) => m.fileId.equals(f._id));
      dto.setFile(file);
      return dto;
    });
    return {
      isMine: true,
      media: dtos
    };
  }

  public async addRef(
    id: string | ObjectId,
    itemType: string,
    itemId: string | ObjectId
  ) {
    // if have no ref,
    const item = await this.MediaItemModel.findById(id);
    if (!item) return;

    const found = item.refItems?.find(
      (f) => f.itemId.toString() === itemId.toString()
    );
    if (found) return;

    // TODO - check if not have in the set
    await this.MediaItemModel.updateOne(
      { _id: id },
      {
        $addToSet: {
          refItems: {
            itemType,
            itemId
          }
        },
        $set: {
          updatedAt: new Date()
        }
      }
    );
  }

  public async removeRef(id: string | ObjectId, itemId: string | ObjectId) {
    const dbId = toObjectId(id);
    const dbItemId = toObjectId(itemId);
    // TODO - check if not have in the set
    await this.MediaItemModel.updateOne(
      {
        $or: [
          {
            _id: dbId
          },
          {
            fileId: dbId
          }
        ]
      },
      {
        $pull: {
          refItems: {
            itemId: dbItemId
          }
        },
        $set: {
          updatedAt: new Date()
        }
      }
    );
  }

  public async addRefByFileId(
    fileId: string | ObjectId,
    itemType: string,
    itemId: string | ObjectId
  ) {
    // if have no ref,
    const item = await this.MediaItemModel.findOne({ fileId });
    if (!item) return;

    const found = item.refItems?.find(
      (f) => f.itemId.toString() === itemId.toString()
    );
    if (found) return;

    // TODO - check if not have in the set
    await this.MediaItemModel.updateOne(
      { _id: item._id },
      {
        $addToSet: {
          refItems: {
            itemType,
            itemId
          }
        },
        $set: {
          updatedAt: new Date()
        }
      }
    );
  }

  public async removeRefByFileId(
    fileId: string | ObjectId,
    itemId: string | ObjectId
  ) {
    const dbId = toObjectId(fileId);
    const dbItemId = toObjectId(itemId);
    // TODO - check if not have in the set
    await this.MediaItemModel.updateOne(
      { fileId: dbId },
      {
        $pull: {
          refItems: {
            itemId: dbItemId
          }
        },
        $set: {
          updatedAt: new Date()
        }
      }
    );
  }

  public async findByCategoryId(categoryId: string | ObjectId) {
    const media = await this.MediaItemModel.find({
      categoryId: toObjectId(categoryId)
    })
      .sort({ createdAt: -1 })
      .lean();

    return media;
  }

  public async updateMediaItemsCategory(
    categoryId: string | ObjectId,
    mediaItemIds: Array<string | ObjectId>
  ) {
    const dbCategoryId = toObjectId(categoryId);
    const dbMediaItemIds = mediaItemIds.map((id) => toObjectId(id));

    const result = await this.MediaItemModel.updateMany(
      { _id: { $in: dbMediaItemIds } },
      {
        $set: {
          categoryId: dbCategoryId,
          updatedAt: new Date()
        }
      }
    );

    return {
      modifiedCount: result.modifiedCount,
      matchedCount: result.matchedCount
    };
  }
}
