const { DB, COLLECTION } = require('./lib');

module.exports.up = async function up(next) {
  const minimumPayoutSetting = {
    key: 'minimumPayoutRequest',
    value: 10,
    name: 'Minimum Payout Request',
    description: '',
    public: true,
    autoload: true,
    group: 'pricing',
    editable: true
  };
  try {
    const isExist = await DB.collection(COLLECTION.SETTING).findOne({
      key: minimumPayoutSetting.key
    });
    if (!isExist) {
      await DB.collection(COLLECTION.SETTING).insertOne(minimumPayoutSetting);
    }
  } catch (error) {
    console.log(error);
  }
  next();
};

module.exports.down = function down(next) {
  next();
};
