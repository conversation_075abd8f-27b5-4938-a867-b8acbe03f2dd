const { readFileSync } = require('fs');
const { join } = require('path');
const { DB, COLLECTION } = require('./lib');

const TEMPLATE_DIR = join(__dirname, '..', 'templates', 'emails');

async function createTemplate(key, data) {
  const template = await DB.collection(COLLECTION.EMAIL_TEMPLATE).findOne({
    key
  });
  if (template) return;
  const content = readFileSync(join(TEMPLATE_DIR, `${key}.html`)).toString();
  await DB.collection(COLLECTION.EMAIL_TEMPLATE).insertOne({
    key,
    content,
    layout: 'layouts/default',
    ...data,
    createdAt: new Date(),
    updatedAt: new Date()
  });
}

module.exports.up = async function up(next) {
  await createTemplate('model-tagging-email', {
    name: 'Notification Template for Model Tagging in a Video',
    subject: 'You’ve Been Tagged in a Video! 🎥',
    description:
      'Email notifications to model when creators tagging other creator during create new video'
  });

  next();
};

module.exports.down = function down(next) {
  next();
};
