const { readFileSync } = require('fs');
const { join } = require('path');
const { DB, COLLECTION } = require('./lib');

const TEMPLATE_DIR = join(__dirname, '..', 'templates', 'emails');

async function createTemplate(key, data) {
  const template = await DB.collection(COLLECTION.EMAIL_TEMPLATE).findOne({ key });
  if (template) return;
  const content = readFileSync(join(TEMPLATE_DIR, `${key}.html`)).toString();
  await DB.collection(COLLECTION.EMAIL_TEMPLATE).insertOne({
    key,
    content,
    layout: 'layouts/default',
    ...data,
    createdAt: new Date(),
    updatedAt: new Date()
  });
}

module.exports.up = async function up(next) {
  await createTemplate('performer-turn-on-free-subscription', {
    name: 'Performer turn on free subscriptions',
    subject: 'Turn on free subscription',
    description: 'Email notifications to users when creators turn on free subscriptions'
  });

  await createTemplate('performer-turn-off-free-subscription', {
    name: 'Performer turn off free subscription',
    subject: 'Turn off free subscription',
    description: 'Email notification to users when creators turn off free subscriptions'
  });

  next();
};

module.exports.down = function down(next) {
  next();
};
