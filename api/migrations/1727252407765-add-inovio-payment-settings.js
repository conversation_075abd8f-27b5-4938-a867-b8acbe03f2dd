const {
  createSettings
} = require('./lib');

const SETTING_KEYS = {
  INOVIO_ENABLED: 'payment.inovio.enabled',
  INOVIO_PRODUCTION_MODE: 'payment.inovio.ProductionMode',
  INOVIO_REQ_USERNAME: 'payment.inovio.ReqUsername',
  INOVIO_REQ_PASSWORD: 'payment.inovio.ReqPassword',
  INOVIO_SITE_ID: 'payment.inovio.SiteId',
  INOVIO_REQ_API_VERSION: 'payment.inovio.ReqApiVersion',
  INOVIO_CLIENT_ID: 'payment.inovio.ClientId',
  INOVIO_MERCH_ACCT_ID: 'payment.inovio.MerchAcctId',
  INOVIO_PAYMENT_CURRENCY: 'payment.inovio.PaymentCurrency',
  INOVIO_DDC_ORIGIN: 'payment.inovio.DDCOrigin',
  INOVIO_SINGLE_PRODUCT_ID: 'payment.inovio.SingleProductId',
  INOVIO_MONTHLY_SUBSCRIPTION_PRODUCT_ID: 'payment.inovio.MonthlySubscriptionProductId',
  INOVIO_YEARLY_SUBSCRIPTION_PRODUCT_ID: 'payment.inovio.YearlySubscriptionProductId'
};

const settings = [
  {
    key: SETTING_KEYS.INOVIO_ENABLED,
    value: false,
    name: 'Enable',
    description: 'Enable/Disable Inovio gateway',
    public: true,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'boolean',
    ordering: 0
  },
  {
    key: SETTING_KEYS.INOVIO_PRODUCTION_MODE,
    value: false,
    name: 'Enable Production Mode',
    description: 'Enable/Disable Inovio production mode',
    public: false,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'boolean',
    ordering: 1
  },
  {
    key: SETTING_KEYS.INOVIO_REQ_USERNAME,
    value: '',
    name: 'Merchant’s Service username',
    description: 'Merchant’s Service username',
    public: false,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'text',
    ordering: 2
  },
  {
    key: SETTING_KEYS.INOVIO_REQ_PASSWORD,
    value: '',
    name: 'Merchant’s Service password',
    description: 'Merchant’s Service password',
    public: false,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'text',
    ordering: 2
  },
  {
    key: SETTING_KEYS.INOVIO_SITE_ID,
    value: '',
    name: 'Merchant’s Website ID',
    description: 'Merchant’s Website ID',
    public: false,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'number',
    ordering: 3
  },
  {
    key: SETTING_KEYS.INOVIO_REQ_API_VERSION,
    value: '',
    name: 'API Version',
    description: 'API Version of this document. (4.5) Must be sent on all gateway requests.',
    public: false,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'number',
    ordering: 4
  },
  {
    key: SETTING_KEYS.INOVIO_CLIENT_ID,
    value: '',
    name: 'Client Id',
    description: 'Use for Credit Request',
    public: false,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'text',
    ordering: 6
  },
  {
    key: SETTING_KEYS.INOVIO_MERCH_ACCT_ID,
    value: '',
    name: 'Merchant’s account ID',
    description: 'Merchant’s account ID',
    public: false,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'number',
    ordering: 8
  },
  {
    key: SETTING_KEYS.INOVIO_PAYMENT_CURRENCY,
    value: 'EUR',
    name: 'Currency',
    description: '',
    public: false,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'text',
    ordering: 9
  },
  {
    key: SETTING_KEYS.INOVIO_SINGLE_PRODUCT_ID,
    value: 0,
    name: 'Single product ID',
    description: 'Single product ID, use to purchase single payment such as topup wallet, PPV...',
    public: false,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'number',
    ordering: 10
  },
  {
    key: SETTING_KEYS.INOVIO_MONTHLY_SUBSCRIPTION_PRODUCT_ID,
    value: 287166,
    name: 'Monthly subscription product ID',
    description: 'Monthly subscription product ID. This is default product ID if cannot find on model profile',
    public: false,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'number',
    ordering: 11
  },
  {
    key: SETTING_KEYS.INOVIO_YEARLY_SUBSCRIPTION_PRODUCT_ID,
    value: 287168,
    name: 'Yearly subscription product ID',
    description: 'Yearly subscription product ID. This is default product ID if cannot find on model profile',
    public: false,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'number',
    ordering: 12
  },
  {
    key: SETTING_KEYS.INOVIO_DDC_ORIGIN,
    value: '',
    name: 'DDC origin',
    description: 'Used to detect events from 3DS. Check with ',
    public: true,
    group: 'payment-gateway.inovio',
    editable: true,
    type: 'text',
    ordering: 13
  }
];

module.exports.up = async function (next) {
  await createSettings(settings);
  next();
};

module.exports.down = function (next) {
  next();
};
