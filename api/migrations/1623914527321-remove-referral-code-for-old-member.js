// Login changes, model will input their own code
const { DB, COLLECTION } = require('./lib');

module.exports.up = async function up(next) {
  try {
    await DB.collection(COLLECTION.PERFORMER).updateMany(
      {},
      {
        $set: {
          referralCode: '',
          hasUpdatedReferralCode: false
        }
      }
    );
  } catch (error) {
    console.log('error remove referral code');
  }
  next();
};

module.exports.down = function down(next) {
  next();
};
