const { DB, COLLECTION } = require('./lib');

module.exports.up = async function up(next) {
  const IS_ALLOWED_REFERRAL_KEY = 'isAllowedReferral';
  const settings = {
    key: IS_ALLOWED_REFERRAL_KEY,
    value: true,
    name: 'Referral',
    description: 'Enable/Disable Referral feature',
    public: true,
    group: 'general',
    editable: true,
    type: 'boolean',
    ordering: 1
  };
  try {
    const isExisted = await DB.collection(COLLECTION.SETTING).findOne({
      key: IS_ALLOWED_REFERRAL_KEY
    });
    if (!isExisted) {
      await DB.collection(COLLECTION.SETTING).insertOne(settings);
    }
  } catch (error) {
    console.log(error);
  }
  next();
};

module.exports.down = function down(next) {
  next();
};
