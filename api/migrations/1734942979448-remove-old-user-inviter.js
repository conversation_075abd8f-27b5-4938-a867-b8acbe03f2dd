const { DB, COLLECTION } = require('./lib');

module.exports.up = async function up(next) {
  try {
    const [referrals, performers, users] = await Promise.all([
      DB.collection(COLLECTION.REFERRAL).find({}).toArray(),
      DB.collection(COLLECTION.PERFORMER).find({}).toArray(),
      DB.collection(COLLECTION.USER).find({}).toArray()
    ]);

    // remove referrals
    referrals.reduce(async (lp, referral) => {
      await lp;
      const inviter = await DB.collection(COLLECTION.PERFORMER).findOne({
        _id: referral.inviterId
      });
      if (!inviter || !referral.invitedRole !== 'performer') {
        await DB.collection(COLLECTION.REFERRAL).deleteOne({
          _id: referral._id
        });
      }
    }, Promise.resolve());
    // remove user refCode
    users.reduce(async (lp, user) => {
      await lp;
      await DB.collection(COLLECTION.USER).updateOne(
        {
          _id: user._id
        },
        {
          $set: {
            referralCode: '',
            invitedBy: null,
            invitedById: null
          }
        }
      );
    }, Promise.resolve());

    // remove performer inviter who is user.
    performers.reduce(async (lp, performer) => {
      await lp;
      if (performer.invitedById) {
        const user = await DB.collection(COLLECTION.USER).findOne({
          _id: performer.invitedById
        });
        if (user) {
          await DB.collection(COLLECTION.PERFORMER).updateOne(
            {
              _id: performer._id
            },
            {
              $set: {
                referralCode: '',
                invitedBy: null,
                invitedById: null
              }
            }
          );
        }
      }
    }, Promise.resolve());
  } catch (error) {
    const e = await error;
    console.log(e);
  }
  next();
};

module.exports.down = function down(next) {
  next();
};
