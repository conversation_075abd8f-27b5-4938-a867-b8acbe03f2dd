const { DB, COLLECTION } = require('./lib');

module.exports.up = async function up(next) {
  const query = {
    invitedById: { $exists: true }
  };
  const [users, performers] = await Promise.all([
    DB.collection(COLLECTION.USER).find(query).toArray(),
    DB.collection(COLLECTION.PERFORMER).find(query).toArray()
  ]);
  users.reduce(async (lp, u) => {
    await lp;
    const [user, performer, referral] = await Promise.all([
      DB.collection(COLLECTION.USER).findOne({ _id: u.invitedById }),
      DB.collection(COLLECTION.PERFORMER).findOne({ _id: u.invitedById }),
      DB.collection(COLLECTION.REFERRAL).findOne({
        invitedId: u._id,
        inviterId: u.invitedById
      })
    ]);

    const existInviter = user || performer;
    if (existInviter && !referral) {
      const userRefQuery = {
        inviterId: existInviter._id,
        invitedId: u._id,
        referralCode: existInviter.referralCode,
        invitedUsername: u.username,
        invitedRole: u.roles.pop(),
        createdAt: u.createdAt,
        updatedAt: u.updatedAt
      };
      await DB.collection(COLLECTION.REFERRAL).insertOne(userRefQuery);
    }
  }, Promise.resolve());
  performers.reduce(async (lp, u) => {
    await lp;
    const [user, performer, referral] = await Promise.all([
      DB.collection(COLLECTION.USER).findOne({ _id: u.invitedById }),
      DB.collection(COLLECTION.PERFORMER).findOne({ _id: u.invitedById }),
      DB.collection(COLLECTION.REFERRAL).findOne({
        invitedId: u._id,
        inviterId: u.invitedById
      })
    ]);

    const existInviter = user || performer;
    if (existInviter && !referral) {
      const userRefQuery = {
        inviterId: existInviter._id,
        invitedId: u._id,
        referralCode: existInviter.referralCode,
        invitedUsername: u.username,
        invitedRole: 'performer',
        createdAt: u.createdAt,
        updatedAt: u.updatedAt
      };
      await DB.collection(COLLECTION.REFERRAL).insertOne(userRefQuery);
    }
  }, Promise.resolve());
  next();
};

module.exports.down = function down(next) {
  next();
};
