const { DB, COLLECTION } = require('./lib');

const SETTING_KEYS = {
  REFERRAL_ENABLED: 'referralEnabled',
  INVITE_USER_COMMISSION: 'inviteUserCommission',
  INVITE_PERFORMER_COMMISSION: 'invitePerformerCommission',
  OPTION_FOR_REFERRAL: 'optionForReferral',
  INVITE_USER_FLAT_FEE: 'inviteUserFlatFee',
  INVITE_PERFORMER_FLAT_FEE: 'invitePerformerFlatFee'
};

const settings = [
  {
    key: SETTING_KEYS.REFERRAL_ENABLED,
    value: false,
    name: 'Enable/Disable referral program',
    description: 'Enable/Disable referral program, inviter will not receive any commission if you turn this option off',
    public: true,
    group: 'referral',
    editable: true,
    autoload: true,
    type: 'boolean',
    ordering: 0
  },
  {
    key: SETTING_KEYS.OPTION_FOR_REFERRAL,
    value: 'flatFee',
    name: 'Option for referral reward',
    description: 'If sellect "Flat fee", inviter will receive reward one time only. If sellect "Commission", inviter will receive reward every time invited member spend (user) money/wallet or sell (model) an item',
    public: false,
    group: 'referral',
    autoload: false,
    editable: true,
    type: 'radio',
    meta: {
      value: [{ key: 'flatFee', name: 'Flat Fee' }, { key: 'commission', name: 'Commission' }]
    },
    ordering: 1
  },
  {
    key: SETTING_KEYS.INVITE_USER_FLAT_FEE,
    value: 5,
    name: 'Invite user flat fee (wallet)',
    description: 'Flat fee that inviter receive everytime they invite successfully an user',
    public: false,
    group: 'referral',
    autoload: false,
    editable: true,
    type: 'number',
    meta: {
      min: 0
    },
    ordering: 2
  },
  {
    key: SETTING_KEYS.INVITE_PERFORMER_FLAT_FEE,
    value: 5,
    name: 'Invite model flat fee (wallet)',
    description: 'Flat fee that inviter receive everytime they invite successfully a model',
    public: false,
    group: 'referral',
    autoload: false,
    editable: true,
    type: 'number',
    meta: {
      min: 0
    },
    ordering: 2
  },
  {
    key: SETTING_KEYS.INVITE_USER_COMMISSION,
    value: 0.03,
    name: 'Invite user reward commission 0.01-0.99 (1%-99%)',
    description: 'Percent of total money/wallet that inviter receive everytime user spend',
    public: false,
    group: 'referral',
    editable: true,
    autoload: false,
    type: 'number',
    meta: {
      min: 0,
      max: 1,
      step: 0.01
    },
    ordering: 3
  },
  {
    key: SETTING_KEYS.INVITE_PERFORMER_COMMISSION,
    value: 0.03,
    name: 'Invite model reward commission 0.01-0.99 (1%-99%)',
    description: 'Percent of total money/wallet that inviter receive everytime user spend money to purchase something from invited model',
    public: false,
    group: 'referral',
    autoload: false,
    editable: true,
    type: 'number',
    meta: {
      min: 0,
      max: 1,
      step: 0.01
    },
    ordering: 3
  }
];

module.exports.up = async function up(next) {
  // eslint-disable-next-line no-console
  console.log('Migrate referral program settings');

  // eslint-disable-next-line no-restricted-syntax
  for (const setting of settings) {
    // eslint-disable-next-line no-await-in-loop
    const checkKey = await DB.collection(COLLECTION.SETTING).findOne({
      key: setting.key
    });
    if (!checkKey) {
      // eslint-disable-next-line no-await-in-loop
      await DB.collection(COLLECTION.SETTING).insertOne({
        ...setting,
        type: setting.type || 'text',
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
  }
  // eslint-disable-next-line no-console
  console.log('Migrate referral program done');
  next();
};

module.exports.down = function down(next) {
  next();
};
