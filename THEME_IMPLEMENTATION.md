# Dark/Light Mode Theme Implementation

## Overview

This implementation provides a comprehensive dark/light mode theme switching system for both the admin and user React applications. The system supports three theme modes: Light, Dark, and System (follows OS preference), with System mode as the default.

## Features

✅ **Three Theme Modes**
- Light mode
- Dark mode  
- System mode (follows OS preference)
- Default: System mode

✅ **Modern Theme Toggle UI**
- Professional dropdown interface with icons
- Clear visual indicators for current theme state
- Responsive design for desktop and mobile
- Smooth transitions between themes

✅ **Technical Implementation**
- Integrated with existing Ant Design components
- CSS variables for consistent theming
- Redux state management for theme persistence
- localStorage persistence across sessions
- System theme detection and automatic switching
- Smooth theme transitions

✅ **Accessibility & UX**
- WCAG compliant color contrasts
- Keyboard navigation support
- Clear visual feedback
- Consistent design system integration

## File Structure

### Core Theme Files

```
user/src/hooks/useTheme.ts                    # Theme management hook
admin/src/hooks/useTheme.ts                   # Theme management hook (admin)

user/src/components/common/theme-toggle/      # Theme toggle component
├── theme-toggle.tsx
├── theme-toggle.module.less
└── index.ts

admin/src/components/common/theme-toggle/     # Theme toggle component (admin)
├── theme-toggle.tsx
├── theme-toggle.module.less
└── index.ts

user/src/components/common/theme-provider/    # Theme initialization
└── theme-provider.tsx

admin/src/components/common/theme-provider/   # Theme initialization (admin)
└── theme-provider.tsx
```

### Style Files

```
user/style/app.less                          # CSS variables and dark mode overrides
admin/style/theme-variables.less             # CSS variables for admin
admin/style/index.less                       # Updated to include theme variables
user/style/index.less                        # Updated body styles
```

### Redux Updates

```
user/src/redux/ui/reducers.ts                # Updated initial state
admin/src/redux/ui/reducers.ts               # Updated initial state
```

## Usage

### Theme Toggle Component

```tsx
import ThemeToggle from '@components/common/theme-toggle';

// Basic usage
<ThemeToggle />

// With options
<ThemeToggle 
  size="middle"
  showLabel={true}
  placement="bottomRight"
  className="custom-theme-toggle"
/>
```

### Theme Hook

```tsx
import { useTheme } from '@hooks/useTheme';

function MyComponent() {
  const { theme, actualTheme, setTheme, toggleTheme, isSystemTheme } = useTheme();
  
  // Current theme setting ('light' | 'dark' | 'system')
  console.log(theme);
  
  // Actual resolved theme ('light' | 'dark')
  console.log(actualTheme);
  
  // Change theme
  setTheme('dark');
  
  // Toggle between light/dark
  toggleTheme();
  
  // Check if using system theme
  console.log(isSystemTheme);
}
```

## CSS Variables

The implementation uses CSS custom properties for consistent theming:

### Light Theme Variables
```css
:root {
  --bg-white: #fff;
  --bg-primary: #7b5dbd;
  --text-color: #444;
  --border-color-base: #e5e5e5;
  --component-background: #fff;
  /* ... more variables */
}
```

### Dark Theme Variables
```css
.dark-mode {
  --bg-white: #141414;
  --bg-primary: #7b5dbd;
  --text-color: rgba(255, 255, 255, 0.85);
  --border-color-base: #434343;
  --component-background: #1f1f1f;
  /* ... more variables */
}
```

## Integration Points

### 1. Admin App
- Theme toggle in sidebar (when not collapsed)
- Replaces old simple switch with modern dropdown
- Integrated with existing layout system

### 2. User App
- Theme toggle in header (both logged in and logged out states)
- Positioned before other header elements
- Responsive design for mobile

### 3. Redux State
```typescript
interface UIState {
  theme: 'light' | 'dark' | 'system';
  actualTheme: 'light' | 'dark';
  // ... other UI state
}
```

## Browser Support

- Modern browsers with CSS custom properties support
- Graceful fallback for older browsers
- System theme detection via `prefers-color-scheme` media query

## Performance Considerations

- CSS variables provide efficient theme switching
- Minimal JavaScript overhead
- Smooth transitions without layout shifts
- localStorage caching for instant theme restoration

## Customization

### Adding New Theme Variables

1. Add to both light and dark theme sections in CSS
2. Use consistent naming convention
3. Provide fallback values

```css
.light-mode {
  --new-variable: #value;
}

.dark-mode {
  --new-variable: #dark-value;
}

/* Usage */
.my-component {
  color: var(--new-variable, #fallback);
}
```

### Extending Theme Options

To add new theme modes, update:
1. `ThemeMode` type in `useTheme.ts`
2. Theme options array in `theme-toggle.tsx`
3. Logic in `useTheme` hook

## Testing

### Manual Testing Checklist

- [ ] Theme toggle appears in correct locations
- [ ] All three theme modes work correctly
- [ ] System theme detection works
- [ ] Theme persists across page reloads
- [ ] Smooth transitions between themes
- [ ] All Ant Design components styled correctly
- [ ] Mobile responsive design
- [ ] Keyboard navigation works
- [ ] No console errors

### Browser Testing

Test in:
- Chrome/Chromium
- Firefox
- Safari
- Edge

## Troubleshooting

### Common Issues

1. **Theme not persisting**: Check localStorage permissions
2. **Styles not applying**: Verify CSS variable names and fallbacks
3. **System theme not detected**: Check browser support for `prefers-color-scheme`
4. **Import errors**: Verify file paths and exports

### Debug Mode

Add to component for debugging:
```tsx
const { theme, actualTheme } = useTheme();
console.log('Theme state:', { theme, actualTheme });
```

## Future Enhancements

- [ ] Additional theme variants (high contrast, etc.)
- [ ] Theme scheduling (automatic switching based on time)
- [ ] Custom color picker for user-defined themes
- [ ] Theme preview mode
- [ ] Animation preferences respect `prefers-reduced-motion`

## Migration Notes

### From Old Implementation

The old simple switch in admin sidebar has been replaced with the new dropdown component. The Redux state structure has been extended but remains backward compatible.

### Breaking Changes

None - the implementation is fully backward compatible with existing code.
