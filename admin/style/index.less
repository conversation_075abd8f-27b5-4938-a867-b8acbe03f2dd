@import '../node_modules/antd/dist/antd.less';
@import './vars.less';
@import './theme-variables.less';
@import './wysityg.less';
@import './custom.less';

body {
  height: 100%;
  overflow-y: hidden;
  background-color: var(--bg-secondary, #f8f8f8);
  color: var(--text-color, @text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

::-webkit-scrollbar-thumb {
  background-color: var(--text-color-tertiary, #999);
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: var(--bg-secondary, #f8f8f8);
  transition: background-color 0.3s ease;
}

:global {
  .ant-row {
    margin-left: -5px !important;
    margin-right: -5px !important;

    .ant-col {
      padding: 5px !important;
    }
  }

  .ant-table tbody > tr > td {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    .ant-tag {
      margin: 0;
    }
  }

  .dashboard-stats {
    .ant-card {
      &:hover {
        background-color: #ccc;
      }
    }
  }

  .ant-breadcrumb {
    & > span {
      &:last-child {
        color: #999;
        font-weight: normal;
      }
    }
  }

  .ant-breadcrumb-link {
    .anticon + span {
      margin-left: 4px;
    }
  }

  .ant-table {
    overflow: auto;
    &.ant-table-small {
      .ant-table-thead > tr > th {
        background: #f7f7f7;
      }

      .ant-table-body > table {
        padding: 0;
      }
    }
  }

  .ant-table-pagination {
    float: none !important;
    display: table;
    margin: 16px auto !important;
  }

  .ant-popover-inner {
    border: none;
    border-radius: 0;
    box-shadow: 0 0 20px rgba(100, 100, 100, 0.2);
  }

  .ant-form-item-control {
    vertical-align: middle;
  }

  .ant-modal-mask {
    background-color: rgba(55, 55, 55, 0.2);
  }

  .ant-modal-content {
    box-shadow: none;
  }

  .ant-select-dropdown-menu-item {
    padding: 12px 16px !important;
  }

  .margin-right {
    margin-right: 16px;
  }

  a:focus {
    text-decoration: none;
  }

  .ant-table-layout-fixed table {
    table-layout: auto;
  }
}

@media (min-width: 1600px) {
  :global {
    .ant-col-xl-48 {
      width: 20%;
    }

    .ant-col-xl-96 {
      width: 40%;
    }
  }
}

@media (max-width: 767px) {
  :global {
    .ant-pagination-item,
    .ant-pagination-next,
    .ant-pagination-options,
    .ant-pagination-prev {
      margin-bottom: 8px;
    }

    .ant-card {
      .ant-card-head {
        padding: 0 12px;
      }

      .ant-card-body {
        padding: 12px;
      }
    }
  }
}

@media (max-width: 500px) {
  :global {
    textarea:focus,
    input:focus {
      font-size: 16px !important;
    }

    .ant-statistic-title {
      font-size: 12px;
    }
  }
}
.ant-table {
  overflow-x: auto !important;
}

@media (max-width: 767px) {
  .ant-layout-sider-collapsed {
    flex: 0 0 0px !important;
    max-width: 0 !important;
    min-width: 0 !important;
    width: 0 !important;
  }
}

// Dark mode specific overrides for Ant Design components
.dark-mode {
  // Global anticon dark mode fixes - Professional, modern, smart and intuitive UI
  .anticon {
    color: var(--text-color-secondary) !important;
    transition: color 0.3s ease !important;

    &:hover {
      color: var(--primary-color) !important;
    }

    // Specific icon color overrides for better dark mode support
    &.anticon-search {
      color: var(--text-color-secondary) !important;
    }

    &.anticon-close {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--error-color) !important;
      }
    }

    &.anticon-check {
      color: var(--success-color) !important;
    }

    &.anticon-exclamation-circle,
    &.anticon-warning {
      color: var(--warning-color) !important;
    }

    &.anticon-info-circle {
      color: var(--info-color) !important;
    }

    &.anticon-question-circle {
      color: var(--primary-color) !important;
    }

    // Button icons should inherit from their parent button
    .ant-btn & {
      color: inherit !important;
    }

    // Form label icons
    .ant-form-item-label & {
      color: var(--primary-color) !important;
    }

    // Alert icons
    .ant-alert & {
      color: inherit !important;
    }

    // Menu icons
    .ant-menu & {
      color: inherit !important;
    }

    // Dropdown icons
    .ant-dropdown & {
      color: var(--text-color-secondary) !important;
    }

    // Table action icons
    .ant-table & {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Card action icons
    .ant-card & {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Input suffix/prefix icons
    .ant-input-suffix &,
    .ant-input-prefix & {
      color: var(--text-color-secondary) !important;
    }

    // Select arrow icons
    .ant-select-arrow & {
      color: var(--text-color-secondary) !important;
    }

    // Pagination icons
    .ant-pagination & {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Breadcrumb icons
    .ant-breadcrumb & {
      color: var(--text-color-secondary) !important;
    }

    // Upload icons
    .ant-upload & {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Modal icons
    .ant-modal & {
      color: var(--text-color-secondary) !important;
    }

    // Drawer icons
    .ant-drawer & {
      color: var(--text-color-secondary) !important;
    }

    // Notification icons
    .ant-notification & {
      color: inherit !important;
    }

    // Message icons
    .ant-message & {
      color: inherit !important;
    }

    // Calendar icons
    .ant-calendar &,
    .ant-picker & {
      color: var(--text-color-secondary) !important;
    }

    // List icons
    .ant-list & {
      color: var(--text-color-secondary) !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Timeline icons
    .ant-timeline & {
      color: var(--primary-color) !important;
    }

    // Rate icons
    .ant-rate & {
      color: var(--warning-color) !important;
    }

    // Switch icons
    .ant-switch & {
      color: var(--color-white) !important;
    }

    // Progress icons
    .ant-progress & {
      color: inherit !important;
    }

    // Spin icons
    .ant-spin & {
      color: var(--primary-color) !important;
    }
  }

  // Layout components
  .ant-layout {
    background: var(--bg-white);
  }

  .ant-layout-header {
    background: var(--component-background);
    border-bottom: 1px solid var(--border-color-base);
    color: var(--text-color);
  }

  .ant-layout-content {
    background: var(--bg-white);
  }

  .ant-layout-sider {
    background: var(--component-background);
  }

  // Card components
  .ant-card {
    background: var(--component-background);
    border-color: var(--border-color-base);
    color: var(--text-color);

    .ant-card-head {
      background: var(--component-background);
      border-bottom-color: var(--border-color-base);
      color: var(--text-color);
    }

    .ant-card-body {
      background: var(--component-background);
      color: var(--text-color);
    }
  }

  // Button components
  .ant-btn {
    border-color: var(--border-color-base);

    &:not(.ant-btn-primary) {
      background: var(--component-background);
      color: var(--text-color);

      &:hover {
        background: var(--item-hover-bg);
        border-color: var(--primary-color);
        color: var(--primary-color);
      }

      &:focus {
        background: var(--component-background);
        border-color: var(--primary-color);
        color: var(--primary-color);
      }
    }

    &.ant-btn-default {
      background: var(--component-background);
      border-color: var(--border-color-base);
      color: var(--text-color);

      &:hover {
        background: var(--item-hover-bg);
        border-color: var(--primary-color);
        color: var(--primary-color);
      }
    }
  }

  // Form components
  .ant-form-item-label > label {
    color: var(--text-color);
  }

  .ant-input {
    background: var(--component-background);
    border-color: var(--border-color-base);
    color: var(--text-color);

    &:hover {
      border-color: var(--primary-color);
    }

    &:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-fade);
    }

    &::placeholder {
      color: var(--text-color-tertiary);
    }
  }

  .ant-input-affix-wrapper {
    background: var(--component-background);
    border-color: var(--border-color-base);

    &:hover {
      border-color: var(--primary-color);
    }

    &:focus-within {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-fade);
    }

    .ant-input {
      background: transparent;
      border: none;
    }
  }

  .ant-select {
    .ant-select-selector {
      background: var(--component-background);
      border-color: var(--border-color-base);
      color: var(--text-color);
    }

    &:hover .ant-select-selector {
      border-color: var(--primary-color);
    }

    &.ant-select-focused .ant-select-selector {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-fade);
    }

    .ant-select-selection-placeholder {
      color: var(--text-color-tertiary);
    }
  }

  // Table components
  .ant-table {
    background: var(--component-background);
    color: var(--text-color);

    .ant-table-thead > tr > th {
      background: var(--bg-secondary);
      border-bottom-color: var(--border-color-base);
      color: var(--text-color);

      // Fix for table header column separator dark mode
      &:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
        background-color: var(--border-color-split) !important;
      }
    }

    .ant-table-tbody > tr > td {
      border-bottom-color: var(--border-color-split);
      color: var(--text-color);
    }

    .ant-table-tbody > tr:hover > td {
      background: var(--item-hover-bg);
    }
  }

  // Menu components
  .ant-menu {
    background: var(--component-background);
    color: var(--text-color);

    .ant-menu-item {
      color: var(--text-color);

      &:hover {
        background: var(--item-hover-bg);
        color: var(--primary-color);
      }

      &.ant-menu-item-selected {
        background: var(--item-active-bg);
        color: var(--primary-color);
      }
    }

    .ant-menu-submenu-title {
      color: var(--text-color);

      &:hover {
        background: var(--item-hover-bg);
        color: var(--primary-color);
      }
    }

    &.ant-menu-dark {
      background: var(--component-background);

      .ant-menu-item {
        color: var(--text-color);

        &:hover {
          background: var(--item-hover-bg);
          color: var(--primary-color);
        }

        &.ant-menu-item-selected {
          background: var(--primary-color);
          color: var(--color-white);
        }
      }

      .ant-menu-submenu-title {
        color: var(--text-color);

        &:hover {
          background: var(--item-hover-bg);
          color: var(--primary-color);
        }
      }
    }
  }

  // Modal components
  .ant-modal-content {
    background: var(--component-background);
    color: var(--text-color);
  }

  .ant-modal-header {
    background: var(--component-background);
    border-bottom-color: var(--border-color-base);

    .ant-modal-title {
      color: var(--text-color);
    }
  }

  .ant-modal-footer {
    background: var(--component-background);
    border-top-color: var(--border-color-base);
  }

  // Dropdown components
  .ant-dropdown-menu {
    background: var(--component-background);
    border-color: var(--border-color-base);
    box-shadow: 0 6px 16px -8px var(--shadow-color), 0 9px 28px 0 var(--shadow-color-light),
      0 12px 48px 16px var(--shadow-color-light);

    .ant-dropdown-menu-item {
      color: var(--text-color);

      &:hover {
        background: var(--item-hover-bg);
      }
    }
  }

  // Pagination components - Professional, modern, smart and intuitive UI
  .ant-pagination {
    .ant-pagination-item {
      background: var(--component-background) !important;
      border-color: var(--border-color-base) !important;

      a {
        color: var(--text-color) !important;
      }

      &:hover {
        border-color: var(--primary-color) !important;

        a {
          color: var(--primary-color) !important;
        }
      }

      &.ant-pagination-item-active {
        background: var(--primary-color) !important;
        border-color: var(--primary-color) !important;

        a {
          color: var(--color-white) !important;
        }
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next {
      background: var(--component-background) !important;
      border-color: var(--border-color-base) !important;

      .ant-pagination-item-link {
        background: var(--component-background) !important;
        border-color: var(--border-color-base) !important;
        color: var(--text-color) !important;
        transition: all 0.3s ease !important;
      }

      &:hover {
        border-color: var(--primary-color) !important;

        .ant-pagination-item-link {
          background: var(--component-background) !important;
          border-color: var(--primary-color) !important;
          color: var(--primary-color) !important;
        }
      }

      &.ant-pagination-disabled {
        .ant-pagination-item-link {
          background: var(--bg-disabled) !important;
          border-color: var(--border-color-base) !important;
          color: var(--text-color-disabled) !important;
          cursor: not-allowed !important;
        }
      }
    }

    // Additional overrides for hardcoded pagination item link colors
    .ant-pagination-prev .ant-pagination-item-link,
    .ant-pagination-next .ant-pagination-item-link {
      background-color: var(--component-background) !important;
      border: 1px solid var(--border-color-base) !important;
      color: var(--text-color) !important;
      transition: all 0.3s ease !important;

      &:hover {
        background-color: var(--component-background) !important;
        border-color: var(--primary-color) !important;
        color: var(--primary-color) !important;
      }
    }

    // Disabled state overrides
    .ant-pagination-disabled {
      .ant-pagination-item-link {
        background-color: var(--bg-disabled) !important;
        border-color: var(--border-color-base) !important;
        color: var(--text-color-disabled) !important;
        cursor: not-allowed !important;
      }
    }
  }

  // Breadcrumb components
  .ant-breadcrumb {
    color: var(--text-color);

    .ant-breadcrumb-link {
      color: var(--text-color-secondary);

      &:hover {
        color: var(--primary-color);
      }
    }

    .ant-breadcrumb-separator {
      color: var(--text-color-tertiary);
    }
  }

  // Divider components
  .ant-divider {
    border-color: var(--border-color-base);

    // Divider with text content
    &.ant-divider-with-text {
      color: var(--text-color);

      &::before,
      &::after {
        border-top-color: var(--border-color-base);
      }

      .ant-divider-inner-text {
        color: var(--text-color-secondary);
        background-color: var(--component-background);
        padding: 0 16px;
        font-weight: 500;
        border-radius: 4px;
        transition: all 0.3s ease;
      }
    }

    // Horizontal divider
    &.ant-divider-horizontal {
      border-top-color: var(--border-color-base);

      &.ant-divider-with-text-left,
      &.ant-divider-with-text-right,
      &.ant-divider-with-text-center {
        .ant-divider-inner-text {
          color: var(--text-color-secondary);
          background-color: var(--component-background);
        }
      }
    }

    // Vertical divider
    &.ant-divider-vertical {
      border-left-color: var(--border-color-base);
    }

    // Dashed divider
    &.ant-divider-dashed {
      border-color: var(--border-color-base);
      border-style: dashed;
    }
  }

  // DatePicker dark mode improvements
  .ant-picker {
    background: var(--component-background);
    border-color: var(--border-color-base);
    color: var(--text-color);

    &:hover {
      border-color: var(--primary-color);
    }

    &.ant-picker-focused {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-fade);
    }

    .ant-picker-input > input {
      color: var(--text-color);

      &::placeholder {
        color: var(--text-color-tertiary);
      }
    }

    .ant-picker-suffix {
      color: var(--text-color-secondary);
    }

    // DatePicker separator icon dark mode fix - Professional, modern, smart and intuitive UI
    .ant-picker-separator {
      .anticon {
        color: var(--text-color-secondary) !important;
        transition: color 0.3s ease !important;

        &:hover {
          color: var(--primary-color) !important;
        }
      }
    }
  }

  // DatePicker dropdown
  .ant-picker-dropdown {
    .ant-picker-panel-container {
      background: var(--component-background);
      border: 1px solid var(--border-color-base);
      box-shadow: 0 6px 16px -8px var(--shadow-color), 0 9px 28px 0 var(--shadow-color-light),
        0 12px 48px 16px var(--shadow-color-light);
    }

    .ant-picker-panel {
      background: var(--component-background);
      border: none;

      .ant-picker-header {
        color: var(--text-color) !important;
        border-bottom: 1px solid var(--border-color-base);

        button {
          color: var(--text-color);

          &:hover {
            color: var(--primary-color);
          }
        }
      }

      .ant-picker-content {
        th {
          color: var(--text-color-secondary);
        }

        .ant-picker-cell {
          color: var(--text-color);

          &:hover .ant-picker-cell-inner {
            background: var(--item-hover-bg);
          }

          &.ant-picker-cell-selected .ant-picker-cell-inner {
            background: var(--primary-color);
            color: var(--color-white);
          }

          &.ant-picker-cell-today .ant-picker-cell-inner {
            border-color: var(--primary-color);
          }
        }
      }
    }

    // Fix for complex picker cell hover states with hardcoded #f5f5f5 background
    .ant-picker-cell:hover:not(.ant-picker-cell-in-view) .ant-picker-cell-inner,
    .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(
        .ant-picker-cell-range-end
      ):not(.ant-picker-cell-range-hover-start):not(.ant-picker-cell-range-hover-end)
      .ant-picker-cell-inner {
      background: var(--item-hover-bg) !important;
    }

    // Additional picker cell states for comprehensive dark mode support
    .ant-picker-cell {
      &:not(.ant-picker-cell-in-view) {
        .ant-picker-cell-inner {
          color: var(--text-color-disabled);
        }

        &:hover .ant-picker-cell-inner {
          background: var(--item-hover-bg) !important;
          color: var(--text-color-secondary);
        }
      }

      &.ant-picker-cell-range-start .ant-picker-cell-inner,
      &.ant-picker-cell-range-end .ant-picker-cell-inner {
        background: var(--primary-color);
        color: var(--color-white);
      }

      &.ant-picker-cell-range-hover-start .ant-picker-cell-inner,
      &.ant-picker-cell-range-hover-end .ant-picker-cell-inner {
        background: var(--primary-color-fade);
        color: var(--primary-color);
      }

      &.ant-picker-cell-in-range .ant-picker-cell-inner {
        background: var(--primary-color-fade);
        color: var(--text-color);
      }
    }

    // Time picker panel dark mode fixes - Professional, modern, smart and intuitive UI
    .ant-picker-time-panel {
      background: var(--component-background);
      border-color: var(--border-color-base);

      .ant-picker-time-panel-column {
        > li {
          color: var(--text-color);

          &:hover {
            background: var(--item-hover-bg);
          }

          &.ant-picker-time-panel-cell-selected {
            .ant-picker-time-panel-cell-inner {
              background: var(--primary-color-fade) !important;
              color: var(--primary-color) !important;
              transition: all 0.3s ease !important;
            }
          }

          &.ant-picker-time-panel-cell-disabled {
            color: var(--text-color-disabled);
            cursor: not-allowed;

            &:hover {
              background: transparent;
            }
          }
        }
      }
    }

    // Fix for hardcoded #f5f5f5 background in time picker cell inner hover state
    .ant-picker-time-panel-column > li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner:hover {
      background: var(--item-hover-bg) !important;
    }
  }

  // Ant Design Upload Drag component styling - Professional, modern, smart and intuitive UI
  .ant-upload.ant-upload-drag {
    background: var(--component-background) !important;
    border: 1px dashed var(--border-color-base) !important;
    color: var(--text-color) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

    &:hover:not(.ant-upload-disabled) {
      background: var(--bg-hover) !important;
      border-color: var(--primary-color) !important;
    }

    .ant-upload-drag-icon .anticon {
      color: var(--text-color-secondary) !important;
      transition: color 0.3s ease !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    .ant-upload-text {
      color: var(--text-color) !important;
    }

    .ant-upload-hint {
      color: var(--text-color-secondary) !important;
    }

    // Plus icon styling for upload areas
    .anticon-plus {
      color: var(--text-color-secondary) !important;
      transition: color 0.3s ease !important;

      &:hover {
        color: var(--primary-color) !important;
      }
    }

    // Disabled state
    &.ant-upload-disabled {
      background: var(--bg-disabled) !important;
      border-color: var(--border-color-base) !important;
      color: var(--text-color-disabled) !important;
      cursor: not-allowed !important;

      .ant-upload-drag-icon .anticon,
      .ant-upload-text,
      .ant-upload-hint,
      .anticon-plus {
        color: var(--text-color-disabled) !important;
      }
    }
  }

  // Scrollbar styling for dark mode
  ::-webkit-scrollbar-thumb {
    background-color: var(--text-color-tertiary);
  }

  ::-webkit-scrollbar-track {
    background-color: var(--bg-secondary);
  }

  // Input group addon dark mode fixes - Professional, modern, smart and intuitive UI
  .ant-input-group-addon {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color-base) !important;
    color: var(--text-color) !important;
    transition: all 0.3s ease !important;

    &:hover {
      background-color: var(--item-hover-bg) !important;
      color: var(--text-color) !important;
    }

    &:focus {
      background-color: var(--bg-secondary) !important;
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px var(--primary-color-fade) !important;
    }
  }

  // Collapse component dark mode fixes - Professional, modern, smart and intuitive UI
  .ant-collapse {
    background: var(--component-background) !important;
    border-color: var(--border-color-base) !important;

    > .ant-collapse-item {
      border-color: var(--border-color-base) !important;

      > .ant-collapse-header {
        color: var(--text-color) !important;
        background: var(--component-background) !important;
        border-color: var(--border-color-base) !important;
        transition: all 0.3s ease !important;

        &:hover {
          background: var(--item-hover-bg) !important;
          color: var(--text-color) !important;
        }

        &:focus {
          background: var(--item-hover-bg) !important;
          color: var(--text-color) !important;
        }

        .ant-collapse-arrow {
          color: var(--text-color-secondary) !important;
          transition: color 0.3s ease !important;

          &:hover {
            color: var(--primary-color) !important;
          }
        }

        .ant-collapse-expand-icon {
          color: var(--text-color-secondary) !important;

          &:hover {
            color: var(--primary-color) !important;
          }
        }
      }

      &.ant-collapse-item-active {
        > .ant-collapse-header {
          color: var(--text-color) !important;
          background: var(--item-active-bg) !important;

          .ant-collapse-arrow {
            color: var(--primary-color) !important;
          }
        }
      }

      &.ant-collapse-item-disabled {
        > .ant-collapse-header {
          color: var(--text-color-disabled) !important;
          background: var(--bg-disabled) !important;
          cursor: not-allowed !important;

          .ant-collapse-arrow {
            color: var(--text-color-disabled) !important;
          }
        }
      }

      .ant-collapse-content {
        background: var(--component-background) !important;
        border-color: var(--border-color-base) !important;

        .ant-collapse-content-box {
          color: var(--text-color) !important;
        }
      }
    }

    // Borderless collapse variant
    &.ant-collapse-borderless {
      background: transparent !important;

      > .ant-collapse-item {
        border: none !important;

        > .ant-collapse-header {
          background: transparent !important;
        }

        .ant-collapse-content {
          background: transparent !important;
          border: none !important;
        }
      }
    }

    // Ghost collapse variant
    &.ant-collapse-ghost {
      background: transparent !important;

      > .ant-collapse-item {
        border: none !important;

        > .ant-collapse-header {
          background: transparent !important;
          padding-left: 0 !important;
          padding-right: 0 !important;
        }

        .ant-collapse-content {
          background: transparent !important;
          border: none !important;

          .ant-collapse-content-box {
            padding-left: 0 !important;
            padding-right: 0 !important;
          }
        }
      }
    }
  }

  // Ant Design tabs component dark mode fixes - Professional, modern, smart and intuitive UI
  .ant-tabs {
    color: var(--text-color) !important;
    transition: color 0.3s ease !important;

    // Tab navigation styling
    .ant-tabs-nav {
      border-color: var(--border-color-base) !important;

      &::before {
        border-color: var(--border-color-base) !important;
      }
    }

    // Individual tab styling
    .ant-tabs-tab {
      color: var(--text-color) !important;
      transition: color 0.3s ease, background-color 0.3s ease !important;

      &:hover {
        color: var(--primary-color) !important;
      }

      // Tab button content
      .ant-tabs-tab-btn {
        color: var(--text-color) !important;
        transition: color 0.3s ease !important;

        &:hover {
          color: var(--primary-color) !important;
        }
      }
    }

    // Active tab styling
    .ant-tabs-tab-active {
      color: var(--primary-color) !important;

      .ant-tabs-tab-btn {
        color: var(--primary-color) !important;
      }
    }

    // Tab ink bar (active indicator)
    .ant-tabs-ink-bar {
      background: var(--primary-color) !important;
    }

    // Tab content area
    .ant-tabs-content-holder {
      background: var(--component-background) !important;
    }

    // Tab pane content
    .ant-tabs-tabpane {
      color: var(--text-color) !important;
    }
  }
}
