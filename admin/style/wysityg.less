.editor {
  .wysiwyg-wrapper {
    width: 100% !important;
    display: block !important;
    margin-bottom: 25px !important;
    height: 400px !important;
  }
  .wysityg-editor {
    height: 275px !important;
    border: 1px solid var(--border-color-base, #f1f1f1) !important;
    padding: 5px !important;
    border-radius: 2px !important;
    background: var(--component-background, #fff) !important;
    color: var(--text-color, #000) !important;
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
  }
}

// Dark mode specific overrides for WYSIWYG editor
.dark-mode {
  .editor {
    .wysityg-editor {
      background: var(--component-background) !important;
      color: var(--text-color) !important;
      border-color: var(--border-color-base) !important;

      // Override any nested editor styles
      * {
        background-color: transparent !important;
        color: var(--text-color) !important;
      }

      // Toolbar styling
      .ql-toolbar {
        background: var(--bg-secondary) !important;
        border-color: var(--border-color-base) !important;

        .ql-stroke {
          stroke: var(--text-color) !important;
        }

        .ql-fill {
          fill: var(--text-color) !important;
        }

        button {
          color: var(--text-color) !important;

          &:hover {
            background: var(--item-hover-bg) !important;
          }
        }
      }

      // Content area
      .ql-container {
        background: var(--component-background) !important;
        color: var(--text-color) !important;
        border-color: var(--border-color-base) !important;
      }

      .ql-editor {
        background: var(--component-background) !important;
        color: var(--text-color) !important;

        &::before {
          color: var(--text-color-tertiary) !important;
        }
      }
    }
  }
}
