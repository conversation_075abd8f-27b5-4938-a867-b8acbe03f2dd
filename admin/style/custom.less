@import './default.less';

.text-center {
  text-align: center;
}

.avatar-uploader {
  float: left;
  width: unset;
}

.ant-form-item {
  margin-bottom: 5px;

  .ant-form-item-label {
    padding: 0 5px !important;

    label {
      font-size: 12px;
    }
  }
}

.ant-divider-horizontal.ant-divider-with-text {
  display: table;
}

.ant-upload-select.ant-upload-select-picture-card {
  border: 2px solid @light-color;
  border-radius: 50%;
  background-image: url('/placeholder-image.jpg');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50%;
  position: relative;

  img {
    margin-bottom: 10px;
    margin-bottom: 0;
    border-radius: 50%;
    width: 100%;
    height: 100%;
    max-width: 100px;
    max-height: 100px;
    object-fit: contain;
  }

  .anticon {
    font-size: 18px;
    padding: 8px;
    background-color: @theme-color;
    color: @white;
    border-radius: 50%;
    position: absolute;
    bottom: 15px;
    right: -5px;

    &.anticon-file-add {
      background-color: @success-color;
    }

    &.anticon-edit {
      padding: 0;
      background-color: transparent;
      border-radius: 0;
      position: relative;
      top: 0;
      left: 0;
    }
  }
}

.ant-image-mask-info {
  display: none;
}

.ant-menu-overflow-item {
  &::after {
    display: none;
  }
}

.ant-layout-header {
  height: 72px;
}

.ant-select-selector {
  .ant-select-selection-item {
    height: auto;
    line-height: normal;

    .ant-avatar {
      height: 28px;
      width: 28px;
    }
  }
}

.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
  min-height: 32px;
  height: unset;
}

.contentInner {
  background: var(--component-background, #fff);
  padding: 24px;
  box-shadow: var(--shadow-color-light, @shadow-1) 4px 4px 20px 0;
  min-height: ~'calc(100vh - 230px)';
  position: relative;
  color: var(--text-color, @text-color);
  border: 1px solid var(--border-color-split, transparent);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

@media (max-width: 767px) {
  .contentInner {
    padding: 12px;
    min-height: ~'calc(100vh - 160px)';
  }
}

.ant-table-pagination {
  float: none !important;
  display: table;
  margin: 16px auto !important;
}

// Dark mode specific overrides for custom components
.dark-mode {
  // Form styling
  .ant-form-item {
    .ant-form-item-label {
      label {
        color: var(--text-color);
      }
    }
  }

  // Upload components
  .ant-upload-select.ant-upload-select-picture-card {
    border-color: var(--border-color-base);
    background-color: var(--component-background);

    .anticon {
      background-color: var(--primary-color);
      color: var(--color-white);

      &.anticon-file-add {
        background-color: var(--success-color);
      }
    }
  }

  // Avatar styling
  .ant-select-selector {
    .ant-select-selection-item {
      .ant-avatar {
        border: 1px solid var(--border-color-base);
      }
    }
  }

  // Content inner styling
  .contentInner {
    background: var(--component-background);
    border-color: var(--border-color-base);
    box-shadow: var(--shadow-color-dark) 4px 4px 20px 0;
  }

  // Dashboard stats
  .dashboard-stats {
    .ant-card {
      &:hover {
        background-color: var(--item-hover-bg);
      }
    }
  }

  // Breadcrumb styling
  .ant-breadcrumb {
    & > span {
      &:last-child {
        color: var(--text-color-secondary);
      }
    }
  }

  // Table styling
  .ant-table {
    &.ant-table-small {
      .ant-table-thead > tr > th {
        background: var(--bg-secondary);
        color: var(--text-color);
      }
    }
  }

  // Popover styling
  .ant-popover-inner {
    background: var(--component-background);
    color: var(--text-color);
    border: 1px solid var(--border-color-base);
    box-shadow: var(--shadow-color-dark) 0 0 20px;
  }

  .ant-popover-arrow {
    border-color: var(--border-color-base);
  }

  // Modal styling
  .ant-modal-mask {
    background-color: rgba(0, 0, 0, 0.6);
  }

  .ant-modal-content {
    background: var(--component-background);
    color: var(--text-color);
  }

  // Select dropdown styling
  .ant-select-dropdown-menu-item {
    color: var(--text-color);

    &:hover {
      background: var(--item-hover-bg);
    }

    &.ant-select-dropdown-menu-item-selected {
      background: var(--item-active-bg);
      color: var(--primary-color);
    }
  }

  // Text styling
  .text-center {
    color: var(--text-color);
  }

  // Avatar uploader
  .avatar-uploader {
    .ant-upload-select.ant-upload-select-picture-card {
      border-color: var(--border-color-base);
      background-color: var(--component-background);
    }
  }
}
