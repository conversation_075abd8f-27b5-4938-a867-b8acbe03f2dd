// Light theme variables (default)
:root,
.light-theme,
.light-mode {
  // Background colors
  --bg-white: #fff;
  --bg-primary: #7b5dbd;
  --bg-secondary: #f8f8f8;
  --bg-panel-white: #fff;
  --bg-card: #fff;
  --bg-hover: rgba(0, 0, 0, 0.04);
  --bg-active: rgba(0, 0, 0, 0.06);
  --bg-disabled: #f5f5f5;
  
  // Text colors
  --color-white: #fff;
  --text-color: #666;
  --text-color-secondary: #999;
  --text-color-tertiary: #bfbfbf;
  --text-color-disabled: #d9d9d9;
  --text-color-inverse: #fff;
  
  // Border colors
  --border-color-base: #e5e5e5;
  --border-color-split: #f4f4f4;
  --border-color-light: #ddd;
  
  // Component colors
  --component-background: #fff;
  --item-hover-bg: rgba(0, 0, 0, 0.04);
  --item-active-bg: rgba(0, 0, 0, 0.06);
  
  // Shadow colors
  --shadow-color: rgba(0, 0, 0, 0.05);
  --shadow-color-light: rgba(0, 0, 0, 0.02);
  --shadow-color-dark: rgba(0, 0, 0, 0.15);
  
  // Status colors
  --primary-color: #7b5dbd;
  --primary-color-fade: rgba(123, 93, 189, 0.2);
  --success-color: #00c12c;
  --error-color: #f04134;
  --warning-color: #ffbf00;
  --info-color: #7b5dbd;
  
  // Theme mode indicator
  --theme-mode: 'light';
}

// Dark theme variables
.dark-theme,
.dark-mode {
  // Background colors
  --bg-white: #141414;
  --bg-primary: #7b5dbd;
  --bg-secondary: #1f1f1f;
  --bg-panel-white: #1f1f1f;
  --bg-card: #1f1f1f;
  --bg-hover: rgba(255, 255, 255, 0.08);
  --bg-active: rgba(255, 255, 255, 0.12);
  --bg-disabled: #262626;
  
  // Text colors
  --color-white: #fff;
  --text-color: rgba(255, 255, 255, 0.85);
  --text-color-secondary: rgba(255, 255, 255, 0.65);
  --text-color-tertiary: rgba(255, 255, 255, 0.45);
  --text-color-disabled: rgba(255, 255, 255, 0.25);
  --text-color-inverse: #000;
  
  // Border colors
  --border-color-base: #434343;
  --border-color-split: #303030;
  --border-color-light: #434343;
  
  // Component colors
  --component-background: #1f1f1f;
  --item-hover-bg: rgba(255, 255, 255, 0.08);
  --item-active-bg: rgba(255, 255, 255, 0.12);
  
  // Shadow colors
  --shadow-color: rgba(0, 0, 0, 0.45);
  --shadow-color-light: rgba(0, 0, 0, 0.25);
  --shadow-color-dark: rgba(0, 0, 0, 0.65);
  
  // Status colors
  --primary-color: #7b5dbd;
  --primary-color-fade: rgba(123, 93, 189, 0.3);
  --success-color: #00c12c;
  --error-color: #f04134;
  --warning-color: #ffbf00;
  --info-color: #7b5dbd;
  
  // Theme mode indicator
  --theme-mode: 'dark';
}

// Smooth transitions for theme changes
.theme-transition {
  * {
    transition: background-color 0.3s ease, 
                color 0.3s ease, 
                border-color 0.3s ease,
                box-shadow 0.3s ease !important;
  }
}
