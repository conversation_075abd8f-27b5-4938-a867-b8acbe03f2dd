import { formatDate } from '@lib/date';
import { Table, Tag } from 'antd';
import { IReferral } from 'pages/referrals';

interface IProps {
  referrals:IReferral;
  total:number;
  onChange:Function;
  currentPage:number;
  loading:boolean;
  pageSize:number
}
export function ReferralList({
  referrals, total, onChange, currentPage, loading, pageSize
}:IProps) {
  const columns = [
    {
      title: 'Inviter Name',
      dataIndex: 'inviterUsername'
    },
    {
      title: 'Inviter Role',
      dataIndex: 'inviterRole',
      render(invitedRole) {
        switch (invitedRole) {
          case 'user': case 'success':
            return <Tag color="green">{invitedRole}</Tag>;
          case 'performer':
            return <Tag color="blue">{invitedRole}</Tag>;
          default:
            return <Tag color="default">{invitedRole}</Tag>;
        }
      }
    },
    {
      title: 'Invited Name',
      dataIndex: 'invitedUsername'
    },
    {
      title: 'Invited Role',
      dataIndex: 'invitedRole',
      render(invitedRole) {
        switch (invitedRole) {
          case 'user': case 'success':
            return <Tag color="green">{invitedRole}</Tag>;
          case 'performer':
            return <Tag color="blue">{invitedRole}</Tag>;
          default:
            return <Tag color="default">{invitedRole}</Tag>;
        }
      }
    },
    {
      title: 'Update On',
      dataIndex: 'createdAt',
      render(createdAt) {
        return <span>{formatDate(createdAt)}</span>;
      }
    }
  ];
  return (
    <div className="table-responsive">
      <Table
        dataSource={referrals as any}
        columns={columns}
        loading={loading}
        onChange={onChange.bind(this)}
        scroll={{ x: true }}
        pagination={{
          current: currentPage,
          pageSize,
          total
        }}
      />
    </div>
  );
}
