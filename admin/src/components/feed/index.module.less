@import '../../../style/vars.less';

.f-upload-list {
  display: flex;
  width: 100%;
  overflow: auto;
  flex-wrap: wrap;

  :global .f-upload-item {
    border-radius: 5px;
    overflow: hidden;
    padding: 5px;
    margin: 5px;
    background-color: var(--bg-secondary, @light-blue);
    position: relative;
    border: 1px solid var(--border-color-split, transparent);
    transition: background-color 0.3s ease, border-color 0.3s ease;

    .f-remove {
      position: absolute;
      right: 5px;
      top: 5px;
      color: var(--text-color, @text-color);
      transition: color 0.3s ease;
    }

    .f-upload-thumb {
      height: 100px;
      width: 120px;

      .f-thumb-vid {
        position: relative;
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: var(--component-background, @white);
        transition: background-color 0.3s ease;

        .anticon {
          color: var(--primary-color, @theme-color);
          font-size: 22px;
          transition: color 0.3s ease;
        }
      }
    }

    .f-upload-name {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      width: 120px;
      color: var(--text-color, @text-color);
      transition: color 0.3s ease;
    }

    .f-upload-size {
      font-size: 8px;
      color: var(--text-color-secondary, @text-color);
      transition: color 0.3s ease;
    }
  }

  :global .add-more {
    display: flex;
    height: 130px;
    align-self: center;
    justify-content: center;
    width: 65px;
    background-color: var(--bg-secondary, @light-grey);
    border-radius: 5px;
    border: 1px solid var(--border-color-base, transparent);
    transition: background-color 0.3s ease, border-color 0.3s ease, opacity 0.3s ease;

    &:hover {
      opacity: 0.8;
      background-color: var(--item-hover-bg, rgba(0, 0, 0, 0.04));
    }

    cursor: pointer;

    .ant-upload {
      height: 130px;
      width: 65px;
      display: flex;
      align-items: center;
      justify-content: center;

      .anticon.anticon-plus {
        font-size: 20px;
        color: var(--text-color-secondary, @text-color);
        transition: color 0.3s ease;
      }
    }
  }
}

.feed-form {
  :global .feed-input {
    background: var(--component-background, @white);
    color: var(--text-color, @text-color);
    border-color: var(--border-color-base, @border-color-base);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;

    &:focus {
      background-color: var(--bg-secondary, @light-blue);
      border-color: var(--primary-color, @primary-color);
    }
  }

  :global .ant-input-number-input-wrap input {
    height: 30px;
    background: var(--component-background, @white);
    color: var(--text-color, @text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
  }
}

.poll-form {
  width: 100%;
  max-width: 320px;
  padding: 5px;
  border-radius: 5px;
  background-color: var(--bg-secondary, @light-blue);
  margin: 10px 0;
  border: 1px solid var(--border-color-split, transparent);
  transition: background-color 0.3s ease, border-color 0.3s ease;

  :global .poll-top {
    padding: 5px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      cursor: pointer;
      color: var(--text-color, @text-color);
      transition: color 0.3s ease;

      &:hover {
        color: var(--primary-color, @primary-color);
      }
    }

    a {
      color: var(--color-white, @white);
      padding: 1px 7px;
      border-radius: 50%;
      background-color: var(--primary-color, @theme-color);
      transition: background-color 0.3s ease, color 0.3s ease;
    }
  }

  :global .poll-input {
    margin-bottom: 10px;

    .ant-input {
      background: var(--component-background, @white);
      color: var(--text-color, @text-color);
      border-color: var(--border-color-base, @border-color-base);
      transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    }
  }
}
.feed-polls {
  .feed-poll {
    display: flex;
    background-color: var(--bg-secondary, @light-grey);
    padding: 10px 0 0 0;
    border-radius: 3px;
    justify-content: space-between;
    cursor: pointer;
    margin: 3px 0;
    border: 1px solid var(--border-color-split, transparent);
    color: var(--text-color, @text-color);
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, opacity 0.3s ease;

    &:hover {
      opacity: 0.8;
      background-color: var(--item-hover-bg, rgba(0, 0, 0, 0.04));
    }
  }
}
.total-vote {
  display: flex;
  width: 100%;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 10px;
  color: var(--text-color-secondary, @gray);
  transition: color 0.3s ease;
}

.submit-btns {
  justify-content: space-around;
  margin: 20px auto;
  width: 200px;
  display: flex;
}
