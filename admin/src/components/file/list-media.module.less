@import '../../../style/vars.less';

.f-upload-list {
  display: flex;
  width: 100%;
  overflow: auto;
  flex-wrap: wrap;

  :global .f-upload-item {
    border-radius: 5px;
    overflow: hidden;
    padding: 5px;
    margin: 5px;
    background-color: var(--bg-secondary, @light-blue);
    position: relative;
    border: 1px solid var(--border-color-split, transparent);
    transition: background-color 0.3s ease, border-color 0.3s ease;

    .f-remove {
      position: absolute;
      right: 5px;
      top: 5px;
      color: var(--text-color, @text-color);
      transition: color 0.3s ease;
    }

    .f-upload-thumb {
      height: 100px;
      width: 120px;

      .f-thumb-vid {
        position: relative;
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: var(--component-background, @white);
        transition: background-color 0.3s ease;

        .anticon {
          color: var(--primary-color, @theme-color);
          font-size: 22px;
          transition: color 0.3s ease;
        }
      }
    }

    .f-upload-name {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      width: 120px;
      color: var(--text-color, @text-color);
      transition: color 0.3s ease;
    }

    .f-upload-size {
      font-size: 8px;
      color: var(--text-color-secondary, @text-color);
      transition: color 0.3s ease;
    }
  }

  :global .add-more {
    display: flex;
    height: 130px;
    align-self: center;
    justify-content: center;
    width: 65px;
    background-color: var(--bg-secondary, @light-grey);
    border-radius: 5px;
    border: 1px solid var(--border-color-base, transparent);
    transition: background-color 0.3s ease, border-color 0.3s ease, opacity 0.3s ease;

    &:hover {
      opacity: 0.8;
      background-color: var(--item-hover-bg, rgba(0, 0, 0, 0.04));
    }

    cursor: pointer;

    .ant-upload {
      height: 130px;
      width: 65px;
      display: flex;
      align-items: center;
      justify-content: center;

      .anticon {
        font-size: 20px;
        color: var(--text-color-secondary, @text-color);
        transition: color 0.3s ease;
      }
    }
  }
}

.photo-upload-list {
  .ant-image {
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
