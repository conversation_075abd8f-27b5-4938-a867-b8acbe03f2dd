@import '../../../style/vars.less';

.ant-upload-select.ant-upload-select-picture-card {
  border-radius: 50%;
  border: 2px solid var(--border-color-base, @light-color);
  background-image: url('/placeholder-image.jpg');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50%;
  position: relative;
  background-color: var(--component-background, @white);
  transition: border-color 0.3s ease, background-color 0.3s ease;

  img {
    margin-bottom: 10px;
    border-radius: 50%;
    margin-bottom: 0;
  }

  .anticon {
    font-size: 18px;
    padding: 8px;
    background-color: var(--primary-color, @theme-color);
    color: var(--color-white, @white);
    border-radius: 50%;
    position: absolute;
    bottom: 15px;
    right: -5px;
    transition: background-color 0.3s ease, color 0.3s ease;

    &.anticon-edit {
      padding: 0;
      background-color: transparent;
      border-radius: 0;
      position: relative;
      top: 0;
      left: 0;
      color: var(--text-color, @text-color);
    }
  }
}
