import { useEffect, useState } from 'react';
import { Button, Col, Form, InputNumber, message, Row } from 'antd';

import { settingService } from '@services/setting.service';
import { IPerformer } from 'src/interfaces';

const layout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 24 }
};

const validateMessages = {
  required: 'This field is required!'
};

type IProps = {
  onFinish: Function;
  performer: IPerformer;
  submiting?: boolean;
};

type SubscriptionSettings = {
  minMonthSubscriptionPrice: number;
  maxMonthSubscriptionPrice: number;
  minAnnualSubscriptionPrice: number;
  maxAnnualSubscriptionPrice: number;
};

export function SubscriptionForm({ performer, onFinish, submiting = false }: IProps) {
  const [subscriptionSettings, setSubscriptionSettings] = useState<SubscriptionSettings>();

  const loadSettings = async () => {
    const resp = (await settingService.all('pricing')) as any;
    if (resp?.status === 0 && resp?.data?.length) {
      const settings = resp.data.reduce((acc: SubscriptionSettings, setting: any) => {
        if (
          setting.key in
          {
            minMonthSubscriptionPrice: true,
            maxMonthSubscriptionPrice: true,
            minAnnualSubscriptionPrice: true,
            maxAnnualSubscriptionPrice: true
          }
        ) {
          acc[setting.key] = Number(setting.value);
        }
        return acc;
      }, {} as SubscriptionSettings);

      setSubscriptionSettings(settings);
    }
  };

  useEffect(() => {
    loadSettings();
  }, []);

  return (
    <Form
      {...layout}
      name="form-performer"
      onFinish={onFinish.bind(this)}
      onFinishFailed={() => message.error('Please complete the required fields')}
      validateMessages={validateMessages}
      initialValues={
        performer || {
          yearlyPrice: 99.99,
          monthlyPrice: 9.99
        }
      }>
      <Row>
        <Col xs={24} md={12}>
          <Form.Item
            key="yearly"
            name="yearlyPrice"
            label="Yearly Subscription Price in $"
            rules={[{ required: true }]}>
            <InputNumber
              min={subscriptionSettings?.minAnnualSubscriptionPrice || 1}
              max={subscriptionSettings?.maxAnnualSubscriptionPrice}
            />
          </Form.Item>
          <Form.Item
            key="monthly"
            name="monthlyPrice"
            label="Monthly Subscription Price in $"
            rules={[{ required: true }]}>
            <InputNumber
              min={subscriptionSettings?.minMonthSubscriptionPrice || 1}
              max={subscriptionSettings?.maxMonthSubscriptionPrice}
            />
          </Form.Item>
        </Col>
      </Row>
      <Form.Item className="text-center">
        <Button type="primary" htmlType="submit" disabled={submiting} loading={submiting}>
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
}

export default SubscriptionForm;
