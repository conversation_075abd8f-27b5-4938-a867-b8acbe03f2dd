/* eslint-disable indent */
/* eslint-disable prefer-regex-literals */
import { SelectCategoryDropdown } from '@components/category/select-category-dropdown';
import { AvatarUpload } from '@components/user/avatar-upload';
import { CoverUpload } from '@components/user/cover-upload';
import { authService, performerService, utilsService } from '@services/index';
import {
  Button, Col, DatePicker, Form, Input, message, Row, Select, Switch
} from 'antd';
import { FormInstance } from 'antd/lib/form';
// import Router from 'next/router';
import moment from 'moment';
import { createRef, PureComponent } from 'react';
import {
  IBody, ICountry, ILangguges, IPerformer, IPhoneCodes
} from 'src/interfaces';

import style from './accountForm.module.less';

const layout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 24 }
};

const { TextArea } = Input;
const { Option } = Select;

type IProps = {
  onFinish: Function;
  onUploaded: Function;
  performer?: IPerformer;
  submiting: boolean;
  countries: ICountry[];
  languages: ILangguges[];
  phoneCodes: IPhoneCodes[];
  avatarUrl?: string;
  coverUrl?: string;
  bodyInfo: IBody;
};

export class AccountForm extends PureComponent<IProps> {
  state = {
    selectedPhoneCode: 'US_+1',
    states: [],
    cities: [],
    phone: null
  };

  // eslint-disable-next-line react/static-property-placement
  static defaultProps = {
    performer: undefined,
    avatarUrl: null,
    coverUrl: null
  };

  formRef: any;

  componentDidMount() {
    const { performer } = this.props;
    if (performer?.country) {
      this.handleGetStates(performer?.country);
      if (performer?.state) {
        this.handleGetCities(performer?.state, performer?.country);
      }
    }
    if (performer?.phone) {
      this.setState({ phone: performer.phone });
    }
  }

  handleGetStates = async (countryCode: string) => {
    const { performer } = this.props;
    const resp = await utilsService.statesList(countryCode);
    const eState = resp.data.find((s) => s === performer?.state);
    await this.setState({ states: resp.data });
    if (eState) {
      this.formRef.setFieldsValue({ state: eState });
    } else {
      this.formRef.setFieldsValue({ state: '', city: '' });
    }
  };

  setFormVal(field: string, val: any) {
    const instance = this.formRef as FormInstance;
    instance.setFieldsValue({
      [field]: val
    });
  }

  handleGetCities = async (state: string, countryCode: string) => {
    const { performer } = this.props;
    const resp = await utilsService.citiesList(countryCode, state);
    await this.setState({ cities: resp.data });
    const eCity = resp.data.find((s) => s === performer?.city);
    if (eCity) {
      this.formRef.setFieldsValue({ city: eCity });
    } else {
      this.formRef.setFieldsValue({ city: '' });
    }
  };

  render() {
    if (!this.formRef) this.formRef = createRef();
    const {
      performer,
      onFinish,
      submiting,
      countries,
      onUploaded,
      avatarUrl,
      coverUrl,
      phoneCodes,
      bodyInfo
    } = this.props;
    const {
      genders = []
    } = bodyInfo;
    const {
      selectedPhoneCode, cities, states, phone
    } = this.state;
    const uploadHeaders = {
      authorization: authService.getToken()
    };
    return (
      <Form
        {...layout}
        name="form-performer"
        onFinish={(payload) => {
          const values = payload;
          values.phoneCode = selectedPhoneCode;
          onFinish(values);
        }}
        onFinishFailed={() => message.error('Please complete the required fields')}
        initialValues={
          performer
            ? {
              ...performer,
              dateOfBirth: performer.dateOfBirth ? moment(performer.dateOfBirth) : ''
            }
            : {
              country: 'US',
              status: 'active',
              gender: 'male',
              languages: ['en'],
              verifiedEmail: false,
              verifiedAccount: false,
              verifiedDocument: false
            }
        }
        ref={(ref) => {
          this.formRef = ref;
        }}
      >
        <Row>
          <Col xs={24} md={24}>
            <div
              className={style['top-profile']}
              style={{
                position: 'relative',
                marginBottom: 25,
                backgroundImage: coverUrl ? `url('${coverUrl}')` : "url('/banner-image.jpg')"
              }}
            >
              <div className="avatar-upload">
                <AvatarUpload
                  headers={uploadHeaders}
                  uploadUrl={performerService.getAvatarUploadUrl()}
                  onUploaded={onUploaded.bind(this, 'avatarId')}
                  image={avatarUrl}
                />
              </div>
              <div className="cover-upload">
                <CoverUpload
                  options={{ fieldName: 'cover' }}
                  headers={uploadHeaders}
                  uploadUrl={performerService.getCoverUploadUrl()}
                  onUploaded={onUploaded.bind(this, 'coverId')}
                />
              </div>
            </div>
          </Col>
          <Col xs={12} md={12}>
            <Form.Item
              name="firstName"
              label="First Name"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                { required: true, message: 'Please input your first name!' },
                {
                  pattern: new RegExp(
                    /^[a-zA-ZàáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð ,.'-]+$/u
                  ),
                  message: 'First name can not contain number and special character'
                }
              ]}
            >
              <Input placeholder="First Name" />
            </Form.Item>
          </Col>
          <Col xs={12} md={12}>
            <Form.Item
              name="lastName"
              label="Last Name"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                { required: true, message: 'Please input your last name!' },
                {
                  pattern: new RegExp(
                    /^[a-zA-ZàáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð ,.'-]+$/u
                  ),
                  message: 'Last name can not contain number and special character'
                }
              ]}
            >
              <Input placeholder="Last Name" />
            </Form.Item>
          </Col>
          <Col xs={12} md={12}>
            <Form.Item
              name="name"
              label="Display name"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                { required: true, message: 'Please input your display name!' },
                {
                  pattern: new RegExp(/^(?=.*\S).+$/g),
                  message: 'Display name can not contain only whitespace'
                }
              ]}
              hasFeedback
            >
              <Input placeholder="Display name" />
            </Form.Item>
          </Col>
          <Col xs={12} md={12}>
            <Form.Item
              name="username"
              label="Username"
              rules={[
                { required: true },
                {
                  pattern: new RegExp(/^[a-zA-Z0-9]+$/g),
                  message: 'Username must contain only alphanumerics'
                },
                { min: 3 }
              ]}
            >
              <Input placeholder="Unique, alphanumeric only" />
            </Form.Item>
          </Col>
          <Col xs={12} md={12}>
            <Form.Item name="email" label="Email" rules={[{ type: 'email', required: true }]}>
              <Input placeholder="Email address" />
            </Form.Item>
          </Col>
          <Col xs={12} md={12}>
            <Form.Item name="gender" label="Gender" rules={[{ required: true }]}>
              <Select>
                {genders.map((g) => (
                  <Select.Option key={g.value} value={g.value}>
                    {g.text}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} md={24}>
            <Form.Item name="bio" label="Bio">
              <TextArea rows={3} minLength={1} />
            </Form.Item>
          </Col>
          {!performer && [
            <Col xs={12} md={12}>
              <Form.Item key="password" name="password" label="Password" rules={[{ required: true }, { min: 8 }]}>
                <Input.Password placeholder="Password" />
              </Form.Item>
            </Col>,
            <Col xs={12} md={12}>
              <Form.Item
                key="rePassword"
                name="rePassword"
                label="Confirm password"
                rules={[
                  {
                    required: true,
                    message: 'Please confirm your password!'
                  },
                  ({ getFieldValue }) => ({
                    validator(rule, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      // eslint-disable-next-line prefer-promise-reject-errors
                      return Promise.reject('Passwords do not match together!');
                    }
                  })
                ]}
              >
                <Input.Password placeholder="Confirm password" />
              </Form.Item>
            </Col>
          ]}
          <Col md={12} xs={12}>
            <Form.Item name="dateOfBirth" label="Date of Birth" validateTrigger={['onChange', 'onBlur']}>
              <DatePicker
                placeholder="YYYY-MM-DD"
                disabledDate={(currentDate) => currentDate && currentDate > moment().subtract(12, 'year').endOf('day')}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          <Col xs={12} md={12}>
            <Form.Item
              name="phone"
              label="Phone Number"
              rules={[
                {
                  pattern: new RegExp(/^[0-9]{9,12}$/),
                  message: 'Enter 9-12 digits phone number'
                }
              ]}
            >
              <div className={style['phone-code']}>
                <Input
                  placeholder="9-12 digits phone number"
                  addonBefore={(
                    <Select
                      style={{ minWidth: 300 }}
                      defaultValue={performer?.phoneCode || 'US_+1'}
                      optionFilterProp="label"
                      showSearch
                      onChange={(val) => this.setState({ selectedPhoneCode: val })}
                    >
                      {phoneCodes
                        && phoneCodes.map((p) => (
                          <Option
                            key={p.code}
                            value={p.code}
                            label={`${p.dialCode} ${p.name}`}
                          >
                            {`${p.dialCode} ${p.name}`}
                          </Option>
                        ))}
                    </Select>
                  )}
                  value={phone}
                  onChange={(e) => this.setState({ phone: e.target.value })}
                  style={{ width: '100%' }}
                />
              </div>
            </Form.Item>
          </Col>
          {/* <Col xs={24} md={24}>
            <Form.Item name="languages" label="Languages">
              <Select mode="multiple">
                {languages.map((l) => (
                  <Select.Option key={l.code} value={l.name || l.code}>
                    {l.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col> */}
          <Col md={12} sm={12} xs={12}>
            <Form.Item
              name="country"
              rules={[{ required: true, message: 'Please select your country' }]}
              label="Country"
            >
              <Select
                placeholder="Select your country"
                optionFilterProp="label"
                showSearch
                onChange={(val: string) => this.handleGetStates(val)}
              >
                {countries.map((c) => (
                  <Option value={c.code} label={c.name} key={c.code}>
                    <img alt="country_flag" src={c.flag} width="25px" />
                    {' '}
                    {c.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col md={12} sm={12} xs={12}>
            <Form.Item name="state" label="State">
              <Select
                placeholder="Select your state"
                optionFilterProp="label"
                showSearch
                onChange={(val: string) => this.handleGetCities(val, this.formRef.getFieldValue('country'))}
              >
                {states.map((state) => (
                  <Option value={state} label={state} key={state}>
                    {state}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col md={12} sm={12} xs={12}>
            <Form.Item name="city" label="City">
              <Select placeholder="Select your city" optionFilterProp="label" showSearch>
                {cities.map((city) => (
                  <Option value={city} label={city} key={city}>
                    {city}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col xs={12} md={12}>
            <Form.Item name="address" label="Address">
              <Input />
            </Form.Item>
          </Col>
          <Col xs={12} md={12}>
            <Form.Item name="zipcode" label="Zipcode">
              <Input />
            </Form.Item>
          </Col>
          {/* <Col xs={12} md={12}>
            <Form.Item name="sexualPreference" label="Sexual orientation">
              <Select>
                {sexualOrientations.map((g) => (
                  <Select.Option key={g.value} value={g.value}>
                    {g.text}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col> */}
          {/* <Col xs={12} md={12}>
            <Form.Item name="ethnicity" label="Ethnicity">
              <Select>
                {ethnicities.map((g) => (
                  <Select.Option key={g.value} value={g.value}>
                    {g.text}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col> */}
          {/* <Col xs={12} md={12}>
            <Form.Item name="bodyType" label="Body Type">
              <Select>
                {bodyTypes.map((g) => (
                  <Select.Option key={g.value} value={g.value}>
                    {g.text}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col> */}
          {/* <Col xs={12} md={12}>
            <Form.Item name="eyes" label="Eye color">
              <Select>
                {eyes.map((g) => (
                  <Select.Option key={g.value} value={g.value}>
                    {g.text}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col> */}
          {/* <Col xs={12} md={12}>
            <Form.Item name="hair" label="Hair color">
              <Select>
                {hairs.map((g) => (
                  <Select.Option key={g.value} value={g.value}>
                    {g.text}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col> */}
          {/* <Col xs={12} md={12}>
            <Form.Item name="height" label="Height">
              <Select showSearch>
                {heights.map((h) => (
                  <Option key={h.text} value={h.text}>
                    {h.text}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col> */}
          {/* <Col xs={12} md={12}>
            <Form.Item name="weight" label="Weight">
              <Select showSearch>
                {weights.map((w) => (
                  <Option key={w.text} value={w.text}>
                    {w.text}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col> */}
          {/* <Col xs={12} md={12}>
            <Form.Item name="butt" label="Butt size">
              <Select>
                {butts.map((g) => (
                  <Select.Option key={g.value} value={g.value}>
                    {g.text}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col> */}
          {/* <Col xs={12} md={12}>
            <Form.Item name="pubicHair" label="Pubic hair">
              <Select>
                {pubicHairs.map((g) => (
                  <Select.Option key={g.value} value={g.value}>
                    {g.text}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col> */}
          <Col md={24} xs={24}>
            <Form.Item label="Categories" name="categoryIds">
              <SelectCategoryDropdown
                noEmtpy
                defaultValue={performer && performer.categoryIds}
                group="performer"
                onSelect={(val) => this.setFormVal('categoryIds', val)}
                isMultiple
              />
            </Form.Item>
          </Col>
          <Col xs={8} md={8}>
            <Form.Item
              name="verifiedEmail"
              label="Verified Email?"
              valuePropName="checked"
              help="Turn on if email account verified"
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col xs={8} md={8}>
            <Form.Item
              name="verifiedDocument"
              label="Verified ID Documents?"
              valuePropName="checked"
              help="Accept to make the creator visible to users"
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col xs={8} md={8}>
            <Form.Item
              name="verifiedAccount"
              label="Verified Account?"
              valuePropName="checked"
              help="Display verification tick beside creator name"
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col xs={12} md={12}>
            <Form.Item
              name="inovioMonthlySubscriptionProductId"
              label="Inovio monthly subscription product ID"
              validateTrigger={['onChange', 'onBlur']}
              help="Check with Inovio support and create product for this creator with monthly subscription price and update here."
            >
              <Input placeholder="Inovio Monthly Subscription ProductId" />
            </Form.Item>
          </Col>
          <Col xs={12} md={12}>
            <Form.Item
              name="inovioYearlySubscriptionProductId"
              label="Inovio yearly subscription product ID"
              validateTrigger={['onChange', 'onBlur']}
              help="Check with Inovio support and create product for this creator with yearly subscription price and update here."
            >
              <Input placeholder="Inovio Yearly Subscription ProductId" />
            </Form.Item>
          </Col>
          <Col xs={24} md={24}>
            <Form.Item name="status" label="Status" rules={[{ required: true }]}>
              <Select>
                <Select.Option key="active" value="active">
                  Active
                </Select.Option>
                <Select.Option key="inactive" value="inactive">
                  Inactive
                </Select.Option>
                <Select.Option key="pending-email-confirmation" value="pending-email-confirmation" disabled>
                  Pending email confirmation
                </Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} md={24}>
            <Form.Item className="text-center">
              <Button type="primary" htmlType="submit" disabled={submiting} loading={submiting}>
                Submit
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  }
}
