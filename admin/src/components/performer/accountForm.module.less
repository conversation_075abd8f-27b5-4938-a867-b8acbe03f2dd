@import '../../../style/vars.less';

.top-profile {
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  // height: 25vh;
  padding-top: 30%;
  background-color: var(--bg-secondary, @light-blue);
  border: 1px solid var(--border-color-split, transparent);
  transition: background-color 0.3s ease, border-color 0.3s ease;

  :global .avatar-upload {
    position: absolute;
    left: 5%;
    bottom: -30px;

    .ant-upload-select.ant-upload-select-picture-card {
      background-image: url('/no-avatar.png');
    }
  }

  :global .cover-upload {
    float: right;
    margin-top: 5px;

    .ant-upload-select.ant-upload-select-picture-card {
      border-radius: 0;
      border: 0;
      border-radius: 3px;
      border: 0;
      background: @secondary-color;
      width: auto;
      height: auto;
      padding: 5px 15px;

      .ant-upload {
        color: @white;
      }
    }
  }

  @media screen and (max-width: @tablet-screen) {
    height: 18vh;
  }
}

.phone-code {
  :global .ant-select-selection-item,
  :global .ant-select-selector {
    min-width: 160px;
    max-width: 200px;
  }
}
