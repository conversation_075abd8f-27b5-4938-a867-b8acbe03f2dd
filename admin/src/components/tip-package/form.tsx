import { getResponseError } from '@lib/utils';
import { tipPackageService } from '@services/tip-package.service';
import {
    Button,
    Col, Form, Input, message, Row
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { useEffect } from 'react';
interface IProps {
    tipList: number[]
    minTipAmount: number;
    maxTipAmount: number;
}
function TipPackageForm({ tipList, minTipAmount, maxTipAmount }: IProps) {
    const [form] = useForm();
    const onFinish = async (values) => {
        try {
            const payload = Array.from(new Map(Object.entries(values)).values());
            await tipPackageService.update(payload as number[]);
            message.success('Updated Successfully!');
        } catch (error) {
            message.error(getResponseError(error) || 'Update failed!');
        }
    };
    useEffect(() => {
        tipList.map((number, index) => form.setFieldValue(`v-${index}`, number));
    }, []);
    return (
        <Form form={form} layout="vertical" onFinish={onFinish}>
            <Row gutter={16}>
                {tipList.map((num, index) => (
                    <Col span={8} key={num}>
                        {' '}
                        <Form.Item
                            name={`v-${index}`}
                            initialValue={num}
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter number'
                                },
                                {
                                    validator: (_, value) => {
                                        if (Number(value) === 0 || (Number(value) >= minTipAmount && Number(value) <= maxTipAmount)) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject(`Value must be 0 (skip) or greater ${minTipAmount} and less than ${maxTipAmount}`);
                                    }
                                }
                            ]}
                        >
                            <Input type="number" />
                        </Form.Item>
                    </Col>
                ))}
            </Row>
            <Form.Item>
                <Button type="primary" htmlType="submit">
                    Submit
                </Button>
            </Form.Item>
        </Form>
    );
}
export default TipPackageForm;