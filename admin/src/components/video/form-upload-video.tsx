/* eslint-disable jsx-a11y/label-has-associated-control */
import { CameraOutlined, FileAddOutlined, VideoCameraAddOutlined } from '@ant-design/icons';
import { SelectPerformerDropdown } from '@components/performer/common/select-performer-dropdown';
import { performerService } from '@services/index';
import {
  Avatar,
  Button, Col, DatePicker,
  Form, Input, InputNumber, message, Progress, Row, Select, Switch, Upload
} from 'antd';
import { FormInstance } from 'antd/lib/form';
import ImgCrop from 'antd-img-crop';
import { debounce } from 'lodash';
import moment from 'moment';
import getConfig from 'next/config';
import { createRef, PureComponent } from 'react';
import { IVideo } from 'src/interfaces';

interface IProps {
  video?: IVideo;
  submit: Function;
  beforeUpload: Function;
  uploading: boolean;
  uploadPercentage: number;
}

const layout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 24 }
};

export class FormUploadVideo extends PureComponent<IProps> {
  state = {
    previewThumbnail: null,
    previewVideo: null,
    previewTeaserVideo: null,
    isSaleVideo: false,
    isSchedule: false,
    scheduledAt: moment().add(1, 'day'),
    selectedVideo: null,
    selectedThumbnail: null,
    selectedTeaser: null,
    firstLoadPerformer: false,
    performers: [],
    searchValue: ''
  };

  // eslint-disable-next-line react/static-property-placement
  static defaultProps = {
    video: undefined
  };

  formRef: any;

  componentDidMount() {
    if (!this.formRef) this.formRef = createRef();
    const { video } = this.props;
    if (video) {
      this.setState(
        {
          previewThumbnail: video?.thumbnail?.url || video?.thumbnail?.thumbnails[0] || '',
          previewVideo: video?.video?.url || '',
          isSaleVideo: video.isSaleVideo,
          previewTeaserVideo: video?.teaser?.url || '',
          isSchedule: video.isSchedule,
          scheduledAt: video.scheduledAt || moment().add(1, 'day')
        }
      );
    }
    this.getPerformers('', video?.participantIds || '');
  }

  getPerformers = debounce(async (q = '', performerIds = '') => {
    try {
      const resp = await (await performerService.search({ q, performerIds, limit: 500 })).data;
      const performers = resp.data || [];
      this.setState({ performers, firstLoadPerformer: true });
    } catch (e) {
      const err = await e;
      message.error(err?.message || 'An error occurred, please try again!');
      this.setState({ firstLoadPerformer: true });
    }
  }, 500);

  setFormVal(field: string, val: any) {
    const instance = this.formRef.current as FormInstance;
    instance.setFieldsValue({
      [field]: val
    });
  }

  onSearchParticipants = (q: string) => {
    this.setState({ searchValue: q });
    this.getPerformers(q);
  };

  clearSearchParticipants = () => {
    this.setState({ searchValue: '' });
    this.getPerformers();
  };

  beforeUpload(file: File, field: string) {
    const { beforeUpload: beforeUploadHandler } = this.props;
    const { publicRuntimeConfig: config } = getConfig();
    let maxSize = config.MAX_SIZE_FILE || 10240;
    switch (field) {
      case 'thumbnail':
        maxSize = config.MAX_SIZE_IMAGE || 5;
        break;
      case 'teaser': maxSize = config.MAX_SIZE_TEASER || 200;
        break;
      case 'video': maxSize = config.MAX_SIZE_VIDEO || 10240;
        break;
      default: break;
    }
    const valid = file.size / 1024 / 1024 < maxSize;
    if (!valid) {
      // eslint-disable-next-line no-nested-ternary
      message.error(`${field === 'thumbnail' ? 'Thumbnail' : field === 'teaser' ? 'Teaser' : 'Video'} must be smaller than ${maxSize}MB!`);
      return false;
    }
    if (field === 'thumbnail') this.setState({ selectedThumbnail: file });
    if (field === 'teaser') this.setState({ selectedTeaser: file });
    if (field === 'video') this.setState({ selectedVideo: file });
    beforeUploadHandler(file, field);
    return true;
  }

  render() {
    if (!this.formRef) this.formRef = createRef();
    const { publicRuntimeConfig: config } = getConfig();
    const {
      video, submit, uploading, uploadPercentage = 0
    } = this.props;
    const {
      previewThumbnail, previewVideo, isSchedule, previewTeaserVideo, scheduledAt,
      selectedTeaser, selectedThumbnail, selectedVideo, isSaleVideo, performers, firstLoadPerformer, searchValue
    } = this.state;
    return (
      <Form
        {...layout}
        onFinish={(values) => {
          const data = { ...values };
          if (data.status === 'file-error') {
            message.error('The video file is in error. Please upload a new one.');
            return;
          }
          if (data.isSchedule) {
            data.scheduledAt = scheduledAt;
          }
          if (data.tags && data.tags.length) {
            data.tags = data.tags.map((t) => t.replace(/\s+/g, '_').toLowerCase());
          }
          submit(data);
        }}
        name="form-upload"
        ref={this.formRef}
        initialValues={
          video || ({
            title: '',
            price: 1,
            description: '',
            status: 'active',
            performerId: '',
            tags: [],
            categoryIds: [],
            isSaleVideo: false,
            participantIds: [],
            isSchedule: false
          })
        }
      >
        <Form.Item name="performerId" label="Creator">
          <SelectPerformerDropdown
            noEmpty
            defaultValue={video && video.performerId}
            onSelect={(val) => this.setFormVal('performerId', val)}
          />
        </Form.Item>
        <Form.Item name="title" label="Title">
          <Input placeholder="Enter video title" />
        </Form.Item>
        <Form.Item label="Tags" name="tags">
          <Select
            defaultValue={video && video.tags}
            onChange={(val) => this.setFormVal('tags', val)}
            mode="tags"
            style={{ width: '100%' }}
            size="middle"
            showArrow={false}
            defaultActiveFirstOption={false}
            placeholder="Add Tags"
          />
        </Form.Item>
        <Form.Item
          label="Participants"
          name="participantIds"
        >
          {firstLoadPerformer && (
            <Select
              mode="multiple"
              style={{ width: '100%' }}
              showSearch
              placeholder="Search performers here"
              optionFilterProp="children"
              onSearch={this.onSearchParticipants.bind(this)}
              onSelect={this.clearSearchParticipants.bind(this)}
              searchValue={searchValue}
              loading={uploading}

            >
              {performers
                && performers.length > 0
                && performers.map((p) => (
                  <Select.Option key={p._id} value={p._id}>
                    <Avatar src={p?.avatar || '/no-avatar.png'} />
                    {' '}
                    {p?.name || p?.username || 'N/A'}
                  </Select.Option>
                ))}
            </Select>
          )}
        </Form.Item>
        <Form.Item name="description" label="Description">
          <Input.TextArea rows={3} />
        </Form.Item>
        <Form.Item name="isSaleVideo" label="PPV?" valuePropName="checked">
          <Switch unCheckedChildren="Subscribe to view" checkedChildren="Pay per view" onChange={(val) => this.setState({ isSaleVideo: val })} />
        </Form.Item>
        {isSaleVideo && (
          <Form.Item name="price" label="Price">
            <InputNumber min={1} />
          </Form.Item>
        )}
        <Form.Item name="isSchedule" label="Scheduled?" valuePropName="checked">
          <Switch unCheckedChildren="Recent" checkedChildren="Upcoming" onChange={(checked) => this.setState({ isSchedule: checked })} />
        </Form.Item>
        {isSchedule && (
          <Form.Item label="Scheduled for">
            <DatePicker
              style={{ width: '100%' }}
              disabledDate={(currentDate) => currentDate && currentDate < moment().endOf('day')}
              defaultValue={video && video.scheduledAt ? moment(video.scheduledAt) : moment().add(1, 'day')}
              onChange={(date) => this.setState({ scheduledAt: date })}
            />
          </Form.Item>
        )}
        <Form.Item name="status" label="Status" rules={[{ required: true, message: 'Please select status!' }]}>
          <Select>
            <Select.Option key="active" value="active">
              Active
            </Select.Option>
            <Select.Option key="inactive" value="inactive">
              Inactive
            </Select.Option>
          </Select>
        </Form.Item>
        {/* {previewThumbnail && <img src={previewThumbnail} alt="banner" style={{ width: '70px', height: '70px' }} />} */}
        <Row>
          <Col lg={8} xs={24}>
            <Form.Item
              label="Video"
              help={(selectedVideo && <a>{selectedVideo.name}</a>)
                || (previewVideo && <a href={previewVideo} target="_blank" rel="noreferrer">Click here to preview</a>)
                || `Video file is ${config.MAX_SIZE_VIDEO || 10240}MB or below`}
            >
              <Upload
                customRequest={() => false}
                listType="picture-card"
                className="avatar-uploader"
                accept="video/*"
                multiple={false}
                showUploadList={false}
                disabled={uploading}
                beforeUpload={(file) => this.beforeUpload(file, 'video')}
              >
                {selectedVideo ? <FileAddOutlined /> : <VideoCameraAddOutlined />}
              </Upload>
            </Form.Item>
          </Col>
          <Col lg={8} xs={24}>
            <Form.Item
              label="Thumbnail"
              help={(selectedThumbnail && <a>{selectedThumbnail.name}</a>)
                || (previewThumbnail && <p><a href={previewThumbnail} target="_blank" rel="noreferrer">Click here to preview</a></p>)
                || `Thumbnail is ${config.MAX_SIZE_IMAGE || 5}MB or below`}
            >
              <ImgCrop rotationSlider cropShape="rect" quality={1} modalTitle="Edit thumbnail" modalWidth={768}>
                <Upload
                  customRequest={() => false}
                  listType="picture-card"
                  className="avatar-uploader"
                  accept="image/*"
                  multiple={false}
                  showUploadList={false}
                  disabled={uploading}
                  beforeUpload={(file) => this.beforeUpload(file, 'thumbnail')}
                >
                  {selectedThumbnail ? <FileAddOutlined /> : <CameraOutlined />}
                </Upload>
              </ImgCrop>
            </Form.Item>
          </Col>
          <Col lg={8} xs={24}>
            <Form.Item
              label="Teaser"
              help={
                (selectedTeaser && <a>{selectedTeaser.name}</a>)
                || (previewTeaserVideo && <p><a href={previewTeaserVideo} target="_blank" rel="noreferrer">Click here to preview</a></p>)
                || `Teaser is ${config.MAX_SIZE_TEASER || 200}MB or below`
              }
            >
              <Upload
                customRequest={() => false}
                listType="picture-card"
                className="avatar-uploader"
                accept="video/*"
                multiple={false}
                showUploadList={false}
                disabled={uploading}
                beforeUpload={(file) => this.beforeUpload(file, 'teaser')}
              >
                {selectedTeaser ? <FileAddOutlined /> : <VideoCameraAddOutlined />}
              </Upload>
            </Form.Item>
          </Col>
        </Row>
        {uploadPercentage > 0 && (
          <Progress percent={Math.round(uploadPercentage)} />
        )}
        <Form.Item className="text-center">
          <Button type="primary" htmlType="submit" loading={uploading}>
            {video ? 'Update' : 'Upload'}
          </Button>
        </Form.Item>
      </Form>
    );
  }
}
