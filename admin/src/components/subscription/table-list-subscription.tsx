import { EditOutlined, StopOutlined } from '@ant-design/icons';
import { formatDate } from '@lib/date';
import {
  Button,
  Table, Tag
} from 'antd';
import Link from 'next/link';
import { ISubscription } from 'src/interfaces';

interface IProps {
  dataSource: ISubscription[];
  pagination: {};
  rowKey: string;
  onChange(): Function;
  loading: boolean;
  onCancelSubscriber: Function;
}

export function TableListSubscription({
  dataSource,
  pagination,
  rowKey,
  onChange,
  loading,
  onCancelSubscriber
}: IProps) {
  const columns = [
    {
      title: 'User',
      dataIndex: 'userInfo',
      render(data, records) {
        return (
          <span>
            {`${records?.userInfo?.name || records?.userInfo?.username || ''}`}
          </span>
        );
      }
    },
    {
      title: 'Creator',
      dataIndex: 'performerInfo',
      render(data, records) {
        return (
          <span>
            {`${records?.performerInfo?.name || records?.performerInfo?.username || ''}`}
          </span>
        );
      }
    },
    {
      title: 'Type',
      dataIndex: 'subscriptionType',
      sorter: true,
      render(subscriptionType: string) {
        switch (subscriptionType) {
          case 'monthly':
            return <Tag color="orange">Monthly Subscription</Tag>;
          case 'yearly':
            return <Tag color="purple">Yearly Subscription</Tag>;
          case 'free':
            return <Tag color="green">Free Subscription</Tag>;
          case 'system':
            return <Tag color="red">System</Tag>;
          default: return <Tag color="orange">Monthly Subscription</Tag>;
        }
      }
    },
    {
      title: 'Subscription ID',
      dataIndex: 'subscriptionId'
    },
    {
      title: 'Start Date',
      dataIndex: 'startRecurringDate',
      sorter: true,
      render(data, records) {
        return <span>{records?.status === 'active' ? formatDate(records.createdAt, 'LL') : 'N/A'}</span>;
      }
    },
    {
      title: 'Renewal Date',
      dataIndex: 'nextRecurringDate',
      sorter: true,
      render(date, records) {
        if (date && records.status === 'active') {
          return <span>{formatDate(records.nextRecurringDate, 'LL')}</span>;
        }
        return null;
      }
    },
    {
      title: 'Expiry Date',
      dataIndex: 'expiredAt',
      sorter: true,
      render(date: Date, record) {
        if ((record.status !== 'active' || record.subscriptionType === 'system') && date) {
          return <span>{formatDate(date, 'LL')}</span>;
        }

        return null;
      }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      sorter: true,
      render(status: string) {
        switch (status) {
          case 'active':
            return <Tag color="green">Active</Tag>;
          case 'deactivated':
            return <Tag color="red">Deactivated</Tag>;
          default: <Tag color="orange">{status}</Tag>;
        }
        return status;
      }
    },
    {
      title: 'Updated on',
      dataIndex: 'updatedAt',
      sorter: true,
      render(date: Date) {
        return <span>{formatDate(date)}</span>;
      }
    },
    {
      title: 'Action',
      dataIndex: 'status',
      render(id, records) {
        return (
          <div style={{ display: 'inline-flex', gap: '10px' }}>
            <Button disabled={records?.status !== 'active'} type="link" onClick={() => onCancelSubscriber(records)}>
              <StopOutlined />
              {' '}
              Cancel subscription
            </Button>
            <Link
              href={{
                pathname: '/subscription/update',
                query: { id: records._id }
              }}
              as={`/subscription/update?id=${records._id}`}
            >
              <a>
                <EditOutlined />
                {' '}
                Update
              </a>
            </Link>
          </div>

        );
      }
    }
  ];
  return (
    <Table
      columns={columns}
      dataSource={dataSource}
      rowKey={rowKey}
      pagination={pagination}
      onChange={onChange}
      loading={loading}
    />
  );
}
