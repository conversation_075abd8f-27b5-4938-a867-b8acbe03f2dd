import { US_STATES } from '@lib/country';
import { utilsService } from '@services/utils.service';
import {
  Button, Form, InputNumber,
  Select
} from 'antd';
import { useEffect, useState } from 'react';

interface IProps {
  item?: any;
  onFinish: Function;
  loading: boolean;
}
export function FormPaymentFee({ item = null, onFinish = () => {}, loading = false }: IProps) {
  const [countries, setCountries] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState(item?.country || '');

  const getCountries = async () => {
    const resp = await utilsService.countriesList();
    if (resp.data) setCountries(resp.data);
  };

  useEffect(() => {
    getCountries();
  }, []);

  return (
    <Form
      onFinish={onFinish.bind(this)}
      initialValues={
          item || {
            country: '',
            state: '',
            flatFee: 0.05,
            processingFee: 0.1,
            taxes: 1
          }
        }
      layout="vertical"
    >
      <Form.Item
        name="country"
        rules={[{ required: true, message: 'Please select a country' }]}
        label="Country"
      >
        <Select
          placeholder="Select a country"
          optionFilterProp="label"
          showSearch
          disabled={item}
          onSelect={(val) => setSelectedCountry(val)}
        >
          {countries.map((c) => (
            <Select.Option value={c.code} label={c.name} key={c.code}>
              <img alt="country_flag" src={c.flag} width="25px" />
              {' '}
              {c.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      {selectedCountry === 'US' && (
      <Form.Item
        name="state"
        rules={[{ required: true, message: 'Please select a state' }]}
        label="State in US"
      >
        <Select
          placeholder="Select a state"
          optionFilterProp="label"
          showSearch
          disabled={item?.state}
        >
          {US_STATES.map((c) => (
            <Select.Option value={c.code} label={c.name} key={c.code}>
              {c.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      )}
      {/* support state
      <Form.Item
        name="country"
        rules={[{ required: true, message: 'Please select your country' }]}
        label="Country"
      >
        <Select
          placeholder="Select your country"
          optionFilterProp="label"
          showSearch
          disabled={item}
        >
          state
        </Select>
      </Form.Item> */}
      <Form.Item
        name="flatFee"
        label="Flat fee"
        rules={[{ required: true, message: 'Please input flat fee' }]}
      >
        <InputNumber style={{ width: '100%' }} min={0} />
      </Form.Item>
      <Form.Item
        name="processingFee"
        label="Processing fee in percent"
        rules={[
          { required: true, message: 'Please input processing fee' }
        ]}
      >
        <InputNumber min={0} />
      </Form.Item>
      <Form.Item
        name="taxes"
        label="Taxes in percent"
        rules={[
          { required: true, message: 'Please input taxes' }
        ]}
      >
        <InputNumber min={0} />
      </Form.Item>
      <Form.Item wrapperCol={{ span: 20, offset: 4 }}>
        <Button type="primary" htmlType="submit" loading={loading}>
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
}
