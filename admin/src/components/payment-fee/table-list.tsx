import {
  DeleteOutlined, EditOutlined
} from '@ant-design/icons';
import { DropdownAction } from '@components/common/dropdown-action';
import { US_STATES } from '@lib/country';
import { utilsService } from '@services/utils.service';
import { Table } from 'antd';
import { useEffect, useState } from 'react';

type IProps = {
  dataSource: any;
  rowKey: string;
  loading: boolean;
  pagination: {};
  onChange: Function;
  onDelete?: Function;
}

export function TableListPaymentFee({
  onDelete = () => { }, dataSource, rowKey, loading, pagination, onChange
}: IProps) {
  const [countries, setCountries] = useState([]);

  const getCountries = async () => {
    const resp = await utilsService.countriesList();
    if (resp.data) setCountries(resp.data);
  };

  useEffect(() => {
    getCountries();
  }, []);

  const columns = [
    {
      title: 'Country',
      dataIndex: 'country',
      render(countryCode: string) {
        const country = countries.find((c) => c.code.toLowerCase() === countryCode.toLowerCase());
        return <span>{country?.name || countryCode}</span>;
      }
    },
    {
      title: 'State',
      dataIndex: 'state',
      render(stateCode: string) {
        if (!stateCode) return <span />;
        const state = US_STATES.find((s) => s.code.toLowerCase() === stateCode.toLowerCase());
        return <span>{state?.name || stateCode}</span>;
      }
    },
    {
      title: 'Flat fee',
      dataIndex: 'flatFee',
      render(value: number) {
        return <span>{value}</span>;
      }
    },
    {
      title: 'Processing fee (%)',
      dataIndex: 'processingFee',
      render(value: number) {
        return (
          <span>
            {value}
            {' '}
            %
          </span>
        );
      }
    },
    {
      title: 'Taxes (%)',
      dataIndex: 'taxes',
      render(value: number) {
        return (
          <span>
            {value}
            {' '}
            %
          </span>
        );
      }
    },
    {
      title: 'Action',
      dataIndex: '_id',
      render: (data, record) => (
        <DropdownAction
          menuOptions={[
            {
              key: 'update',
              label: 'Update',
              href: {
                pathname: '/payment-fee/update',
                query: { id: record._id }
              },
              icon: <EditOutlined />
            },
            {
              key: 'delete',
              label: 'Delete',
              onClick: onDelete.bind(this, record._id),
              icon: <DeleteOutlined />
            }
          ]}
        />
      )
    }
  ];
  return (
    <Table
      dataSource={dataSource}
      columns={columns}
      rowKey={rowKey}
      loading={loading}
      pagination={pagination}
      onChange={onChange.bind(this)}
    />
  );
}

export default TableListPaymentFee;
