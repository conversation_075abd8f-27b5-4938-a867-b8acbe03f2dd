@import '../../../../style/vars.less';

.themeToggle {
  position: relative;
  display: inline-block;
}

.toggleButton {
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  color: var(--text-color, @text-color);

  &:hover {
    background-color: var(--hover-bg, rgba(0, 0, 0, 0.04));
    color: var(--primary-color, @primary-color);
  }

  &:focus {
    box-shadow: 0 0 0 2px var(--primary-color-fade, fade(@primary-color, 20%));
  }

  .anticon {
    font-size: 16px;
    transition: transform 0.2s ease;
  }

  &:hover .anticon {
    transform: scale(1.1);
  }
}

.buttonLabel {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.dropdownMenu {
  min-width: 200px;
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05),
    0 12px 48px 16px rgba(0, 0, 0, 0.03);
  background: var(--component-background, @white);
  border: 1px solid var(--border-color, @border-color-base);
}

.menuItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    background-color: var(--item-hover-bg, rgba(0, 0, 0, 0.04));
  }
}

.menuItemContent {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.menuItemIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 16px;
  color: var(--text-color-secondary, @gray);
  transition: color 0.2s ease;

  .menuItem:hover & {
    color: var(--primary-color, @primary-color);
  }
}

.menuItemText {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.menuItemLabel {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color, @text-color);
  line-height: 1.4;
}

.menuItemDescription {
  font-size: 12px;
  color: var(--text-color-secondary, @gray);
  line-height: 1.3;
}

.checkIcon {
  color: var(--success-color, @success-color);
  font-size: 14px;
  font-weight: bold;
}

// Dark theme specific styles
:global(.dark-mode) {
  .toggleButton {
    color: rgba(255, 255, 255, 0.85);

    &:hover {
      background-color: rgba(255, 255, 255, 0.08);
      color: var(--primary-color, @primary-color);
    }
  }

  .dropdownMenu {
    background: #1f1f1f;
    border-color: #434343;
    box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.32), 0 9px 28px 0 rgba(0, 0, 0, 0.2),
      0 12px 48px 16px rgba(0, 0, 0, 0.12);
  }

  .menuItem {
    &:hover {
      background-color: rgba(255, 255, 255, 0.08);
    }
  }

  .menuItemIcon {
    color: rgba(255, 255, 255, 0.65);

    .menuItem:hover & {
      color: var(--primary-color, @primary-color);
    }
  }

  .menuItemLabel {
    color: rgba(255, 255, 255, 0.85);
  }

  .menuItemDescription {
    color: rgba(255, 255, 255, 0.45);
  }
}

// Responsive design
@media (max-width: @tablet-screen) {
  .dropdownMenu {
    min-width: 180px;
  }

  .menuItem {
    padding: 10px 12px;
  }

  .menuItemContent {
    gap: 10px;
  }

  .buttonLabel {
    display: none;
  }
}

// Animation for theme transitions is handled in the main theme files
