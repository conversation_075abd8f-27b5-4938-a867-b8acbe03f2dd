@import '../../../../style/vars.less';

.header {
  padding: 0;
  box-shadow: var(--shadow-color-dark, @shadow-2) 0 4px 40px 0;
  position: relative;
  display: flex;
  justify-content: space-between;
  height: 72px;
  z-index: 9;
  align-items: center;
  background: var(--component-background, @white);
  color: var(--text-color, @text-color);
  transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;

  &.fixed {
    position: fixed;
    top: 0;
    right: 0;
    width: ~'calc(100% - 256px)';
    z-index: 29;
    transition: width 0.2s;

    &.collapsed {
      width: ~'calc(100% - 80px)';
    }
  }

  :global {
    .ant-menu-submenu-title {
      height: 72px;
    }

    .ant-menu-horizontal {
      line-height: 72px;

      & > .ant-menu-submenu:hover {
        color: @white;
      }
    }

    .ant-menu {
      border-bottom: none;
      height: 72px;
    }

    .ant-menu-horizontal > .ant-menu-submenu {
      top: 0;
      margin-top: 0;
    }

    .ant-menu-horizontal > .ant-menu-item,
    .ant-menu-horizontal > .ant-menu-submenu {
      border-bottom: none;
    }

    .ant-menu-horizontal > .ant-menu-item-active,
    .ant-menu-horizontal > .ant-menu-item-open,
    .ant-menu-horizontal > .ant-menu-item-selected,
    .ant-menu-horizontal > .ant-menu-item:hover,
    .ant-menu-horizontal > .ant-menu-submenu-active,
    .ant-menu-horizontal > .ant-menu-submenu-open,
    .ant-menu-horizontal > .ant-menu-submenu-selected,
    .ant-menu-horizontal > .ant-menu-submenu:hover {
      border-bottom: none;
    }
  }

  .rightContainer {
    display: flex;
    align-items: center;
  }

  :global .button {
    width: 72px;
    height: 72px;
    line-height: 72px;
    text-align: center;
    font-size: 18px;
    cursor: pointer;
    transition: @transition-ease-in;
    color: var(--text-color, @text-color);

    &:hover {
      background-color: var(--primary-color, @primary-color);
      color: var(--color-white, @white);
    }
  }
}

.iconButton {
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 24px;
  cursor: pointer;
  .background-hover();
  transition: background-color 0.3s ease;

  &:hover {
    background-color: var(--item-hover-bg, rgba(0, 0, 0, 0.04));

    .iconFont {
      color: var(--primary-color, @primary-color);
    }
  }

  & + .iconButton {
    margin-left: 8px;
  }

  .iconFont {
    color: var(--text-color-secondary, #b2b0c7);
    font-size: 24px;
    transition: color 0.3s ease;
  }
}

.notification {
  padding: 24px 0;
  width: 320px;

  .notificationItem {
    transition: all 0.3s;
    padding: 12px 24px;
    cursor: pointer;

    &:hover {
      background-color: @hover-color;
    }
  }

  .clearButton {
    text-align: center;
    height: 48px;
    line-height: 48px;
    cursor: pointer;
    .background-hover();
  }
}

.notificationPopover {
  :global {
    .ant-popover-inner-content {
      padding: 0;
    }

    .ant-popover-arrow {
      display: none;
    }

    .ant-list-item-content {
      flex: 0;
      margin-left: 16px;
    }
  }
}

@media (max-width: 767px) {
  .header {
    width: 100% !important;
  }
}

.right-content {
  padding: 15px;
  list-style: none;
  display: flex;
  align-items: center;

  li {
    margin: 0px 5px;
    font-size: 25px;
    color: var(--text-color, @text-color);
    transition: color 0.3s ease;

    a {
      color: var(--text-color, @text-color);
      transition: color 0.3s ease;

      &:hover {
        color: var(--primary-color, @primary-color);
      }
    }
  }

  :global .logout {
    margin-top: 5px;
  }
}
