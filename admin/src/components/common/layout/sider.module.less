@import '../../../../style/vars.less';

.sider {
  box-shadow: var(--shadow-color, fade(@primary-color, 10%)) 0 0 28px 0;
  z-index: 10;
  background: var(--component-background, @white);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;

  :global {
    .ant-layout-sider-children {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
}

.brand {
  z-index: 1;
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
  box-shadow: 0 1px 9px -3px var(--shadow-color, rgba(0, 0, 0, 0.2));
  background: var(--component-background, @white);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;

  :global .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;

    img {
      height: 30px;
      object-fit: contain;
      width: 100%;
      margin-right: 8px;
    }

    h1 {
      vertical-align: text-bottom;
      font-size: 16px;
      text-transform: uppercase;
      display: inline-block;
      font-weight: 700;
      color: var(--primary-color, @primary-color);
      white-space: nowrap;
      margin-bottom: 0;
      .text-gradient();
      transition: color 0.3s ease;

      :local {
        animation: fadeRightIn 300ms ease-in-out;
        animation-fill-mode: both;
      }
    }
  }
}

.menuContainer {
  height: ~'calc(100vh - 120px)';
  overflow-x: hidden;
  flex: 1;
  padding: 0;

  &::-webkit-scrollbar-thumb {
    background-color: transparent;
  }

  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
    }
  }

  :global {
    .ant-menu-inline {
      border-right: none;
    }
  }
}

.switchTheme {
  width: 100%;
  height: 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  overflow: hidden;
  transition: all 0.3s;
  background: var(--component-background, @white);
  border-top: 1px solid var(--border-color-split, @border-color-split);

  span {
    white-space: nowrap;
    overflow: hidden;
    font-size: 12px;
    color: var(--text-color-secondary, @border-color-base);
    transition: color 0.3s ease;
  }

  :global {
    .anticon {
      min-width: 14px;
      margin-right: 4px;
      font-size: 14px;
      color: var(--text-color-secondary, @text-color);
      transition: color 0.3s ease;
    }
  }
}
