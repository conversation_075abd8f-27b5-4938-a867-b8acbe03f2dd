@import (inline) '../../../../node_modules/react-perfect-scrollbar/dist/css/styles.css';

:global {
  .ps--active-x > .ps__rail-x,
  .ps--active-y > .ps__rail-y {
    background-color: transparent;
  }

  .ps__rail-x:hover > .ps__thumb-x,
  .ps__rail-x:focus > .ps__thumb-x {
    height: 8px;
  }

  .ps__rail-y:hover > .ps__thumb-y,
  .ps__rail-y:focus > .ps__thumb-y {
    width: 8px;
  }

  .ps__rail-y,
  .ps__rail-x {
    z-index: 9;
  }

  .ps__thumb-y {
    width: 4px;
    right: 4px;
    background-color: var(--text-color-tertiary, rgba(0, 0, 0, 0.3));
    transition: background-color 0.3s ease;
  }

  .ps__thumb-x {
    height: 4px;
    bottom: 4px;
    background-color: var(--text-color-tertiary, rgba(0, 0, 0, 0.3));
    transition: background-color 0.3s ease;
  }

  // Dark mode specific overrides
  .dark-mode & {
    .ps__thumb-y,
    .ps__thumb-x {
      background-color: var(--text-color-tertiary, rgba(255, 255, 255, 0.3));
    }

    .ps__rail-y:hover > .ps__thumb-y,
    .ps__rail-x:hover > .ps__thumb-x {
      background-color: var(--text-color-secondary, rgba(255, 255, 255, 0.5));
    }
  }
}
