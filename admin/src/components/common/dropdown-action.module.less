.dropbtn {
  padding: 5px;
  border: none;
  border: 1px solid var(--border-color-base, #ccc);
  background: var(--component-background, @white);
  color: var(--text-color, @text-color);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;

  &:hover {
    background: var(--item-hover-bg, rgba(0, 0, 0, 0.04));
    border-color: var(--primary-color, @primary-color);
  }
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown:hover {
  .dropdown-content {
    display: block;
  }
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: var(--component-background, #f1f1f1);
  min-width: 160px;
  left: -30px;
  box-shadow: 0px 8px 8px 0px var(--shadow-color, rgba(0, 0, 0, 0.2));
  z-index: 999;
  border: 1px solid var(--border-color-base, #ccc);
  border-radius: 4px;
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;

  :global {
    a {
      color: var(--text-color, black);
      padding: 10px;
      text-decoration: none;
      display: block;
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    a:hover {
      background-color: var(--item-hover-bg, #ddd);
      color: var(--primary-color, @primary-color);
    }
  }
}

.space-item {
  float: left;
}
