import { formatDate } from '@lib/date';
import {
  message,
  Modal,
  Table, Tag
} from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { utilsService } from '@services/utils.service';
import { US_STATES } from '@lib/country';

interface IProps {
  dataSource: [];
  rowKey: string;
  loading: boolean;
  pagination: {};
  onChange: Function;
}

interface ICountry {
  code: string;
  name: string;
}

export function TableListPaymentTransaction({
  dataSource,
  rowKey,
  loading,
  pagination,
  onChange
}: IProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedVat, setSelectedVat] = useState<any>(null);
  const [countries, setCountries] = useState<ICountry[]>([]);

  const showVatModal = (record: any) => {
    setSelectedVat(record);
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setSelectedVat(null);
  };

  const getCountries = async () => {
    try {
      const resp = await utilsService.countriesList();
      if (resp.data) setCountries(resp.data);
    } catch (error) {
      message.error('Failed to fetch countries.');
    }
  };

  const country = selectedVat?.countryCode ? countries.find((c) => c.code.toLowerCase() === selectedVat.countryCode.toLowerCase()) : null;

  const state = selectedVat?.stateCode ? US_STATES.find((s) => s.code.toLowerCase() === selectedVat?.stateCode.toLowerCase()) : null;

  const columns = [
    {
      title: 'Transaction ID',
      key: 'orderNumber',
      render(record) {
        return (
          <span>{record.orderNumber}</span>
        );
      }
    },
    {
      title: 'Transaction Date',
      key: 'updatedAt',
      dataIndex: 'updatedAt',
      sorter: true,
      fixed: 'right' as 'right',
      render(date: Date) {
        return <span>{formatDate(date)}</span>;
      }
    },
    {
      title: 'User Name',
      key: 'buyer',
      render(record) {
        return (
          <span>
            {record.buyer?.name || record?.buyer?.username || 'N/A'}
          </span>
        );
      }
    },
    {
      title: 'Creator Name',
      key: 'seller',
      render(record) {
        return (
          <span>
            {record?.seller?.name || record?.seller?.username || 'N/A'}
          </span>
        );
      }
    },
    {
      title: 'Transaction Type',
      dataIndex: 'productType',
      sorter: true,
      render(type: string) {
        switch (type) {
          case 'monthly_subscription':
            return <Tag color="red">Monthly Subscription</Tag>;
          case 'yearly_subscription':
            return <Tag color="red">Yearly Subscription</Tag>;
          case 'sale_video':
            return <Tag color="#FFCF00">PPV Purchase</Tag>;
          case 'digital':
            return <Tag color="blue">Digital Product</Tag>;
          case 'physical':
            return <Tag color="blue">Physical Product</Tag>;
          case 'stream_private':
            return <Tag color="yellow">Private Show</Tag>;
          case 'tip':
            return <Tag color="magenta">Tip</Tag>;
          case 'feed':
            return <Tag color="magenta">Feed</Tag>;
          case 'referral':
            return <Tag color="magenta">Referral reward</Tag>;
          case 'wallet':
            return <Tag color="#8A2BE2">Wallet Top-up</Tag>;
          default:
            return <Tag color="#936dc9">{type}</Tag>;
        }
      }
    },
    {
      title: 'Product Name',
      dataIndex: 'name'
    },
    {
      title: 'Price',
      dataIndex: 'totalPrice',
      render(data, record) {
        return (
          <span>
            $
            {record.totalPrice?.toFixed(2)}
          </span>
        );
      }
    },
    {
      title: 'Payment Method',
      dataIndex: 'paymentGateway',
      render(paymentGateway: string) {
        switch (paymentGateway) {
          case 'ccbill':
            return <Tag color="blue">CCBill</Tag>;
          case 'verotel':
            return <Tag color="pink">Verotel</Tag>;
          default: return <Tag color="#FFCF00">{paymentGateway || 'CCbill'}</Tag>;
        }
      }
    },
    {
      title: 'Payment Status',
      dataIndex: 'paymentStatus',
      render(status: string) {
        switch (status) {
          case 'paid': case 'success':
            return <Tag color="success">Success</Tag>;
          case 'pending': case 'created':
            return <Tag color="warning">Created</Tag>;
          case 'cancelled':
            return <Tag color="danger">Cancelled</Tag>;
          case 'chargeback':
            return <Tag color="danger">Chargeback</Tag>;
          default:
            return <Tag color="default">{status}</Tag>;
        }
      }
    },
    {
      title: 'Fees & VAT',
      dataIndex: '_id',
      render(id: string, record: any) {
        return (
          <>
            <span> $ {record.paymentFee?.toFixed(2) || '0.00'}</span>
            {' '}
            <a onClick={() => showVatModal(record)}>
              <EyeOutlined />
            </a>
          </>
        );
      }
    }
  ];
  useEffect(() => {
    getCountries();
  }, []);

  return (
    <>
      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey={rowKey}
        loading={loading}
        pagination={pagination}
        onChange={onChange.bind(this)}
      />
      <Modal
        title="Fee & VAT Breakdown"
        open={isModalOpen}
        onCancel={handleCancel}
        footer={null}
        width={600}
      >
        {selectedVat && (
          <div>
            <p>
              <strong>Order Number:</strong> {selectedVat.orderNumber}
            </p>
            <p>
              <strong>Original Price:</strong> ${selectedVat.originalPrice?.toFixed(2) || selectedVat.unitPrice?.toFixed(2) || '0.00'}
            </p>
            <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
              <h4>Fee Breakdown:</h4>
              {selectedVat.flatFee > 0 && (
                <p>
                  <strong>Processing Fee (Flat):</strong> ${selectedVat.flatFee?.toFixed(2) || '0.00'}
                </p>
              )}
              {selectedVat.processingFee > 0 && (
                <p>
                  <strong>Processing Fee ({selectedVat.processingFee?.toFixed(2) || '0'}%):</strong> ${((selectedVat.originalPrice || selectedVat.unitPrice || 0) * (selectedVat.processingFee || 0) / 100)?.toFixed(2) || '0.00'}
                </p>
              )}
              {selectedVat.taxesFee > 0 && (
                <p>
                  <strong>VAT/Sales Tax ({selectedVat.taxesFee}%):</strong> ${((selectedVat.originalPrice || selectedVat.unitPrice || 0) * (selectedVat.taxesFee || 0) / 100)?.toFixed(2) || '0.00'}
                </p>
              )}
              <hr />
              <p>
                <strong>Total Fees & VAT:</strong> ${selectedVat.paymentFee?.toFixed(2) || '0.00'}
              </p>
            </div>
            <p style={{ marginTop: '15px' }}>
              <strong>Total Price Paid:</strong> ${selectedVat.totalPrice?.toFixed(2) || '0.00'}
            </p>
            <p>
              <strong>Country:</strong> {country?.name || selectedVat.countryCode || 'N/A'}
            </p>
            <p>
              <strong>State:</strong> {state?.name || selectedVat.stateCode || 'N/A'}
            </p>
            <div style={{ marginTop: '15px', fontSize: '12px', color: '#666' }}>
              * Processing fees and VAT are retained by the company for tax obligations and payment processing costs.
            </div>
          </div>
        )}
      </Modal>
    </>
  );
}
