import { APIRequest } from './api-request';

export class PaymentFeeService extends APIRequest {
  create(payload: any) {
    return this.post('/admin/payment-fee', payload);
  }

  search(query?: { [key: string]: any }) {
    return this.get(this.buildUrl('/admin/payment-fee/search', query));
  }

  findByIdOrCode(id: string) {
    return this.get(`/admin/payment-fee/${id}/view`);
  }

  update(id: string, payload: any) {
    return this.put(`/admin/payment-fee/${id}`, payload);
  }

  delete(id: string) {
    return this.del(`/admin/payment-fee/${id}`);
  }
}

export const paymentFeeService = new PaymentFeeService();
