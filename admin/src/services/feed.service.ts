import { APIRequest } from './api-request';

export class FeedService extends APIRequest {
  search(query?: { [key: string]: any }) {
    return this.get(
      this.buildUrl('/admin/feeds', query)
    );
  }

  userSearch(query?: { [key: string]: any }) {
    return this.get(
      this.buildUrl('/feeds/users', query)
    );
  }

  userHomeFeeds(query?: { [key: string]: any }) {
    return this.get(
      this.buildUrl('/feeds/users/home-feeds', query)
    );
  }

  delete(id: string) {
    return this.del(`/admin/feeds/${id}`);
  }

  findById(id: string, headers?: { [key: string]: string }) {
    return this.get(`/admin/feeds/${id}`, headers);
  }

  update(id: string, payload: any) {
    return this.put(`/admin/feeds/${id}`, payload);
  }

  create(data) {
    return this.post('/admin/feeds', data);
  }

  uploadPhoto(file: File, payload: any, onProgress?: Function) {
    return this.upload(
      '/admin/feeds/photo/upload',
      [
        {
          fieldname: 'file',
          file
        }
      ],
      {
        onProgress,
        customData: payload
      }
    );
  }

  uploadVideo(file: File, payload: any, onProgress?: Function) {
    return this.upload(
      '/admin/feeds/video/upload',
      [
        {
          fieldname: 'file',
          file
        }
      ],
      {
        onProgress,
        customData: payload
      }
    );
  }

  uploadThumbnail(file: File, payload: any, onProgress?: Function) {
    return this.upload(
      '/admin/feeds/thumbnail/upload',
      [
        {
          fieldname: 'file',
          file
        }
      ],
      {
        onProgress,
        customData: payload
      }
    );
  }

  uploadTeaser(file: File, payload: any, onProgress?: Function) {
    return this.upload(
      '/admin/feeds/teaser/upload',
      [
        {
          fieldname: 'file',
          file
        }
      ],
      {
        onProgress,
        customData: payload
      }
    );
  }

  addPoll(payload) {
    return this.post('/admin/feeds/polls', payload);
  }

  votePoll(pollId: string) {
    return this.post(`/feeds/users/vote/${pollId}`);
  }

  getBookmark(payload) {
    return this.get(this.buildUrl('/reactions/feeds/bookmark', payload));
  }
}

export const feedService = new FeedService();
