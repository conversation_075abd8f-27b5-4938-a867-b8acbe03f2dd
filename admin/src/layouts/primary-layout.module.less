@import '../../style/vars.less';

.backTop {
  right: 50px;

  :global {
    .ant-back-top-content {
      background: @primary-color;
      opacity: 0.3;
      transition: all 0.3s;
      box-shadow: 0 0 15px 1px rgba(69, 65, 78, 0.1);

      &:hover {
        opacity: 1;
        //background-color: @hover-color;
      }
    }
  }
}

.content {
  padding: 24px;
  min-height: ~'calc(100% - 72px)';
  background: var(--bg-white, @white);
  color: var(--text-color, @text-color);
  transition: background-color 0.3s ease, color 0.3s ease;

  // overflow-y: scroll;
  :global .ant-row .ant-col {
    padding: 5px !important;
  }
}

.container {
  height: 100vh;
  flex: 1;
  width: ~'calc(100% - 256px)';
  overflow-y: scroll;
  overflow-x: hidden;
  background: var(--bg-white, @white);
  transition: background-color 0.3s ease;
}

@media (max-width: 767px) {
  .content {
    padding: 12px;
  }

  .backTop {
    right: 20px;
    bottom: 20px;
  }

  .container {
    height: 100vh;
    flex: 1;
    width: 100%;
  }
}
