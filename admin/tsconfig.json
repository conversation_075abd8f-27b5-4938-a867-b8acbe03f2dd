{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": "./", "paths": {"@layouts/*": ["./src/layouts/*"], "@components/*": ["./src/components/*"], "@lib/*": ["./src/lib/*"], "@redux/*": ["./src/redux/*"], "@services/*": ["./src/services/*"], "src/*": ["./src/*"]}, "incremental": true}, "exclude": ["node_modules"], "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "./src/**/*"]}