import { BreadcrumbComponent } from '@components/common';
import Page from '@components/common/layout/page';
import { ReferralList } from '@components/referral/list';
import { referralService } from '@services/referral.service';
import { Layout, message } from 'antd';
import Head from 'next/head';
import { useEffect, useState } from 'react';

export interface IReferral{
  invitedUsername:string;
  invitedRole:string;
  updatedAt:Date
}

export default function ReferralDetail() {
  const limit = 10;
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [referrals, setReferrals] = useState<IReferral[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const handlePageChange = ({ current }) => {
    setCurrentPage(current);
  };
  const search = async (page = 1) => {
    try {
      setLoading(true);
      const resp = await referralService.getReferral({
        limit,
        offset: (page - 1) * limit
      });
      setTotal(resp.data.total);
      setReferrals(resp.data.data);
    } catch (error) {
      const e = await error;
      message.error(e.message || 'An error occurred, please try again!');
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    search(currentPage);
  }, [currentPage]);
  return (
    <Layout>
      <Head>
        <title>Referrals</title>
      </Head>
      <BreadcrumbComponent breadcrumbs={[{ title: 'Referral' }]} />
      <Page>
        <ReferralList
          referrals={referrals as any}
          total={total}
          onChange={handlePageChange}
          loading={loading}
          pageSize={limit}
          currentPage={currentPage}
        />
      </Page>
    </Layout>
  );
}
ReferralDetail.authenticate = true;
