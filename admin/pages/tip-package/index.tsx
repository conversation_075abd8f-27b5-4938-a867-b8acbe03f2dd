import { BreadcrumbComponent } from '@components/common';
import Page from '@components/common/layout/page';
import TipPackageForm from '@components/tip-package/form';
import { settingService } from '@services/setting.service';
import { tipPackageService } from '@services/tip-package.service';
import { Layout, message } from 'antd';
import Head from 'next/head';
import { PureComponent } from 'react';
interface IProps { }
class TipPackage extends PureComponent<IProps> {
    state = {
        tipList: [],
        minTippingAmount: 0,
        maxTippingAmount: 0
    };
    async componentDidMount() {
        try {
            const [resp, data] = await Promise.all([tipPackageService.getTipPackage(), settingService.valueByKeys(['minTippingAmount', 'maxTippingAmount'])]);
            const filledNumbers = [...resp.data.data, ...Array(9 - resp.data.data.length).fill(0)];
            this.setState({ tipList: filledNumbers || [], minTippingAmount: data.minTippingAmount, maxTippingAmount: data.maxTippingAmount });
        } catch (error) {
            message.error('Error fetching tip package!');
        }
    }
    render() {
        const { tipList, minTippingAmount, maxTippingAmount } = this.state;
        return (
            <Layout>
                <Head>
                    <title>Tip Package</title>
                </Head>
                <BreadcrumbComponent breadcrumbs={[{ title: 'Tip Package' }]} />
                <Page>
                    <TipPackageForm tipList={tipList} minTipAmount={minTippingAmount} maxTipAmount={maxTippingAmount} />
                </Page>
            </Layout>
        );
    }
}
export default TipPackage;