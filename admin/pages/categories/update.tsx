import { FormCategory } from '@components/category/form';
import { BreadcrumbComponent } from '@components/common';
import Loader from '@components/common/base/loader';
import Page from '@components/common/layout/page';
import { categoryService } from '@services/category.service';
import { message } from 'antd';
import Head from 'next/head';
import Router from 'next/router';
import { useEffect, useState } from 'react';
import { ICategory } from 'src/interfaces';

interface IProps {
  id: string;
}
function categoryUpdate({ id }: IProps) {
  const [submiting, setSubmiting] = useState(false);
  const [fetching, setFetching] = useState(true);
  const [category, setCategory] = useState({} as ICategory);

  const getCategory = async () => {
    try {
      setFetching(true);
      const resp = await categoryService.findById(id);
      setCategory(resp.data);
    } catch (e) {
      message.error('Category not found!');
    } finally {
      setFetching(false);
    }
  };

  useEffect(() => {
    getCategory();
  }, []);

  const submit = async (data: any) => {
    try {
      setSubmiting(true);
      await categoryService.update(id, data);
      message.success('Updated successfully');
      if (data.group === 'product') {
        Router.push('/categories?group=product');
        return;
      }
      Router.push('/categories?group=performer');
    } catch (e) {
      message.error('Something went wrong, please try again!');
    } finally {
      setSubmiting(false);
    }
  };

  return (
    <>
      <Head>
        <title>Update Category</title>
      </Head>
      <BreadcrumbComponent
        breadcrumbs={[
          { title: 'Categories', href: '/categories' },
          { title: 'Update' }
        ]}
      />
      <Page>
        {fetching ? (
          <Loader />
        ) : (
          <FormCategory category={category} onFinish={submit} submiting={submiting} />
        )}
      </Page>
    </>
  );
}

categoryUpdate.getInitialProps = (ctx) => ctx.query;

export default categoryUpdate;
