import { FormCategory } from '@components/category/form';
import { BreadcrumbComponent } from '@components/common';
import Page from '@components/common/layout/page';
import { categoryService } from '@services/category.service';
import { message } from 'antd';
import Head from 'next/head';
import Router from 'next/router';
import { useState } from 'react';

function PerformerCategoryCreate() {
  const [submiting, setSubmiting] = useState(false);

  const submit = async (data: any) => {
    try {
      await setSubmiting(true);
      await categoryService.create(data);
      message.success('Created successfully');
      if (data.group === 'product') {
        Router.push('/categories?group=product');
      }
      Router.push('/categories?group=performer');
    } catch (e) {
      const err = await e;
      message.error(err?.message || 'Something went wrong, please try again!');
    } finally {
      setSubmiting(false);
    }
  };

  return (
    <>
      <Head>
        <title>New category</title>
      </Head>
      <BreadcrumbComponent
        breadcrumbs={[{ title: 'Categories', href: '/categories' }, { title: 'New category' }]}
      />
      <Page>
        <FormCategory onFinish={submit} submiting={submiting} />
      </Page>
    </>
  );
}

export default PerformerCategoryCreate;
