import { TableListCategory } from '@components/category/table-list';
import { BreadcrumbComponent } from '@components/common';
import Page from '@components/common/layout/page';
import { SearchFilter } from '@components/common/search-filter';
import { categoryService } from '@services/category.service';
import { message } from 'antd';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

interface IProps {
  groupCategory: string;
}

function PerformerCategorys({ groupCategory }: IProps) {
  const [fetching, setFetching] = useState(false);
  const [offset, setOffset] = useState(0);
  const [list, setList] = useState([] as any);
  const [totalList, setTotalList] = useState(0);
  const [filter, setFilter] = useState({} as any);
  const [sortBy, setSortBy] = useState('updatedAt');
  const [sort, setSort] = useState('desc');
  const limit = 12;
  const router = useRouter();

  const search = async () => {
    try {
      await setFetching(true);
      let gr = '';
      if (groupCategory && groupCategory === 'performer') {
        gr = groupCategory;
      }
      const resp = await categoryService.search({
        ...filter,
        limit,
        offset: offset * limit,
        sortBy,
        sort,
        group: gr || 'product'
      });
      setList(resp.data.data);
      setTotalList(resp.data.total);
      setFetching(false);
    } catch (e) {
      message.error('An error occurred, please try again!');
      setFetching(false);
    }
  };

  useEffect(() => {
    search();
  }, [router]);

  const handleTabChange = (data, _filter, sorter) => {
    const sortD = sorter?.order === 'ascend' ? 'asc' : 'desc';
    const sortByUpdate = sorter?.field || 'updatedAt';
    setOffset(data.current - 1);
    setSort(sortD);
    setSortBy(sortByUpdate);
    search();
  };

  const handleFilter = (values) => {
    setFilter({ ...filter, ...values });
    search();
  };

  const deleteCategory = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this category?')) {
      return;
    }
    try {
      await categoryService.delete(id);
      message.success('Deleted successfully');
      await search();
    } catch (error) {
      message.error(error.message || 'An error occurred, please try again!');
    }
  };

  const statuses = [
    {
      key: '',
      text: 'All status'
    },
    {
      key: 'inactive',
      text: 'Inactive'
    },
    {
      key: 'active',
      text: 'Active'
    }
  ];

  return (
    <>
      <Head>
        <title>Categories</title>
      </Head>
      <BreadcrumbComponent breadcrumbs={[{ title: 'Categories' }]} />
      <Page>
        <SearchFilter
          statuses={statuses}
          onSubmit={handleFilter}
        />
        <div style={{ marginBottom: '20px' }} />
        <TableListCategory
          dataSource={list}
          pagination={{ total: totalList }}
          rowKey="_id"
          loading={fetching}
          onChange={handleTabChange}
          deleteCategory={deleteCategory}
        />
      </Page>
    </>
  );
}

PerformerCategorys.getInitialProps = (ctx) => ({
  groupCategory: ctx.query.group,
  router: ctx.asPath
});

export default PerformerCategorys;
