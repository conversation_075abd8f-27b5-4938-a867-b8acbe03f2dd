@import '../../style/vars';

.form {
  position: absolute;
  top: 45%;
  left: 50%;
  margin: -160px 0 0 -160px;
  width: 320px;
  height: 320px;
  padding: 36px;
  box-shadow: 0 0 100px var(--shadow-color, rgba(0, 0, 0, 0.08));
  border-radius: 5px;
  background: var(--component-background, @white);
  color: var(--text-color, @text-color);
  border: 1px solid var(--border-color-base, transparent);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;

  button {
    width: 100%;
  }

  :global .sub-para {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    :global a,
    .ant-checkbox-wrapper {
      color: var(--primary-color, @primary-color);
      font-size: 11px;
      transition: color 0.3s ease;
    }
  }
}

.logo {
  text-align: center;

  img {
    height: 60px;
    max-width: 100%;
  }

  h2 {
    vertical-align: text-bottom;
    font-size: 16px;
    text-transform: uppercase;
    font-weight: 700;
    color: var(--primary-color, @primary-color);
    .text-gradient();
    transition: color 0.3s ease;
  }
}

.ant-spin-container,
.ant-spin-nested-loading {
  height: 100%;
}

.footer {
  text-align: center;
  position: absolute;
  width: 100%;
  bottom: 0;
  background: var(--component-background, #fff);
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 24px;
  padding-bottom: 24px;
  min-height: 72px;
  color: var(--text-color, @text-color);
  border-top: 1px solid var(--border-color-split, transparent);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
