import { BreadcrumbComponent } from '@components/common';
import Page from '@components/common/layout/page';
import { FormSubscription } from '@components/subscription/form-subscription';
import { getResponseError } from '@lib/utils';
import { subscriptionService } from '@services/subscription.service';
import { message } from 'antd';
import Head from 'next/head';
import Router from 'next/router';
import { useEffect, useState } from 'react';
import { ISubscriptionCreate } from 'src/interfaces';

interface IProps {
  id: string;
}
function SubscriptionUpdate({ id }: IProps) {
  const [submitting, setSubmitting] = useState(false);
  const [subscription, setSubscription] = useState<ISubscriptionCreate>();

  const getData = async () => {
    try {
      const resp = await subscriptionService.findOne(id);
      setSubscription(resp.data);
    } catch (e) {
      const err = await Promise.resolve(e);
      message.error(getResponseError(err));
    }
  };

  const handleUpdate = async (data: Record<string, any>) => {
    try {
      await setSubmitting(true);
      await subscriptionService.create({ ...data });
      message.success('Updated successfully');
      Router.push('/subscription');
    } catch (e) {
      const err = await Promise.resolve(e);
      message.error(getResponseError(err));
      setSubmitting(false);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <>
      <Head>
        <title>New subscription</title>
      </Head>
      <BreadcrumbComponent
        breadcrumbs={[{ title: 'Subscriptions', href: '/subscription' }, { title: 'Update' }]}
      />
      <Page>
        {subscription
          && (
            <FormSubscription
              onFinish={handleUpdate}
              submiting={submitting}
              subscription={subscription}
            />
          )}
      </Page>
    </>
  );
}
SubscriptionUpdate.getInitialProps = async (ctx) => ctx.query;

export default SubscriptionUpdate;
