/* eslint-disable no-nested-ternary */
import { BreadcrumbComponent } from '@components/common';
import Page from '@components/common/layout/page';
import TableListPaymentFee from '@components/payment-fee/table-list';
import { paymentFeeService } from '@services/payment-fee.service';
import { message } from 'antd';
import Head from 'next/head';
import { useEffect, useRef, useState } from 'react';

function PaymentFeePage() {
  const [loading, setLoading] = useState(true);
  const [records, setRecords] = useState([]);
  const [total, setTotal] = useState(0);

  const limit = 10;
  const page = useRef(1);

  const search = async () => {
    try {
      setLoading(true);
      const resp = await paymentFeeService.search({
        limit,
        offset: (page.current - 1) * limit,
        sort: 'desc',
        sortBy: 'updatedAt'
      });
      setRecords(resp.data.data);
      setTotal(resp.data.total);
    } catch (e) {
      message.error('An error occurred, please try again!');
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (pagination) => {
    page.current = pagination.current;
    search();
  };

  const onDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this record?')) {
      return;
    }
    try {
      await paymentFeeService.delete(id);
      message.success('Deleted successfully');
      page.current = 1;
      search();
    } catch (e) {
      const err = (await Promise.resolve(e)) || {};
      message.error(err.message || 'An error occurred, please try again!');
    }
  };

  useEffect(() => { search(); }, []);

  return (
    <>
      <Head>
        <title>Payment Fee</title>
      </Head>
      <BreadcrumbComponent breadcrumbs={[{ title: 'Payment Fees' }]} />
      <Page>
        <TableListPaymentFee
          dataSource={records}
          rowKey="_id"
          loading={loading}
          pagination={{
            current: page.current,
            pageSize: limit,
            total,
            showSizeChanger: false
          }}
          onChange={handleTableChange.bind(this)}
          onDelete={onDelete.bind(this)}
        />
      </Page>
    </>
  );
}

export default PaymentFeePage;
