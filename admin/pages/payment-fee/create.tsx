import { BreadcrumbComponent } from '@components/common';
import Page from '@components/common/layout/page';
import { FormPaymentFee } from '@components/payment-fee/form';
import { paymentFeeService } from '@services/payment-fee.service';
import { message } from 'antd';
import Head from 'next/head';
import Router from 'next/router';
import { useState } from 'react';

function PaymentFeeCreate() {
  const [loading, setLoading] = useState(false);

  // eslint-disable-next-line consistent-return
  const submit = async (data) => {
    if (data.country === 'US' && !data.state) return message.error('Please select a state', 5);
    try {
      setLoading(true);
      await paymentFeeService.create(data);
      message.success('Created successfully');
      Router.push('/payment-fee');
    } catch (e) {
      const err = (await Promise.resolve(e));
      message.error(err.message || 'Something went wrong, please try again!');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>New Payment Fee</title>
      </Head>
      <BreadcrumbComponent breadcrumbs={[{ title: 'Payment Fee', href: '/payment-fee' }, { title: 'New Payment Fee' }]} />
      <Page>
        <FormPaymentFee onFinish={submit.bind(this)} loading={loading} />
      </Page>
    </>
  );
}

export default PaymentFeeCreate;
