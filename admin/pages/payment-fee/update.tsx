import { BreadcrumbComponent } from '@components/common';
import Loader from '@components/common/base/loader';
import Page from '@components/common/layout/page';
import { FormPaymentFee } from '@components/payment-fee/form';
import { paymentFeeService } from '@services/payment-fee.service';
import { message } from 'antd';
import Head from 'next/head';
import Router from 'next/router';
import { useEffect, useState } from 'react';

interface IProps {
  id: string;
}

function PaymentFeeUpdate({ id }: IProps) {
  const [fetching, setFetching] = useState(true);
  const [loading, setLoading] = useState(false);
  const [paymentFee, setPaymentFee] = useState(null);

  const getData = async () => {
    try {
      const resp = await paymentFeeService.findByIdOrCode(id);
      setPaymentFee(resp.data);
    } catch (e) {
      const err = (await Promise.resolve(e));
      message.error(err.message || 'Something went wrong, please try again!');
    } finally {
      setFetching(false);
    }
  };

  useEffect(() => {
    getData();
  }, [id]);

  const submit = async (data: any) => {
    try {
      setLoading(true);
      await paymentFeeService.update(id, data);
      message.success('Updated successfully');
      Router.push('/payment-fee');
    } catch (e) {
      const err = (await Promise.resolve(e));
      message.error(err.message || 'Something went wrong, please try again!');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Update Payment Fee</title>
      </Head>
      <BreadcrumbComponent
        breadcrumbs={[{ title: 'Payment Fee', href: '/payment-fee' }, { title: 'Update Payment Fee' }]}
      />
      <Page>
        {fetching ? (
          <Loader />
        ) : (
          <FormPaymentFee item={paymentFee} onFinish={submit.bind(this)} loading={loading} />
        )}
      </Page>
    </>
  );
}

PaymentFeeUpdate.getInitialProps = (ctx) => ctx.query;

export default PaymentFeeUpdate;
