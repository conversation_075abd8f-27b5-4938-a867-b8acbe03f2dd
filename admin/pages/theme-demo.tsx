import React from 'react';
import { 
  Card, 
  Button, 
  Input, 
  Select, 
  Table, 
  Tag, 
  Space, 
  Typography,
  Row,
  Col,
  Divider
} from 'antd';
import { 
  UserOutlined, 
  SettingOutlined, 
  HeartOutlined,
  StarOutlined 
} from '@ant-design/icons';
import ThemeToggle from '@components/common/theme-toggle';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

const ThemeDemo: React.FC = () => {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Action',
      key: 'action',
      render: () => (
        <Space size="middle">
          <Button type="link">Edit</Button>
          <Button type="link" danger>Delete</Button>
        </Space>
      ),
    },
  ];

  const data = [
    {
      key: '1',
      name: '<PERSON>',
      status: 'active',
    },
    {
      key: '2',
      name: '<PERSON>',
      status: 'inactive',
    },
    {
      key: '3',
      name: '<PERSON>',
      status: 'active',
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
              <Title level={2}>Theme Demo Page</Title>
              <ThemeToggle showLabel={true} size="large" />
            </div>
            <Paragraph>
              This page demonstrates how the theme system works across different Ant Design components.
              Use the theme toggle above to switch between light, dark, and system modes.
            </Paragraph>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="Form Components" extra={<SettingOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Input 
                placeholder="Enter your name" 
                prefix={<UserOutlined />}
              />
              <Select 
                defaultValue="option1" 
                style={{ width: '100%' }}
                placeholder="Select an option"
              >
                <Option value="option1">Option 1</Option>
                <Option value="option2">Option 2</Option>
                <Option value="option3">Option 3</Option>
              </Select>
              <Space>
                <Button type="primary">Primary Button</Button>
                <Button>Default Button</Button>
                <Button type="dashed">Dashed Button</Button>
              </Space>
            </Space>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="Status & Tags" extra={<HeartOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>Status Tags:</Text>
                <br />
                <Space wrap>
                  <Tag color="success">Success</Tag>
                  <Tag color="processing">Processing</Tag>
                  <Tag color="error">Error</Tag>
                  <Tag color="warning">Warning</Tag>
                  <Tag color="default">Default</Tag>
                </Space>
              </div>
              <Divider />
              <div>
                <Text strong>Interactive Elements:</Text>
                <br />
                <Space>
                  <Button type="text" icon={<StarOutlined />}>
                    Text Button
                  </Button>
                  <Button type="link">Link Button</Button>
                </Space>
              </div>
            </Space>
          </Card>
        </Col>

        <Col span={24}>
          <Card title="Data Table">
            <Table 
              columns={columns} 
              dataSource={data} 
              pagination={false}
              size="middle"
            />
          </Card>
        </Col>

        <Col span={24}>
          <Card title="Typography Examples">
            <Title level={3}>This is a Level 3 Title</Title>
            <Paragraph>
              This is a paragraph with <Text strong>strong text</Text>, <Text italic>italic text</Text>, 
              and <Text code>inline code</Text>. The theme system ensures all text elements 
              have proper contrast ratios in both light and dark modes.
            </Paragraph>
            <Paragraph type="secondary">
              This is secondary text that adapts to the current theme.
            </Paragraph>
            <Text type="success">Success text</Text> | 
            <Text type="warning"> Warning text</Text> | 
            <Text type="danger"> Danger text</Text>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

// Set layout for this page
(ThemeDemo as any).layout = 'primary';

export default ThemeDemo;
