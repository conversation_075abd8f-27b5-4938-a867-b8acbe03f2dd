module.exports = {
  "blue-base": "#1890ff",
  "blue-1": "mix(color(~`colorPalette('@{blue-base}', 8) `), @component-background, 15%)",
  "blue-2": "mix(color(~`colorPalette('@{blue-base}', 7) `), @component-background, 25%)",
  "blue-3": "mix(@blue-base, @component-background, 30%)",
  "blue-4": "mix(@blue-base, @component-background, 45%)",
  "blue-5": "mix(@blue-base, @component-background, 65%)",
  "blue-6": "mix(@blue-base, @component-background, 85%)",
  "blue-7": "mix(color(~`colorPalette('@{blue-base}', 5) `), @component-background, 90%)",
  "blue-8": "mix(color(~`colorPalette('@{blue-base}', 4) `), @component-background, 95%)",
  "blue-9": "mix(color(~`colorPalette('@{blue-base}', 3) `), @component-background, 97%)",
  "blue-10": "mix(color(~`colorPalette('@{blue-base}', 2) `), @component-background, 98%)",
  "purple-base": "#722ed1",
  "purple-1": "mix(color(~`colorPalette('@{purple-base}', 8) `), @component-background, 15%)",
  "purple-2": "mix(color(~`colorPalette('@{purple-base}', 7) `), @component-background, 25%)",
  "purple-3": "mix(@purple-base, @component-background, 30%)",
  "purple-4": "mix(@purple-base, @component-background, 45%)",
  "purple-5": "mix(@purple-base, @component-background, 65%)",
  "purple-6": "mix(@purple-base, @component-background, 85%)",
  "purple-7": "mix(color(~`colorPalette('@{purple-base}', 5) `), @component-background, 90%)",
  "purple-8": "mix(color(~`colorPalette('@{purple-base}', 4) `), @component-background, 95%)",
  "purple-9": "mix(color(~`colorPalette('@{purple-base}', 3) `), @component-background, 97%)",
  "purple-10": "mix(color(~`colorPalette('@{purple-base}', 2) `), @component-background, 98%)",
  "cyan-base": "#13c2c2",
  "cyan-1": "mix(color(~`colorPalette('@{cyan-base}', 8) `), @component-background, 15%)",
  "cyan-2": "mix(color(~`colorPalette('@{cyan-base}', 7) `), @component-background, 25%)",
  "cyan-3": "mix(@cyan-base, @component-background, 30%)",
  "cyan-4": "mix(@cyan-base, @component-background, 45%)",
  "cyan-5": "mix(@cyan-base, @component-background, 65%)",
  "cyan-6": "mix(@cyan-base, @component-background, 85%)",
  "cyan-7": "mix(color(~`colorPalette('@{cyan-base}', 5) `), @component-background, 90%)",
  "cyan-8": "mix(color(~`colorPalette('@{cyan-base}', 4) `), @component-background, 95%)",
  "cyan-9": "mix(color(~`colorPalette('@{cyan-base}', 3) `), @component-background, 97%)",
  "cyan-10": "mix(color(~`colorPalette('@{cyan-base}', 2) `), @component-background, 98%)",
  "green-base": "#52c41a",
  "green-1": "mix(color(~`colorPalette('@{green-base}', 8) `), @component-background, 15%)",
  "green-2": "mix(color(~`colorPalette('@{green-base}', 7) `), @component-background, 25%)",
  "green-3": "mix(@green-base, @component-background, 30%)",
  "green-4": "mix(@green-base, @component-background, 45%)",
  "green-5": "mix(@green-base, @component-background, 65%)",
  "green-6": "mix(@green-base, @component-background, 85%)",
  "green-7": "mix(color(~`colorPalette('@{green-base}', 5) `), @component-background, 90%)",
  "green-8": "mix(color(~`colorPalette('@{green-base}', 4) `), @component-background, 95%)",
  "green-9": "mix(color(~`colorPalette('@{green-base}', 3) `), @component-background, 97%)",
  "green-10": "mix(color(~`colorPalette('@{green-base}', 2) `), @component-background, 98%)",
  "magenta-base": "#eb2f96",
  "magenta-1": "mix(color(~`colorPalette('@{magenta-base}', 8) `), @component-background, 15%)",
  "magenta-2": "mix(color(~`colorPalette('@{magenta-base}', 7) `), @component-background, 25%)",
  "magenta-3": "mix(@magenta-base, @component-background, 30%)",
  "magenta-4": "mix(@magenta-base, @component-background, 45%)",
  "magenta-5": "mix(@magenta-base, @component-background, 65%)",
  "magenta-6": "mix(@magenta-base, @component-background, 85%)",
  "magenta-7": "mix(color(~`colorPalette('@{magenta-base}', 5) `), @component-background, 90%)",
  "magenta-8": "mix(color(~`colorPalette('@{magenta-base}', 4) `), @component-background, 95%)",
  "magenta-9": "mix(color(~`colorPalette('@{magenta-base}', 3) `), @component-background, 97%)",
  "magenta-10": "mix(color(~`colorPalette('@{magenta-base}', 2) `), @component-background, 98%)",
  "pink-base": "#eb2f96",
  "pink-1": "mix(color(~`colorPalette('@{pink-base}', 8) `), @component-background, 15%)",
  "pink-2": "mix(color(~`colorPalette('@{pink-base}', 7) `), @component-background, 25%)",
  "pink-3": "mix(@pink-base, @component-background, 30%)",
  "pink-4": "mix(@pink-base, @component-background, 45%)",
  "pink-5": "mix(@pink-base, @component-background, 65%)",
  "pink-6": "mix(@pink-base, @component-background, 85%)",
  "pink-7": "mix(color(~`colorPalette('@{pink-base}', 5) `), @component-background, 90%)",
  "pink-8": "mix(color(~`colorPalette('@{pink-base}', 4) `), @component-background, 95%)",
  "pink-9": "mix(color(~`colorPalette('@{pink-base}', 3) `), @component-background, 97%)",
  "pink-10": "mix(color(~`colorPalette('@{pink-base}', 2) `), @component-background, 98%)",
  "red-base": "#f5222d",
  "red-1": "mix(color(~`colorPalette('@{red-base}', 8) `), @component-background, 15%)",
  "red-2": "mix(color(~`colorPalette('@{red-base}', 7) `), @component-background, 25%)",
  "red-3": "mix(@red-base, @component-background, 30%)",
  "red-4": "mix(@red-base, @component-background, 45%)",
  "red-5": "mix(@red-base, @component-background, 65%)",
  "red-6": "mix(@red-base, @component-background, 85%)",
  "red-7": "mix(color(~`colorPalette('@{red-base}', 5) `), @component-background, 90%)",
  "red-8": "mix(color(~`colorPalette('@{red-base}', 4) `), @component-background, 95%)",
  "red-9": "mix(color(~`colorPalette('@{red-base}', 3) `), @component-background, 97%)",
  "red-10": "mix(color(~`colorPalette('@{red-base}', 2) `), @component-background, 98%)",
  "orange-base": "#fa8c16",
  "orange-1": "mix(color(~`colorPalette('@{orange-base}', 8) `), @component-background, 15%)",
  "orange-2": "mix(color(~`colorPalette('@{orange-base}', 7) `), @component-background, 25%)",
  "orange-3": "mix(@orange-base, @component-background, 30%)",
  "orange-4": "mix(@orange-base, @component-background, 45%)",
  "orange-5": "mix(@orange-base, @component-background, 65%)",
  "orange-6": "mix(@orange-base, @component-background, 85%)",
  "orange-7": "mix(color(~`colorPalette('@{orange-base}', 5) `), @component-background, 90%)",
  "orange-8": "mix(color(~`colorPalette('@{orange-base}', 4) `), @component-background, 95%)",
  "orange-9": "mix(color(~`colorPalette('@{orange-base}', 3) `), @component-background, 97%)",
  "orange-10": "mix(color(~`colorPalette('@{orange-base}', 2) `), @component-background, 98%)",
  "yellow-base": "#fadb14",
  "yellow-1": "mix(color(~`colorPalette('@{yellow-base}', 8) `), @component-background, 15%)",
  "yellow-2": "mix(color(~`colorPalette('@{yellow-base}', 7) `), @component-background, 25%)",
  "yellow-3": "mix(@yellow-base, @component-background, 30%)",
  "yellow-4": "mix(@yellow-base, @component-background, 45%)",
  "yellow-5": "mix(@yellow-base, @component-background, 65%)",
  "yellow-6": "mix(@yellow-base, @component-background, 85%)",
  "yellow-7": "mix(color(~`colorPalette('@{yellow-base}', 5) `), @component-background, 90%)",
  "yellow-8": "mix(color(~`colorPalette('@{yellow-base}', 4) `), @component-background, 95%)",
  "yellow-9": "mix(color(~`colorPalette('@{yellow-base}', 3) `), @component-background, 97%)",
  "yellow-10": "mix(color(~`colorPalette('@{yellow-base}', 2) `), @component-background, 98%)",
  "volcano-base": "#fa541c",
  "volcano-1": "mix(color(~`colorPalette('@{volcano-base}', 8) `), @component-background, 15%)",
  "volcano-2": "mix(color(~`colorPalette('@{volcano-base}', 7) `), @component-background, 25%)",
  "volcano-3": "mix(@volcano-base, @component-background, 30%)",
  "volcano-4": "mix(@volcano-base, @component-background, 45%)",
  "volcano-5": "mix(@volcano-base, @component-background, 65%)",
  "volcano-6": "mix(@volcano-base, @component-background, 85%)",
  "volcano-7": "mix(color(~`colorPalette('@{volcano-base}', 5) `), @component-background, 90%)",
  "volcano-8": "mix(color(~`colorPalette('@{volcano-base}', 4) `), @component-background, 95%)",
  "volcano-9": "mix(color(~`colorPalette('@{volcano-base}', 3) `), @component-background, 97%)",
  "volcano-10": "mix(color(~`colorPalette('@{volcano-base}', 2) `), @component-background, 98%)",
  "geekblue-base": "#2f54eb",
  "geekblue-1": "mix(color(~`colorPalette('@{geekblue-base}', 8) `), @component-background, 15%)",
  "geekblue-2": "mix(color(~`colorPalette('@{geekblue-base}', 7) `), @component-background, 25%)",
  "geekblue-3": "mix(@geekblue-base, @component-background, 30%)",
  "geekblue-4": "mix(@geekblue-base, @component-background, 45%)",
  "geekblue-5": "mix(@geekblue-base, @component-background, 65%)",
  "geekblue-6": "mix(@geekblue-base, @component-background, 85%)",
  "geekblue-7": "mix(color(~`colorPalette('@{geekblue-base}', 5) `), @component-background, 90%)",
  "geekblue-8": "mix(color(~`colorPalette('@{geekblue-base}', 4) `), @component-background, 95%)",
  "geekblue-9": "mix(color(~`colorPalette('@{geekblue-base}', 3) `), @component-background, 97%)",
  "geekblue-10": "mix(color(~`colorPalette('@{geekblue-base}', 2) `), @component-background, 98%)",
  "lime-base": "#a0d911",
  "lime-1": "mix(color(~`colorPalette('@{lime-base}', 8) `), @component-background, 15%)",
  "lime-2": "mix(color(~`colorPalette('@{lime-base}', 7) `), @component-background, 25%)",
  "lime-3": "mix(@lime-base, @component-background, 30%)",
  "lime-4": "mix(@lime-base, @component-background, 45%)",
  "lime-5": "mix(@lime-base, @component-background, 65%)",
  "lime-6": "mix(@lime-base, @component-background, 85%)",
  "lime-7": "mix(color(~`colorPalette('@{lime-base}', 5) `), @component-background, 90%)",
  "lime-8": "mix(color(~`colorPalette('@{lime-base}', 4) `), @component-background, 95%)",
  "lime-9": "mix(color(~`colorPalette('@{lime-base}', 3) `), @component-background, 97%)",
  "lime-10": "mix(color(~`colorPalette('@{lime-base}', 2) `), @component-background, 98%)",
  "gold-base": "#faad14",
  "gold-1": "mix(color(~`colorPalette('@{gold-base}', 8) `), @component-background, 15%)",
  "gold-2": "mix(color(~`colorPalette('@{gold-base}', 7) `), @component-background, 25%)",
  "gold-3": "mix(@gold-base, @component-background, 30%)",
  "gold-4": "mix(@gold-base, @component-background, 45%)",
  "gold-5": "mix(@gold-base, @component-background, 65%)",
  "gold-6": "mix(@gold-base, @component-background, 85%)",
  "gold-7": "mix(color(~`colorPalette('@{gold-base}', 5) `), @component-background, 90%)",
  "gold-8": "mix(color(~`colorPalette('@{gold-base}', 4) `), @component-background, 95%)",
  "gold-9": "mix(color(~`colorPalette('@{gold-base}', 3) `), @component-background, 97%)",
  "gold-10": "mix(color(~`colorPalette('@{gold-base}', 2) `), @component-background, 98%)",
  "preset-colors": "pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,\n  purple",
  "theme": "dark",
  "ant-prefix": "ant",
  "html-selector": "html",
  "primary-color": "@blue-6",
  "info-color": "@blue-6",
  "success-color": "@green-6",
  "processing-color": "@blue-6",
  "error-color": "@red-5",
  "highlight-color": "@red-5",
  "warning-color": "@gold-6",
  "normal-color": "#d9d9d9",
  "white": "#fff",
  "black": "#000",
  "primary-1": "mix(color(~`colorPalette('@{primary-color}', 8) `), @component-background, 15%)",
  "primary-2": "mix(color(~`colorPalette('@{primary-color}', 7) `), @component-background, 25%)",
  "primary-3": "mix(@primary-color, @component-background, 30%)",
  "primary-4": "mix(@primary-color, @component-background, 45%)",
  "primary-5": "mix(@primary-color, @component-background, 65%)",
  "primary-6": "@primary-color",
  "primary-7": "mix(color(~`colorPalette('@{primary-color}', 5) `), @component-background, 90%)",
  "primary-8": "mix(color(~`colorPalette('@{primary-color}', 4) `), @component-background, 95%)",
  "primary-9": "mix(color(~`colorPalette('@{primary-color}', 3) `), @component-background, 97%)",
  "primary-10": "mix(color(~`colorPalette('@{primary-color}', 2) `), @component-background, 98%)",
  "body-background": "@black",
  "component-background": "#141414",
  "popover-background": "#1f1f1f",
  "popover-customize-border-color": "#3a3a3a",
  "font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,\n  'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',\n  'Noto Color Emoji'",
  "code-family": "'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace",
  "text-color": "fade(@white, 65%)",
  "text-color-secondary": "fade(@white, 45%)",
  "text-color-inverse": "@white",
  "icon-color": "inherit",
  "icon-color-hover": "fade(@white, 75%)",
  "heading-color": "fade(@white, 85%)",
  "heading-color-dark": "fade(@white, 100%)",
  "text-color-dark": "fade(@white, 85%)",
  "text-color-secondary-dark": "fade(@white, 65%)",
  "text-selection-bg": "@primary-color",
  "font-variant-base": "tabular-nums",
  "font-feature-settings-base": "tnum",
  "font-size-base": "14px",
  "font-size-lg": "@font-size-base + 2px",
  "font-size-sm": "12px",
  "heading-1-size": "ceil(@font-size-base * 2.71)",
  "heading-2-size": "ceil(@font-size-base * 2.14)",
  "heading-3-size": "ceil(@font-size-base * 1.71)",
  "heading-4-size": "ceil(@font-size-base * 1.42)",
  "line-height-base": "1.5715",
  "border-radius-base": "2px",
  "border-radius-sm": "@border-radius-base",
  "padding-lg": "24px",
  "padding-md": "16px",
  "padding-sm": "12px",
  "padding-xs": "8px",
  "control-padding-horizontal": "@padding-sm",
  "control-padding-horizontal-sm": "@padding-xs",
  "item-active-bg": "@primary-1",
  "item-hover-bg": "fade(@white, 8%)",
  "iconfont-css-prefix": "anticon",
  "link-color": "@primary-color",
  "link-hover-color": "@primary-5",
  "link-active-color": "@primary-7",
  "link-decoration": "none",
  "link-hover-decoration": "none",
  "ease-base-out": "cubic-bezier(0.7, 0.3, 0.1, 1)",
  "ease-base-in": "cubic-bezier(0.9, 0, 0.3, 0.7)",
  "ease-out": "cubic-bezier(0.215, 0.61, 0.355, 1)",
  "ease-in": "cubic-bezier(0.55, 0.055, 0.675, 0.19)",
  "ease-in-out": "cubic-bezier(0.645, 0.045, 0.355, 1)",
  "ease-out-back": "cubic-bezier(0.12, 0.4, 0.29, 1.46)",
  "ease-in-back": "cubic-bezier(0.71, -0.46, 0.88, 0.6)",
  "ease-in-out-back": "cubic-bezier(0.71, -0.46, 0.29, 1.46)",
  "ease-out-circ": "cubic-bezier(0.08, 0.82, 0.17, 1)",
  "ease-in-circ": "cubic-bezier(0.6, 0.04, 0.98, 0.34)",
  "ease-in-out-circ": "cubic-bezier(0.78, 0.14, 0.15, 0.86)",
  "ease-out-quint": "cubic-bezier(0.23, 1, 0.32, 1)",
  "ease-in-quint": "cubic-bezier(0.755, 0.05, 0.855, 0.06)",
  "ease-in-out-quint": "cubic-bezier(0.86, 0, 0.07, 1)",
  "border-color-base": "#434343",
  "border-color-split": "#303030",
  "border-color-inverse": "@white",
  "border-width-base": "1px",
  "border-style-base": "solid",
  "outline-blur-size": "0",
  "outline-width": "2px",
  "outline-color": "@primary-color",
  "outline-fade": "20%",
  "background-color-light": "fade(@white, 4%)",
  "background-color-base": "fade(@white, 8%)",
  "disabled-color": "fade(@white, 30%)",
  "disabled-bg": "@background-color-base",
  "disabled-color-dark": "fade(@white, 30%)",
  "shadow-color": "rgba(0, 0, 0, 0.45)",
  "shadow-color-inverse": "@component-background",
  "box-shadow-base": "@shadow-2",
  "shadow-1-up": "0 -6px 16px -8px rgba(0, 0, 0, 0.32), 0 -9px 28px 0 rgba(0, 0, 0, 0.2),\n  0 -12px 48px 16px rgba(0, 0, 0, 0.12)",
  "shadow-1-down": "0 6px 16px -8px rgba(0, 0, 0, 0.32), 0 9px 28px 0 rgba(0, 0, 0, 0.2),\n  0 12px 48px 16px rgba(0, 0, 0, 0.12)",
  "shadow-1-left": "-6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05),\n  -12px 0 48px 16px rgba(0, 0, 0, 0.03)",
  "shadow-1-right": "6px 0 16px -8px rgba(0, 0, 0, 0.32), 9px 0 28px 0 rgba(0, 0, 0, 0.2),\n  12px 0 48px 16px rgba(0, 0, 0, 0.12)",
  "shadow-2": "0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32),\n  0 9px 28px 8px rgba(0, 0, 0, 0.2)",
  "btn-font-weight": "400",
  "btn-border-radius-base": "@border-radius-base",
  "btn-border-radius-sm": "@border-radius-base",
  "btn-border-width": "@border-width-base",
  "btn-border-style": "@border-style-base",
  "btn-shadow": "0 2px 0 rgba(0, 0, 0, 0.015)",
  "btn-primary-shadow": "0 2px 0 rgba(0, 0, 0, 0.045)",
  "btn-text-shadow": "0 -1px 0 rgba(0, 0, 0, 0.12)",
  "btn-primary-color": "#fff",
  "btn-primary-bg": "@primary-color",
  "btn-default-color": "@text-color",
  "btn-default-bg": "transparent",
  "btn-default-border": "@border-color-base",
  "btn-danger-color": "#fff",
  "btn-danger-bg": "@error-color",
  "btn-danger-border": "@error-color",
  "btn-disable-color": "@disabled-color",
  "btn-disable-bg": "@disabled-bg",
  "btn-disable-border": "@border-color-base",
  "btn-default-ghost-color": "@text-color",
  "btn-default-ghost-bg": "transparent",
  "btn-default-ghost-border": "fade(@white, 25%)",
  "btn-font-size-lg": "@font-size-lg",
  "btn-font-size-sm": "@font-size-base",
  "btn-padding-horizontal-base": "@padding-md - 1px",
  "btn-padding-horizontal-lg": "@btn-padding-horizontal-base",
  "btn-padding-horizontal-sm": "@padding-xs - 1px",
  "btn-height-base": "32px",
  "btn-height-lg": "40px",
  "btn-height-sm": "24px",
  "btn-circle-size": "@btn-height-base",
  "btn-circle-size-lg": "@btn-height-lg",
  "btn-circle-size-sm": "@btn-height-sm",
  "btn-square-size": "@btn-height-base",
  "btn-square-size-lg": "@btn-height-lg",
  "btn-square-size-sm": "@btn-height-sm",
  "btn-group-border": "@primary-5",
  "btn-link-ghost-color": "@text-color",
  "checkbox-size": "16px",
  "checkbox-color": "@primary-color",
  "checkbox-check-color": "#fff",
  "checkbox-check-bg": "transparent",
  "checkbox-border-width": "@border-width-base",
  "descriptions-bg": "@background-color-light",
  "dropdown-selected-color": "@primary-color",
  "dropdown-menu-submenu-disabled-bg": "transparent",
  "empty-font-size": "@font-size-base",
  "radio-size": "16px",
  "radio-dot-color": "@primary-color",
  "radio-dot-disabled-color": "fade(@white, 20%)",
  "radtio-solid-checked-color": "@white",
  "radio-button-bg": "@btn-default-bg",
  "radio-button-checked-bg": "@btn-default-bg",
  "radio-button-color": "@btn-default-color",
  "radio-button-hover-color": "@primary-5",
  "radio-button-active-color": "@primary-7",
  "radio-disabled-button-checked-bg": "fade(@white, 20%)",
  "radio-disabled-button-checked-color": "@black",
  "screen-xs": "480px",
  "screen-xs-min": "@screen-xs",
  "screen-sm": "576px",
  "screen-sm-min": "@screen-sm",
  "screen-md": "768px",
  "screen-md-min": "@screen-md",
  "screen-lg": "992px",
  "screen-lg-min": "@screen-lg",
  "screen-xl": "1200px",
  "screen-xl-min": "@screen-xl",
  "screen-xxl": "1600px",
  "screen-xxl-min": "@screen-xxl",
  "screen-xs-max": "(@screen-sm-min - 1px)",
  "screen-sm-max": "(@screen-md-min - 1px)",
  "screen-md-max": "(@screen-lg-min - 1px)",
  "screen-lg-max": "(@screen-xl-min - 1px)",
  "screen-xl-max": "(@screen-xxl-min - 1px)",
  "grid-columns": "24",
  "grid-gutter-width": "0",
  "layout-body-background": "@body-background",
  "layout-header-background": "@popover-background",
  "layout-header-height": "64px",
  "layout-header-padding": "0 50px",
  "layout-header-color": "@text-color",
  "layout-footer-padding": "24px 50px",
  "layout-footer-background": "@layout-body-background",
  "layout-sider-background": "@layout-header-background",
  "layout-trigger-height": "48px",
  "layout-trigger-background": "#262626",
  "layout-trigger-color": "#fff",
  "layout-zero-trigger-width": "36px",
  "layout-zero-trigger-height": "42px",
  "layout-sider-background-light": "#fff",
  "layout-trigger-background-light": "#fff",
  "layout-trigger-color-light": "@text-color",
  "zindex-badge": "auto",
  "zindex-table-fixed": "auto",
  "zindex-affix": "10",
  "zindex-back-top": "10",
  "zindex-picker-panel": "10",
  "zindex-popup-close": "10",
  "zindex-modal": "1000",
  "zindex-modal-mask": "1000",
  "zindex-message": "1010",
  "zindex-notification": "1010",
  "zindex-popover": "1030",
  "zindex-dropdown": "1050",
  "zindex-picker": "1050",
  "zindex-tooltip": "1060",
  "animation-duration-slow": "0.3s",
  "animation-duration-base": "0.2s",
  "animation-duration-fast": "0.1s",
  "collapse-panel-border-radius": "@border-radius-base",
  "dropdown-menu-bg": "@popover-background",
  "dropdown-vertical-padding": "5px",
  "dropdown-edge-child-vertical-padding": "4px",
  "dropdown-font-size": "@font-size-base",
  "dropdown-line-height": "22px",
  "label-required-color": "@highlight-color",
  "label-color": "@heading-color",
  "form-warning-input-bg": "@input-bg",
  "form-item-margin-bottom": "24px",
  "form-item-trailing-colon": "true",
  "form-vertical-label-padding": "0 0 8px",
  "form-vertical-label-margin": "0",
  "form-item-label-font-size": "@font-size-base",
  "form-item-label-colon-margin-right": "8px",
  "form-item-label-colon-margin-left": "2px",
  "form-error-input-bg": "@input-bg",
  "input-height-base": "32px",
  "input-height-lg": "40px",
  "input-height-sm": "24px",
  "input-padding-horizontal": "@control-padding-horizontal - 1px",
  "input-padding-horizontal-base": "@input-padding-horizontal",
  "input-padding-horizontal-sm": "@control-padding-horizontal-sm - 1px",
  "input-padding-horizontal-lg": "@input-padding-horizontal",
  "input-padding-vertical-base": "max(\n  round((@input-height-base - @font-size-base * @line-height-base) / 2 * 10) / 10 -\n    @border-width-base,\n  3px\n)",
  "input-padding-vertical-sm": "round((@input-height-sm - @font-size-base * @line-height-base) / 2 * 10) /\n  10 - @border-width-base",
  "input-padding-vertical-lg": "ceil((@input-height-lg - @font-size-lg * @line-height-base) / 2 * 10) /\n  10 - @border-width-base",
  "input-placeholder-color": "fade(@white, 30%)",
  "input-color": "@text-color",
  "input-icon-color": "fade(@white, 30%)",
  "input-border-color": "@border-color-base",
  "input-bg": "transparent",
  "input-number-hover-border-color": "@input-hover-border-color",
  "input-number-handler-active-bg": "@item-hover-bg",
  "input-number-handler-hover-bg": "@primary-5",
  "input-number-handler-bg": "transparent",
  "input-number-handler-border-color": "@border-color-base",
  "input-addon-bg": "@background-color-light",
  "input-hover-border-color": "@primary-5",
  "input-disabled-bg": "@disabled-bg",
  "input-outline-offset": "0 0",
  "input-icon-hover-color": "fade(@white, 85%)",
  "mentions-dropdown-bg": "@popover-background",
  "mentions-dropdown-menu-item-hover-bg": "@mentions-dropdown-bg",
  "select-border-color": "@border-color-base",
  "select-item-selected-font-weight": "600",
  "select-dropdown-bg": "@popover-background",
  "select-item-selected-bg": "@primary-1",
  "select-item-active-bg": "@item-hover-bg",
  "select-dropdown-vertical-padding": "@dropdown-vertical-padding",
  "select-dropdown-font-size": "@dropdown-font-size",
  "select-dropdown-line-height": "@dropdown-line-height",
  "select-background": "transparent",
  "select-clear-background": "@component-background",
  "select-selection-item-bg": "fade(@white, 8)",
  "select-selection-item-border-color": "@border-color-split",
  "cascader-bg": "transparent",
  "cascader-item-selected-bg": "@primary-1",
  "cascader-menu-bg": "@popover-background",
  "cascader-menu-border-color-split": "@border-color-split",
  "cascader-dropdown-vertical-padding": "@dropdown-vertical-padding",
  "cascader-dropdown-edge-child-vertical-padding": "@dropdown-edge-child-vertical-padding",
  "cascader-dropdown-font-size": "@dropdown-font-size",
  "cascader-dropdown-line-height": "@dropdown-line-height",
  "anchor-bg": "@component-background",
  "anchor-border-color": "@border-color-split",
  "tooltip-max-width": "250px",
  "tooltip-color": "#fff",
  "tooltip-bg": "#434343",
  "tooltip-arrow-width": "5px",
  "tooltip-distance": "@tooltip-arrow-width - 1px + 4px",
  "tooltip-arrow-color": "@tooltip-bg",
  "popover-bg": "@popover-background",
  "popover-color": "@text-color",
  "popover-min-width": "177px",
  "popover-arrow-width": "6px",
  "popover-arrow-color": "@popover-bg",
  "popover-arrow-outer-color": "@popover-bg",
  "popover-distance": "@popover-arrow-width + 4px",
  "modal-body-padding": "24px",
  "modal-header-bg": "@popover-background",
  "modal-header-border-color-split": "@border-color-split",
  "modal-content-bg": "@popover-background",
  "modal-heading-color": "@heading-color",
  "modal-footer-bg": "transparent",
  "modal-footer-border-color-split": "@border-color-split",
  "modal-footer-padding-vertical": "10px",
  "modal-footer-padding-horizontal": "16px",
  "modal-mask-bg": "fade(@black, 45%)",
  "progress-default-color": "@processing-color",
  "progress-remaining-color": "@background-color-base",
  "progress-text-color": "@text-color",
  "progress-radius": "100px",
  "progress-steps-item-bg": "fade(@white, 8%)",
  "menu-inline-toplevel-item-height": "40px",
  "menu-item-height": "40px",
  "menu-collapsed-width": "80px",
  "menu-bg": "@component-background",
  "menu-popup-bg": "@popover-background",
  "menu-item-color": "@text-color",
  "menu-highlight-color": "@primary-color",
  "menu-item-active-bg": "@primary-1",
  "menu-item-active-border-width": "3px",
  "menu-item-group-title-color": "@text-color-secondary",
  "menu-icon-size": "@font-size-base",
  "menu-icon-size-lg": "@font-size-lg",
  "menu-item-vertical-margin": "4px",
  "menu-item-font-size": "@font-size-base",
  "menu-item-boundary-margin": "8px",
  "menu-dark-color": "@text-color-secondary-dark",
  "menu-dark-bg": "@popover-background",
  "menu-dark-arrow-color": "#fff",
  "menu-dark-submenu-bg": "@component-background",
  "menu-dark-highlight-color": "#fff",
  "menu-dark-item-active-bg": "@primary-color",
  "menu-dark-selected-item-icon-color": "@white",
  "menu-dark-selected-item-text-color": "@white",
  "menu-dark-item-hover-bg": "transparent",
  "spin-dot-size-sm": "14px",
  "spin-dot-size": "20px",
  "spin-dot-size-lg": "32px",
  "table-bg": "@component-background",
  "table-header-bg": "#1d1d1d",
  "table-header-color": "@heading-color",
  "table-header-sort-bg": "#262626",
  "table-body-sort-bg": "fade(@white, 1%)",
  "table-row-hover-bg": "#262626",
  "table-selected-row-color": "inherit",
  "table-selected-row-bg": "@primary-1",
  "table-body-selected-sort-bg": "@table-selected-row-bg",
  "table-selected-row-hover-bg": "@table-selected-row-bg",
  "table-expanded-row-bg": "@table-header-bg",
  "table-padding-vertical": "16px",
  "table-padding-horizontal": "16px",
  "table-border-radius-base": "@border-radius-base",
  "table-footer-bg": "@background-color-light",
  "table-footer-color": "@heading-color",
  "table-header-bg-sm": "@table-header-bg",
  "table-header-sort-active-bg": "#303030",
  "table-header-filter-active-bg": "#434343",
  "table-filter-btns-bg": "@popover-background",
  "table-filter-dropdown-bg": "@popover-background",
  "table-expand-icon-bg": "transparent",
  "tag-default-bg": "@background-color-light",
  "tag-default-color": "@text-color",
  "tag-font-size": "@font-size-sm",
  "picker-bg": "transparent",
  "picker-basic-cell-hover-color": "@item-hover-bg",
  "picker-basic-cell-active-with-range-color": "@primary-1",
  "picker-basic-cell-hover-with-range-color": "darken(@primary-color, 35%)",
  "picker-basic-cell-disabled-bg": "#303030",
  "picker-border-color": "@border-color-split",
  "picker-date-hover-range-border-color": "darken(@primary-color, 20%)",
  "picker-date-hover-range-color": "@picker-basic-cell-hover-with-range-color",
  "calendar-bg": "@popover-background",
  "calendar-input-bg": "@calendar-bg",
  "calendar-border-color": "transparent",
  "calendar-item-active-bg": "@item-active-bg",
  "calendar-full-bg": "@component-background",
  "calendar-full-panel-bg": "@calendar-full-bg",
  "carousel-dot-width": "16px",
  "carousel-dot-height": "3px",
  "carousel-dot-active-width": "24px",
  "badge-height": "20px",
  "badge-dot-size": "6px",
  "badge-font-size": "@font-size-sm",
  "badge-font-weight": "normal",
  "badge-status-size": "6px",
  "badge-text-color": "@white",
  "rate-star-color": "@yellow-6",
  "rate-star-bg": "fade(@white, 12%)",
  "card-head-color": "@heading-color",
  "card-head-background": "transparent",
  "card-head-padding": "16px",
  "card-inner-head-padding": "12px",
  "card-padding-base": "24px",
  "card-actions-background": "@background-color-light",
  "card-skeleton-bg": "#303030",
  "card-background": "@component-background",
  "card-shadow": "0 1px 2px -2px rgba(0, 0, 0, 0.64), 0 3px 6px 0 rgba(0, 0, 0, 0.48),\n  0 5px 12px 4px rgba(0, 0, 0, 0.36)",
  "card-radius": "@border-radius-base",
  "comment-bg": "transparent",
  "comment-padding-base": "16px 0",
  "comment-nest-indent": "44px",
  "comment-font-size-base": "@font-size-base",
  "comment-font-size-sm": "@font-size-sm",
  "comment-author-name-color": "@text-color-secondary",
  "comment-author-time-color": "fade(@white, 30%)",
  "comment-action-color": "@text-color-secondary",
  "comment-action-hover-color": "fade(@white, 65%)",
  "tabs-card-head-background": "@background-color-light",
  "tabs-card-height": "40px",
  "tabs-card-active-color": "@primary-color",
  "tabs-title-font-size": "@font-size-base",
  "tabs-title-font-size-lg": "@font-size-lg",
  "tabs-title-font-size-sm": "@font-size-base",
  "tabs-ink-bar-color": "@primary-color",
  "tabs-bar-margin": "0 0 16px 0",
  "tabs-horizontal-margin": "0 32px 0 0",
  "tabs-horizontal-margin-rtl": "0 0 0 32px",
  "tabs-horizontal-padding": "12px 0",
  "tabs-horizontal-padding-lg": "16px 0",
  "tabs-horizontal-padding-sm": "8px 0",
  "tabs-vertical-padding": "8px 24px",
  "tabs-vertical-margin": "0 0 16px 0",
  "tabs-scrolling-size": "32px",
  "tabs-highlight-color": "@primary-color",
  "tabs-hover-color": "@primary-5",
  "tabs-active-color": "@primary-7",
  "tabs-card-gutter": "2px",
  "tabs-card-tab-active-border-top": "2px solid transparent",
  "back-top-color": "#fff",
  "back-top-bg": "@text-color-secondary",
  "back-top-hover-bg": "@text-color",
  "avatar-size-base": "32px",
  "avatar-size-lg": "40px",
  "avatar-size-sm": "24px",
  "avatar-font-size-base": "18px",
  "avatar-font-size-lg": "24px",
  "avatar-font-size-sm": "14px",
  "avatar-bg": "fade(@white, 30%)",
  "avatar-color": "#fff",
  "avatar-border-radius": "@border-radius-base",
  "switch-height": "22px",
  "switch-sm-height": "16px",
  "switch-min-width": "44px",
  "switch-sm-min-width": "28px",
  "switch-sm-checked-margin-left": "-(@switch-sm-height - 3px)",
  "switch-disabled-opacity": "0.4",
  "switch-color": "@primary-color",
  "switch-bg": "@white",
  "switch-shadow-color": "fade(#00230b, 20%)",
  "pagination-item-bg": "transparent",
  "pagination-item-size": "32px",
  "pagination-item-size-sm": "24px",
  "pagination-font-family": "Arial",
  "pagination-font-weight-active": "500",
  "pagination-item-bg-active": "transparent",
  "pagination-item-link-bg": "transparent",
  "pagination-item-disabled-color-active": "@black",
  "pagination-item-disabled-bg-active": "fade(@white, 25%)",
  "pagination-item-input-bg": "@pagination-item-bg",
  "page-header-padding": "24px",
  "page-header-padding-vertical": "16px",
  "page-header-padding-breadcrumb": "12px",
  "page-header-back-color": "@icon-color",
  "page-header-ghost-bg": "transparent",
  "breadcrumb-base-color": "@text-color-secondary",
  "breadcrumb-last-item-color": "@text-color",
  "breadcrumb-font-size": "@font-size-base",
  "breadcrumb-icon-font-size": "@font-size-base",
  "breadcrumb-link-color": "@text-color-secondary",
  "breadcrumb-link-color-hover": "@primary-5",
  "breadcrumb-separator-color": "@text-color-secondary",
  "breadcrumb-separator-margin": "0 @padding-xs",
  "slider-margin": "10px 6px 10px",
  "slider-rail-background-color": "#262626",
  "slider-rail-background-color-hover": "@border-color-base",
  "slider-track-background-color": "@primary-3",
  "slider-track-background-color-hover": "@primary-4",
  "slider-handle-border-width": "2px",
  "slider-handle-background-color": "@component-background",
  "slider-handle-color": "@primary-3",
  "slider-handle-color-hover": "@primary-4",
  "slider-handle-color-focus": "tint(@primary-color, 20%)",
  "slider-handle-color-focus-shadow": "fade(@primary-color, 12%)",
  "slider-handle-color-tooltip-open": "@primary-color",
  "slider-handle-shadow": "0",
  "slider-dot-border-color": "@border-color-split",
  "slider-dot-border-color-active": "@primary-4",
  "slider-disabled-color": "@disabled-color",
  "slider-disabled-background-color": "@component-background",
  "tree-bg": "transparent",
  "tree-title-height": "24px",
  "tree-child-padding": "18px",
  "tree-directory-selected-color": "#fff",
  "tree-directory-selected-bg": "@primary-color",
  "tree-node-hover-bg": "@item-hover-bg",
  "tree-node-selected-bg": "@primary-2",
  "collapse-header-padding": "12px 16px",
  "collapse-header-padding-extra": "40px",
  "collapse-header-bg": "@background-color-light",
  "collapse-content-padding": "@padding-md",
  "collapse-content-bg": "@component-background",
  "skeleton-color": "#303030",
  "skeleton-to-color": "fade(@white, 16%)",
  "transfer-header-height": "40px",
  "transfer-disabled-bg": "@disabled-bg",
  "transfer-list-height": "200px",
  "transfer-item-hover-bg": "#262626",
  "message-notice-content-padding": "10px 16px",
  "message-notice-content-bg": "@popover-background",
  "wave-animation-width": "6px",
  "alert-success-border-color": "@green-3",
  "alert-success-bg-color": "@green-1",
  "alert-success-icon-color": "@success-color",
  "alert-info-border-color": "@primary-3",
  "alert-info-bg-color": "@primary-1",
  "alert-info-icon-color": "@info-color",
  "alert-warning-border-color": "@gold-3",
  "alert-warning-bg-color": "@gold-1",
  "alert-warning-icon-color": "@warning-color",
  "alert-error-border-color": "@red-3",
  "alert-error-bg-color": "@red-1",
  "alert-error-icon-color": "@error-color",
  "alert-message-color": "@heading-color",
  "alert-text-color": "@text-color",
  "alert-close-color": "@text-color-secondary",
  "alert-close-hover-color": "@icon-color-hover",
  "list-header-background": "transparent",
  "list-footer-background": "transparent",
  "list-empty-text-padding": "@padding-md",
  "list-item-padding": "@padding-sm 0",
  "list-item-meta-margin-bottom": "@padding-md",
  "list-item-meta-avatar-margin-right": "@padding-md",
  "list-item-meta-title-margin-bottom": "@padding-sm",
  "list-customize-card-bg": "transparent",
  "statistic-title-font-size": "@font-size-base",
  "statistic-content-font-size": "24px",
  "statistic-unit-font-size": "16px",
  "statistic-font-family": "@font-family",
  "drawer-header-padding": "16px 24px",
  "drawer-body-padding": "24px",
  "drawer-bg": "@popover-background",
  "drawer-footer-padding-vertical": "@modal-footer-padding-vertical",
  "drawer-footer-padding-horizontal": "@modal-footer-padding-horizontal",
  "timeline-width": "2px",
  "timeline-color": "@border-color-split",
  "timeline-dot-border-width": "2px",
  "timeline-dot-color": "@primary-color",
  "timeline-dot-bg": "@component-background",
  "timeline-item-padding-bottom": "20px",
  "typography-title-font-weight": "600",
  "typography-title-margin-top": "1.2em",
  "typography-title-margin-bottom": "0.5em",
  "upload-actions-color": "@text-color-secondary",
  "process-tail-color": "@border-color-split",
  "steps-nav-arrow-color": "fade(@white, 20%)",
  "steps-background": "transparent",
  "notification-bg": "@popover-background"
};