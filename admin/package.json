{"name": "xfans-back-office", "version": "3.3.1", "private": true, "scripts": {"dev": "next dev -p 8082", "build": "next build", "start": "next start -p 8082", "lint": "eslint \"{pages,src,server}/**/*.ts\" --fix && eslint \"{pages,src,server}/**/*.tsx\" --fix"}, "dependencies": {"@reduxjs/toolkit": "^1.8.5", "antd": "^4.23.6", "antd-img-crop": "^4.21.0", "enquire-js": "^0.2.1", "isomorphic-unfetch": "^4.0.2", "js-cookie": "^3.0.5", "moment": "^2.30.1", "next": "^12.2.5", "next-cookies": "^2.0.3", "next-redux-saga": "^4.1.2", "next-redux-wrapper": "^8.1.0", "react": "18.2.0", "react-dom": "18.2.0", "react-helmet": "^6.1.0", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^8.0.2", "redux-actions": "^2.6.5", "redux-saga": "^1.3.0", "reselect": "^5.1.0", "suneditor": "^2.45.1", "suneditor-react": "^3.6.1", "xhr2": "^0.2.1"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/node": "^20.11.19", "@types/node-fetch": "^2.6.11", "@types/react": "18.2.56", "@types/redux-actions": "^2.6.5", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "babel-plugin-import": "^1.13.8", "eslint": "^8.56.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^12.0.0", "less": "^4.2.0", "less-loader": "^12.2.0", "next-compose-plugins": "^2.2.1", "next-with-less": "^3.0.1", "null-loader": "^4.0.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}