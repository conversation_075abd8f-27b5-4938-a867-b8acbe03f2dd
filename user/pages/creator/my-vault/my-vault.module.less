// My Vault Page - Professional, Modern, Smart and Intuitive Dark Mode Styling
// Comprehensive CSS variables implementation for both desktop and mobile

.vaultContainer {
  .main-container {
    margin: auto;
    max-width: 1200px;
    width: 100%;
    padding: 20px;
    transition: all 0.3s ease;
  }
}

.headerPage {
  .header-page {
    margin-bottom: 24px;
    padding: 16px 0;
    border-bottom: 1px solid var(--border-color-split);
    transition: border-color 0.3s ease;
  }
}

.vaultGrid {
  margin-top: 24px;
  
  .ant-row {
    gap: 16px;
  }
}

// Vault Card Styling - Professional and Modern
.vaultCard {
  border-radius: 12px !important;
  cursor: pointer;
  height: 100%;
  background: var(--bg-card) !important;
  box-shadow: var(--shadow-1) !important;
  border: 1px solid var(--border-color-base) !important;
  transition: all 0.3s ease !important;
  overflow: hidden;
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-3) !important;
    border-color: var(--primary-color-fade) !important;
  }

  .ant-card-body {
    padding: 0 !important;
  }
}

// Media Preview Section
.mediaPreview {
  height: 160px;
  background: var(--bg-secondary);
  border-radius: 12px 12px 0 0;
  margin-bottom: 0;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  transition: background-color 0.3s ease;

  &.allMediaPreview {
    background: var(--bg-secondary);
    border-radius: 0;
  }

  &.categoryPreview {
    background: var(--bg-secondary);
    border-radius: 0;
  }
}

// Preview Images Container
.previewContainer {
  width: 234px;
  height: 120px;
  position: relative;
}

.previewImage {
  position: absolute;
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid var(--bg-card);
  box-shadow: var(--shadow-2);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    z-index: 10;
  }
}

// Empty State Styling
.emptyPreview {
  display: flex;
  height: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-color-tertiary);
  font-size: 14px;
  transition: color 0.3s ease;

  .anticon {
    font-size: 32px;
    margin-bottom: 8px;
    color: var(--text-color-tertiary);
    transition: color 0.3s ease;
  }

  span {
    font-weight: 500;
  }
}

// Video Overlay Icon
.videoOverlay {
  position: absolute;
  right: 8px;
  bottom: 8px;
  background: rgba(0, 0, 0, 0.7);
  padding: 6px;
  border-radius: 6px;
  color: var(--color-white);
  font-size: 14px;
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
  }
}

// Category Info Section
.categoryInfo {
  padding: 16px;
  border-top: 1px solid var(--border-color-split);
  background: var(--bg-card);
  transition: all 0.3s ease;

  .categoryName {
    font-size: 15px;
    font-weight: 600;
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
    transition: color 0.3s ease;
  }

  .categoryStats {
    font-size: 13px;
    color: var(--text-color-secondary);
    transition: color 0.3s ease;

    .anticon {
      margin-right: 4px;
      color: var(--text-color-tertiary);
    }

    span {
      margin-right: 16px;
      display: inline-flex;
      align-items: center;
    }
  }
}

// Action Buttons Container
.categoryActions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 6px;
  opacity: 0;
  transition: opacity 0.3s ease;
  background: var(--bg-overlay);
  padding: 6px 10px;
  border-radius: 8px;
  box-shadow: var(--shadow-2);
  backdrop-filter: blur(8px);
  z-index: 10;

  .ant-btn {
    min-width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: 6px;
    background: var(--bg-card);
    color: var(--text-color);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-1);

    &:hover {
      transform: scale(1.1);
      box-shadow: var(--shadow-2);
    }

    &.editBtn {
      color: var(--primary-color);
      
      &:hover {
        background: var(--primary-color-light);
        color: var(--primary-color);
      }
    }

    &.deleteBtn {
      color: var(--error-color);
      
      &:hover {
        background: rgba(255, 77, 79, 0.1);
        color: var(--error-color);
      }
    }
  }
}

// Show actions on card hover
.vaultCard:hover .categoryActions {
  opacity: 1;
}

// Inline Edit Input
.inlineEdit {
  .ant-input {
    background: var(--bg-card);
    border-color: var(--primary-color);
    color: var(--text-color);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-light);
    }
  }
}

// Skeleton Loading Cards
.skeletonCard {
  .ant-skeleton {
    .ant-skeleton-title {
      background: var(--bg-disabled);
    }

    .ant-skeleton-paragraph li {
      background: var(--bg-disabled);
    }
  }
}

// Modal Styling
.createModal {
  .ant-modal-content {
    background: var(--bg-modal);
    border-radius: 12px;
    overflow: hidden;
  }

  .ant-modal-header {
    background: var(--bg-modal);
    border-bottom: 1px solid var(--border-color-split);
    padding: 20px 24px;

    .ant-modal-title {
      color: var(--text-color);
      font-weight: 600;
    }
  }

  .ant-modal-body {
    background: var(--bg-modal);
    padding: 24px;
  }

  .ant-modal-close {
    color: var(--text-color-secondary);
    
    &:hover {
      color: var(--text-color);
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .vaultContainer .main-container {
    padding: 16px;
  }

  .mediaPreview {
    height: 140px;
  }

  .previewContainer {
    width: 200px;
    height: 100px;
  }

  .previewImage {
    width: 100px;
    height: 100px;
  }

  .categoryInfo {
    padding: 12px;

    .categoryName {
      font-size: 14px;
    }

    .categoryStats {
      font-size: 12px;
    }
  }

  .categoryActions {
    top: 8px;
    right: 8px;
    padding: 4px 6px;

    .ant-btn {
      min-width: 28px;
      height: 28px;
    }
  }
}

@media (max-width: 576px) {
  .vaultGrid {
    .ant-col {
      margin-bottom: 16px;
    }
  }

  .mediaPreview {
    height: 120px;
  }

  .categoryInfo {
    padding: 10px;

    .categoryStats span {
      margin-right: 12px;
    }
  }
}
