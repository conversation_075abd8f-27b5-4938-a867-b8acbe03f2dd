.queueContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.backButton {
  color: #666;
}

.addButton {
  background-color: #7e3ff2;
  border-color: #7e3ff2;

  &:hover,
  &:focus {
    background-color: #6a34cc;
    border-color: #6a34cc;
  }
}

.contentContainer {
  display: flex;
  flex: 1;
  overflow: hidden;
  margin-left: 20px;
  position: relative;
  min-height: 400px;

  &.isLoading {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.7);
      z-index: 1;
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.loadingPlaceholder {
  animation-duration: 1.5s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: shimmer;
  animation-timing-function: linear;
  background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
  background-size: 800px 104px;
}

.calendarSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #f0f0f0;
  overflow: hidden;
}

.calendarHeader {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.monthNavigation {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.statsContainer {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 20px;
  padding: 0 16px;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: default;

  &:hover {
    transform: translateY(-2px);
  }

  .statCount {
    font-size: 18px;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
  }

  .statLabel {
    font-size: 14px;
    color: #666;
  }
}

.postStat {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;

  .statLabel {
    color: #1890ff;
  }
}

.messageStat {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;

  .statLabel {
    color: #faad14;
  }
}

.currentMonth {
  font-size: 18px;
  font-weight: 500;
  margin: 0 16px;
}

.navButton {
  color: #666;
}

.filterTabs {
  :global(.ant-tabs-nav) {
    margin-bottom: 0;
  }

  :global(.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
    color: #7e3ff2;
  }

  :global(.ant-tabs-ink-bar) {
    background-color: #7e3ff2;
  }
}

.calendar {
  flex: 1;
  overflow: auto;

  :global(.ant-picker-calendar) {
    background: transparent;
  }

  :global(.ant-picker-calendar-date) {
    height: auto;
    margin: 0;
    padding: 0;
    border: none;
  }

  :global(.ant-picker-calendar-date-value) {
    display: none;
  }

  :global(.ant-picker-cell) {
    padding: 0;
  }

  :global(.ant-picker-cell-in-view) {
    color: rgba(0, 0, 0, 0.85);
  }

  :global(.ant-picker-calendar-date-today) {
    .dateNumber {
      color: #7e3ff2;
      font-weight: bold;
    }
  }
}

.dateCell {
  height: 100%;
  min-height: 80px;
  padding: 8px;
  border: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;

  &:hover {
    background-color: rgba(126, 63, 242, 0.05);
  }

  &.selectedDate {
    background-color: rgba(126, 63, 242, 0.1);
    border-color: #7e3ff2;
  }

  &.hasContent {
    .dateNumber {
      font-weight: 500;
    }
  }
}

.dateNumber {
  font-size: 14px;
  margin-bottom: 4px;
}

.scheduledIndicator {
  position: absolute;
  bottom: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
}

.postIndicator,
.messageIndicator {
  height: 4px;
  flex: 1;
  border-radius: 2px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.postIndicator {
  background-color: #1890ff;
}

.messageIndicator {
  background-color: #faad14;
}

.indicatorCount {
  position: absolute;
  top: -14px;
  font-size: 10px;
  font-weight: 500;
  color: #666;
  background: rgba(255, 255, 255, 0.9);
  padding: 0 4px;
  border-radius: 8px;
  line-height: 1;
}

.sidebarSection {
  width: 350px;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #f0f0f0;
  background-color: #fff;
  overflow: hidden;
}

.mobileSidebar {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
}

.sidebarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;

  .headerTop {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
}

.sidebarDate {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.selectedDateStats {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.selectedStatItem {
  display: flex;
  align-items: baseline;
  gap: 4px;

  .selectedStatCount {
    font-size: 16px;
    font-weight: 600;
    color: #7e3ff2;
  }

  .selectedStatLabel {
    font-size: 13px;
    color: #666;
  }
}

.sidebarMenuButton {
  color: #666;
}

.scheduledItems {
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 60vh;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
  color: #999;
  text-align: center;
}

.scheduledItem {
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &.post {
    border-left: 4px solid #1890ff;
  }

  &.message {
    border-left: 4px solid #faad14;
  }
}

.itemHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.itemTime {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.itemType {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 12px;
}

.itemMenuButton {
  color: #666;
  padding: 4px;
  height: auto;
}

.itemContent {
  margin-bottom: 12px;
  word-break: break-word;
  position: relative;
}

.editContentWrapper {
  position: relative;
  background: #fff;
  border-radius: 4px;
}

.editInput {
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  font-size: 14px;
  transition: all 0.3s;
  resize: none;

  &:hover {
    border-color: #7e3ff2;
  }

  &:focus {
    border-color: #7e3ff2;
    box-shadow: 0 0 0 2px rgba(126, 63, 242, 0.2);
    outline: none;
  }
}

.editActions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.editButton {
  &[type='primary'] {
    background-color: #7e3ff2;
    border-color: #7e3ff2;

    &:hover {
      background-color: #6a34cc;
      border-color: #6a34cc;
    }
  }
}

.contentText {
  padding: 8px 0;
  min-height: 24px;
}

.itemMedia {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 8px;
  margin-top: 12px;

  .mediaWrapper {
    position: relative;
    padding-top: 100%; // 1:1 Aspect ratio
    overflow: hidden;
    border-radius: 8px;
    background-color: #f5f5f5;
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.02);
    }

    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &.hasMore {
      &::after {
        content: attr(data-remaining);
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: 600;
      }
    }
  }
}

.viewSelectedButton {
  position: absolute;
  bottom: 24px;
  right: 24px;
  background-color: #7e3ff2;
  border-color: #7e3ff2;
  box-shadow: 0 2px 8px rgba(126, 63, 242, 0.4);

  &:hover,
  &:focus {
    background-color: #6a34cc;
    border-color: #6a34cc;
  }
}

.deleteMenuItem {
  color: #ff4d4f !important;

  &:hover {
    background-color: #fff1f0 !important;
  }
}

.deleteModal {
  :global(.ant-modal-content) {
    border-radius: 8px;
    overflow: hidden;
  }

  :global(.ant-modal-header) {
    border-bottom: none;
    padding: 24px 24px 0;
  }

  :global(.ant-modal-body) {
    padding: 20px 24px 24px;
  }
}

.deleteModalTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #000000;
  font-weight: 500;

  .deleteIcon {
    color: #ff4d4f;
    font-size: 22px;
  }
}

.deleteModalContent {
  p {
    margin: 0;
    font-size: 14px;

    &.deleteWarning {
      color: #999;
      margin-top: 8px;
      font-size: 13px;
    }
  }
}

.deleteModalActions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

.cancelButton {
  &:hover {
    border-color: #d9d9d9;
    color: #000000;
  }
}

.confirmButton {
  background-color: #ff4d4f;
  border-color: #ff4d4f;

  &:hover,
  &:focus {
    background-color: #ff7875;
    border-color: #ff7875;
  }

  &:active {
    background-color: #d9363e;
    border-color: #d9363e;
  }

  &[disabled] {
    background-color: #ff7875;
    border-color: #ff7875;
    opacity: 0.7;
  }
}

.messageDetails {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
  flex-wrap: wrap;

  .itemRecipient {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    background: rgba(0, 0, 0, 0.02);
    padding: 4px 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
  }

  .itemPrice {
    display: flex;
    align-items: center;
    gap: 4px;
    background: #f6f8ff;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid rgba(24, 144, 255, 0.1);

    .priceLabel {
      color: rgba(0, 0, 0, 0.45);
      font-size: 13px;
    }

    .priceValue {
      color: #1890ff;
      font-weight: 500;
      font-size: 14px;
    }
  }
}

// Responsive styles
@media (max-width: 767px) {
  .contentContainer {
    flex-direction: column;
    margin-left: 0px;
  }

  .calendarSection {
    border-right: none;
  }

  .sidebarSection {
    width: 100%;
    border-left: none;
    border-top: 1px solid #f0f0f0;
  }

  .calendar {
    :global(.ant-picker-calendar-date) {
      min-height: 60px;
    }
  }

  .dateCell {
    min-height: 60px;
  }

  .header {
    padding: 16px 8px;
  }

  .headerRight {
    padding-right: 8px;
  }

  .scheduledItems {
    flex: 1;
  }

  .itemMedia {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 4px;
  }

  .editContentWrapper {
    margin: 0 -8px;
  }

  .editActions {
    padding: 0 8px;
  }

  .deleteModal {
    max-width: 90%;
    margin: auto;

    :global(.ant-modal-content) {
      border-radius: 0;
    }
  }

  .deleteModalActions {
    margin-top: 32px;

    button {
      flex: 1;
      padding: 6px 0;
    }
  }

  .statsContainer {
    gap: 12px;
    margin: 16px 0;
    padding: 0 8px;
  }

  .statItem {
    flex: 1;
    justify-content: center;
    padding: 12px 8px;

    .statCount {
      font-size: 16px;
    }

    .statLabel {
      font-size: 13px;
    }
  }

  .sidebarMenuButton {
    display: none;
  }

  .messageDetails {
    gap: 8px;
    margin-bottom: 6px;

    .itemRecipient,
    .itemPrice {
      font-size: 13px;
      padding: 3px 6px;
    }
  }
}

:global(.ant-modal) {
  .ant-modal-body {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }
}
