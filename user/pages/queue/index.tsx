import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import {
  Calendar,
  Tabs,
  Button,
  Dropdown,
  Menu,
  Modal,
  Input,
  message
} from 'antd';
import {
  LeftOutlined,
  RightOutlined,
  PlusOutlined,
  MoreOutlined,
  ArrowLeftOutlined,
  ClockCircleOutlined,
  MessageOutlined,
  PictureOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
import 'dayjs/locale/en';
import { useSelector } from 'react-redux';

import styles from './queue.module.less';
import { feedService } from '@services/feed.service';
import { messageService } from '@services/message.service';
import { ErrorView, LoadingView } from '@components/queue/StateViews';
import ContentTypeModal from '@components/queue/ContentTypeModal';
import ScheduleMessageModal from '@components/queue/ScheduleMessageModal';

const { TabPane } = Tabs;

dayjs.extend(weekday);
dayjs.extend(localeData);

interface MediaFile {
  _id: string;
  url: string;
  thumbnails?: string[];
  mimeType: string;
  mediaType: 'image' | 'video';
  name: string;
}

interface ScheduledPost {
  _id: string;
  text?: string;
  type: 'text' | 'image' | 'video';
  files?: MediaFile[];
  status: string;
  scheduledAt: string; // API returns dates in ISO format
  performer?: {
    name: string;
    avatar: string;
    username: string;
  };
}

interface ScheduledMessage {
  _id: string;
  text?: string;
  type: string;
  files?: MediaFile[];
  fileId?: string[];
  conversationId: string;
  senderSource: string;
  senderId: string;
  createdAt: string;
  isScheduled: boolean;
  recipientName: string;
  price: number;
}

const QueuePage: React.FC = () => {
  const router = useRouter();
  const user = useSelector((state: any) => state.user.current);

  const [selectedDate, setSelectedDate] = useState<string>(
    dayjs().format('YYYY-MM-DD')
  );
  const [currentMonth, setCurrentMonth] = useState<Dayjs>(dayjs());
  const [activeTab, setActiveTab] = useState<string>('all');
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [isMobileView, setIsMobileView] = useState<boolean>(false);
  const [sidebarVisible, setSidebarVisible] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [scheduledPosts, setScheduledPosts] = useState<ScheduledPost[]>([]);
  const [scheduledMessages, setScheduledMessages] = useState<
    ScheduledMessage[]
  >([]);
  const [error, setError] = useState<string | null>(null);
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [savingItemId, setSavingItemId] = useState<string | null>(null);
  const [deleteItem, setDeleteItem] = useState<{
    id: string;
    type: string;
  } | null>(null);
  const [isDeletingItem, setIsDeletingItem] = useState(false);
  const [showContentTypeModal, setShowContentTypeModal] = useState(false);

  React.useEffect(() => {
    dayjs.locale('en');

    initializeData();

    const checkMobile = () => {
      setIsMobileView(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setSidebarVisible(false);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (!user?.isPerformer) {
      window.location.href = '/404';
    }
  }, [user]);

  const initializeData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      await Promise.all([fetchScheduledPosts(), fetchScheduledMessages()]);
    } catch (err) {
      setError('Failed to load scheduled content');
      console.error('Error loading scheduled content:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchScheduledPosts = async (): Promise<void> => {
    try {
      const response = await feedService.getScheduledFeeds();
      if (response?.data?.data) {
        setScheduledPosts(response?.data?.data);
      }
    } catch (error) {
      console.error('Error fetching scheduled posts:', error);
      throw error;
    }
  };

  const fetchScheduledMessages = async (): Promise<void> => {
    try {
      const response = await messageService.getAllScheduledMessages();
      if (response?.data) {
        setScheduledMessages(response.data);
      }
    } catch (error) {
      console.error('Error fetching scheduled messages:', error);
      throw error;
    }
  };

  const getScheduledItems = useCallback(() => {
    const normalizedPosts = scheduledPosts.map((post) => ({
      id: post._id,
      scheduledDate: post.scheduledAt,
      date: dayjs(post.scheduledAt).format('YYYY-MM-DD'),
      time: dayjs(post.scheduledAt).format('h:mm A'),
      content: post.text || '',
      type: 'post' as const,
      mediaType: post.type,
      mediaUrls:
        post.files?.map((file) => ({
          url: file.url,
          thumbnail: file.thumbnails?.[0] || '',
          type: file.mediaType
        })) || [],
      author: post.performer?.name || ''
    }));

    const normalizedMessages = scheduledMessages.map((message) => ({
      id: message._id,
      scheduledDate: message.createdAt,
      date: dayjs(message.createdAt).format('YYYY-MM-DD'),
      time: dayjs(message.createdAt).format('h:mm A'),
      content: message.text || '',
      type: 'message' as const,
      mediaType: message.type,
      mediaUrls:
        message.files?.map((file) => ({
          url: file.url,
          thumbnail: file.thumbnails?.[0] || '',
          type: file.mediaType
        })) || [],
      conversationId: message.conversationId,
      recipient: message.recipientName || '',
      price: message.price || 0
    }));

    const allItems = [...normalizedPosts, ...normalizedMessages];

    return allItems.sort(
      (a, b) =>
        new Date(a.scheduledDate).getTime() -
        new Date(b.scheduledDate).getTime()
    );
  }, [scheduledPosts, scheduledMessages]);

  const handleRetry = async () => {
    initializeData();
  };

  const handleDateSelect = (date: string) => {
    setSelectedDate(date);
    if (isMobileView) {
      setSidebarVisible(true);
    }
  };

  const handlePrevMonth = () => {
    setCurrentMonth(currentMonth.subtract(1, 'month'));
  };

  const handleNextMonth = () => {
    setCurrentMonth(currentMonth.add(1, 'month'));
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  const filteredItems = useMemo(
    () =>
      getScheduledItems()
        .filter((item) => item.date === selectedDate)
        .filter((item) => activeTab === 'all' || item.type === activeTab)
        .sort((a, b) => a.time.localeCompare(b.time)),
    [getScheduledItems, selectedDate, activeTab]
  );

  // Get dates with scheduled content
  const getListData = (value: Dayjs) => {
    const dateString = value.format('YYYY-MM-DD');
    return getScheduledItems()
      .filter((item) => item.date === dateString)
      .filter((item) => activeTab === 'all' || item.type === activeTab);
  };

  // Cell renderer for the calendar
  const dateCellRender = (
    value: Dayjs,
    selectedDate: string,
    onSelectDate: (date: string) => void
  ) => {
    const listData = getListData(value);
    const dateString = value.format('YYYY-MM-DD');
    const isSelected = dateString === selectedDate;

    return (
      <div
        className={`${styles.dateCell} ${
          isSelected ? styles.selectedDate : ''
        } ${listData.length > 0 ? styles.hasContent : ''}`}
        onClick={() => onSelectDate(dateString)}
      >
        <div className={styles.dateNumber}>{value.date()}</div>
        {listData.length > 0 && (
          <div className={styles.scheduledIndicator}>
            {listData.some((item) => item.type === 'post') && (
              <div className={styles.postIndicator}>
                <span className={styles.indicatorCount}>
                  {listData.filter((item) => item.type === 'post').length}
                </span>
              </div>
            )}
            {listData.some((item) => item.type === 'message') && (
              <div className={styles.messageIndicator}>
                <span className={styles.indicatorCount}>
                  {listData.filter((item) => item.type === 'message').length}
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  const toggleSidebar = () => {
    setSidebarVisible(!sidebarVisible);
  };

  const handleEditClick = (itemId: string, itemType: string) => {
    if (itemType === 'post') {
      router.push(`/creator/my-feed/edit?id=${itemId}`);
    } else if (itemType === 'message') {
      setEditingItemId(itemId);
    }
  };

  const handleEditSave = async (itemId: string, newContent: string) => {
    setSavingItemId(itemId);
    try {
      await messageService.updateScheduledMessage(itemId, {
        text: newContent
      });

      setScheduledMessages((prev) =>
        prev.map((msg) =>
          msg._id === itemId ? { ...msg, text: newContent } : msg
        )
      );

      setEditingItemId(null);
      message.success('Message updated successfully');
    } catch (error) {
      message.error('Failed to update content');
      console.error('Error updating content:', error);
    } finally {
      setSavingItemId(null);
    }
  };

  const handleEditCancel = () => {
    setEditingItemId(null);
  };

  const handleDeleteClick = (itemId: string, itemType: string) => {
    setDeleteItem({ id: itemId, type: itemType });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteItem) return;

    setIsDeletingItem(true);
    try {
      // API call based on type
      if (deleteItem.type === 'post') {
        await feedService.delete(deleteItem.id);
        setScheduledPosts((prev) =>
          prev.filter((post) => post._id !== deleteItem.id)
        );
      } else {
        await messageService.deleteScheduledMessage(deleteItem.id);
        setScheduledMessages((prev) =>
          prev.filter((msg) => msg._id !== deleteItem.id)
        );
      }

      message.success(
        `${
          deleteItem.type === 'post' ? 'Post' : 'Message'
        } deleted successfully`
      );
    } catch (error) {
      message.error(`Failed to delete ${deleteItem.type}`);
      console.error('Error deleting item:', error);
    } finally {
      setIsDeletingItem(false);
      setDeleteItem(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteItem(null);
  };

  const handleContentTypeSelect = (type: 'post' | 'message') => {
    setShowContentTypeModal(false);

    if (type === 'post') {
      router.push('/creator/my-feed/create?scheduled=true');
      return;
    } else if (type === 'message') {
      setIsModalVisible(true);
    }
  };

  const handleAddContent = () => {
    setShowContentTypeModal(true);
  };

  return (
    <div className={styles.queueContainer}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            className={styles.backButton}
            onClick={() => router.back()}
          />
          <h1 className={styles.title}>QUEUE</h1>
        </div>
        <div className={styles.headerRight}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddContent}
            className={styles.addButton}
          >
            {!isMobileView && 'Add New'}
          </Button>
        </div>
      </div>

      <div className={styles.contentContainer}>
        {isLoading ? (
          <LoadingView />
        ) : error ? (
          <ErrorView message={error} onRetry={handleRetry} />
        ) : (
          <>
            <div className={styles.calendarSection}>
              <div className={styles.calendarHeader}>
                <div className={styles.monthNavigation}>
                  <Button
                    type="text"
                    icon={<LeftOutlined />}
                    onClick={handlePrevMonth}
                    className={styles.navButton}
                  />
                  <span className={styles.currentMonth}>
                    {currentMonth.format('MMMM YYYY')}
                  </span>
                  <Button
                    type="text"
                    icon={<RightOutlined />}
                    onClick={handleNextMonth}
                    className={styles.navButton}
                  />
                </div>
                <div className={styles.statsContainer}>
                  <div className={`${styles.statItem} ${styles.postStat}`}>
                    <PictureOutlined />
                    <span className={styles.statCount}>
                      {
                        getScheduledItems().filter(
                          (item) => item.type === 'post'
                        ).length
                      }
                    </span>
                    <span className={styles.statLabel}>Posts</span>
                  </div>
                  <div className={`${styles.statItem} ${styles.messageStat}`}>
                    <MessageOutlined />
                    <span className={styles.statCount}>
                      {
                        getScheduledItems().filter(
                          (item) => item.type === 'message'
                        ).length
                      }
                    </span>
                    <span className={styles.statLabel}>Messages</span>
                  </div>
                </div>
                <Tabs
                  activeKey={activeTab}
                  onChange={handleTabChange}
                  className={styles.filterTabs}
                >
                  <TabPane tab="All" key="all" />
                  <TabPane tab="Posts" key="post" />
                  <TabPane tab="Messages" key="message" />
                </Tabs>
              </div>

              <div className={styles.calendar}>
                <Calendar
                  value={currentMonth as any}
                  fullscreen={false}
                  headerRender={() => null}
                  dateFullCellRender={(value) =>
                    dateCellRender(value as any, selectedDate, handleDateSelect)
                  }
                  onChange={(date) => setCurrentMonth(date as any)}
                />
              </div>
            </div>

            {(sidebarVisible || !isMobileView) && (
              <div
                className={`${styles.sidebarSection} ${
                  isMobileView && styles.mobileSidebar
                }`}
              >
                <div className={styles.sidebarHeader}>
                  {isMobileView && (
                    <Button
                      type="text"
                      icon={<ArrowLeftOutlined />}
                      onClick={toggleSidebar}
                      className={styles.closeSidebarButton}
                    />
                  )}
                  <h2 className={styles.sidebarDate}>
                    {dayjs(selectedDate).format('MMMM D, YYYY')}
                  </h2>
                  <div className={styles.selectedDateStats}>
                    <div className={styles.selectedStatItem}>
                      <span className={styles.selectedStatCount}>
                        {
                          filteredItems.filter((item) => item.type === 'post')
                            .length
                        }
                      </span>
                      <span className={styles.selectedStatLabel}>Posts</span>
                    </div>
                    <div className={styles.selectedStatItem}>
                      <span className={styles.selectedStatCount}>
                        {
                          filteredItems.filter(
                            (item) => item.type === 'message'
                          ).length
                        }
                      </span>
                      <span className={styles.selectedStatLabel}>Messages</span>
                    </div>
                  </div>
                  <Dropdown
                    overlay={
                      <Menu>
                        <Menu.Item
                          key="1"
                          onClick={() =>
                            router.push(
                              '/creator/my-feed/create?scheduled=true'
                            )
                          }
                        >
                          Add Post
                        </Menu.Item>
                        <Menu.Item
                          key="2"
                          onClick={() => setIsModalVisible(true)}
                        >
                          Add Message
                        </Menu.Item>
                      </Menu>
                    }
                    trigger={['click']}
                  >
                    <Button
                      type="text"
                      icon={<MoreOutlined />}
                      className={styles.sidebarMenuButton}
                    />
                  </Dropdown>
                </div>

                <div className={styles.scheduledItems}>
                  {filteredItems.length === 0 ? (
                    <div className={styles.emptyState}>
                      <p>No scheduled content for this day</p>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddContent}
                      >
                        Schedule Content
                      </Button>
                    </div>
                  ) : (
                    filteredItems.map((item) => (
                      <div
                        key={item.id}
                        className={`${styles.scheduledItem} ${
                          styles[item.type]
                        }`}
                      >
                        <div className={styles.itemHeader}>
                          <div className={styles.itemTime}>
                            <ClockCircleOutlined /> {item.time}
                          </div>
                          <div className={styles.itemType}>
                            {item.type === 'message' ? (
                              <>
                                <MessageOutlined /> Message
                              </>
                            ) : (
                              <>
                                <PictureOutlined /> Post
                              </>
                            )}
                          </div>
                          <Dropdown
                            overlay={
                              <Menu>
                                <Menu.Item
                                  key="1"
                                  onClick={() =>
                                    handleEditClick(item.id, item.type)
                                  }
                                >
                                  Edit
                                </Menu.Item>
                                <Menu.Item
                                  key="2"
                                  onClick={() =>
                                    handleDeleteClick(item.id, item.type)
                                  }
                                  className={styles.deleteMenuItem}
                                >
                                  Delete
                                </Menu.Item>
                              </Menu>
                            }
                            trigger={['click']}
                          >
                            <Button
                              type="text"
                              icon={<MoreOutlined />}
                              className={styles.itemMenuButton}
                            />
                          </Dropdown>
                        </div>

                        {item.type === 'message' && (
                          <div className={styles.messageDetails}>
                            {item.recipient && (
                              <div className={styles.itemRecipient}>
                                to {item.recipient}
                              </div>
                            )}
                            {item.price > 0 && (
                              <div className={styles.itemPrice}>
                                <span className={styles.priceLabel}>
                                  Price:
                                </span>
                                <span className={styles.priceValue}>
                                  ${item.price.toFixed(2)}
                                </span>
                              </div>
                            )}
                          </div>
                        )}

                        {item.content && (
                          <div className={styles.itemContent}>
                            {editingItemId === item.id ? (
                              <div className={styles.editContentWrapper}>
                                <Input.TextArea
                                  defaultValue={item.content}
                                  autoSize={{ minRows: 2, maxRows: 6 }}
                                  className={styles.editInput}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Escape') {
                                      handleEditCancel();
                                    }
                                  }}
                                />
                                <div className={styles.editActions}>
                                  <Button
                                    size="small"
                                    type="text"
                                    onClick={handleEditCancel}
                                    className={styles.editButton}
                                    disabled={savingItemId === item.id}
                                  >
                                    Cancel
                                  </Button>
                                  <Button
                                    size="small"
                                    type="primary"
                                    onClick={(e) => {
                                      const textarea = e.currentTarget
                                        .parentElement
                                        ?.previousElementSibling as HTMLTextAreaElement;
                                      if (textarea) {
                                        handleEditSave(item.id, textarea.value);
                                      }
                                    }}
                                    className={styles.editButton}
                                    loading={savingItemId === item.id}
                                  >
                                    Save
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className={styles.contentText}>
                                {item.content}
                              </div>
                            )}
                          </div>
                        )}

                        {item.mediaUrls?.length > 0 && (
                          <div className={styles.itemMedia}>
                            {item.mediaUrls.slice(0, 4).map((media, index) => (
                              <div
                                key={index}
                                className={`${styles.mediaWrapper} ${
                                  index === 3 && item.mediaUrls.length > 4
                                    ? styles.hasMore
                                    : ''
                                }`}
                                data-remaining={
                                  index === 3 && item.mediaUrls.length > 4
                                    ? `+${item.mediaUrls.length - 4}`
                                    : ''
                                }
                              >
                                {media.type === 'video' ? (
                                  <img
                                    src={media.thumbnail}
                                    alt={`Media content ${index + 1}`}
                                    loading="lazy"
                                  />
                                ) : (
                                  <img
                                    src={media.url}
                                    alt={`Media content ${index + 1}`}
                                    loading="lazy"
                                  />
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}

            {isMobileView && !sidebarVisible && (
              <Button
                type="primary"
                className={styles.viewSelectedButton}
                onClick={toggleSidebar}
              >
                View Selected Day
              </Button>
            )}
          </>
        )}
      </div>

      <Modal
        title={
          <div className={styles.deleteModalTitle}>
            <ExclamationCircleOutlined className={styles.deleteIcon} />
            Delete{' '}
            {deleteItem?.type === 'post'
              ? 'Scheduled Post'
              : 'Scheduled Message'}
          </div>
        }
        visible={deleteItem !== null}
        onCancel={handleDeleteCancel}
        footer={null}
        closable={!isDeletingItem}
        maskClosable={!isDeletingItem}
        className={styles.deleteModal}
      >
        <div className={styles.deleteModalContent}>
          <p>
            Are you sure you want to delete this scheduled {deleteItem?.type}?
          </p>
          <p className={styles.deleteWarning}>
            This action cannot be undone and will remove the {deleteItem?.type}{' '}
            from your queue.
          </p>

          <div className={styles.deleteModalActions}>
            <Button
              onClick={handleDeleteCancel}
              disabled={isDeletingItem}
              className={styles.cancelButton}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              danger
              onClick={handleDeleteConfirm}
              loading={isDeletingItem}
              className={styles.confirmButton}
            >
              Delete {deleteItem?.type}
            </Button>
          </div>
        </div>
      </Modal>

      <ScheduleMessageModal
        isModalVisible={isModalVisible}
        setIsModalVisible={setIsModalVisible}
        isMobileView={isMobileView}
        dayjs={dayjs}
        selectedDate={selectedDate}
        fetchScheduledMessages={fetchScheduledMessages}
      />

      <ContentTypeModal
        visible={showContentTypeModal}
        onClose={() => setShowContentTypeModal(false)}
        onSelect={handleContentTypeSelect}
      />
    </div>
  );
};

export default QueuePage;
