import {
  CloseCircleOutlined,
  FileImageOutlined,
  VideoCameraOutlined
} from '@ant-design/icons';
import { Image, Progress, Tooltip } from 'antd';
import { useEffect, useMemo, useState } from 'react';

import style from './teaser-upload-preview.module.less';

interface IProps {
  file: any;
  onRemoveFile: Function;
  disabled?: boolean;
}

function TeaserPreview({ file }: { file: any }) {
  const [isImage, setIsImage] = useState(false);
  const [isVideo, setIsVideo] = useState(false);

  useEffect(() => {
    if (!file) return;

    const isImageType =
      file.type?.includes('image') || file.mimeType?.includes('image');
    const isVideoType =
      file.type?.includes('video') || file.mimeType?.includes('video');

    setIsImage(isImageType);
    setIsVideo(isVideoType);
  }, [file]);

  const src = useMemo(() => {
    if (!file) return null;

    if (isImage && file.originFileObj) {
      if (file.originFileObj?.file?.url) {
        return file.originFileObj.file.url;
      }
      if (
        file.originFileObj instanceof Blob ||
        file.originFileObj instanceof File ||
        file.originFileObj instanceof MediaSource
      ) {
        try {
          return URL.createObjectURL(file.originFileObj);
        } catch (error) {
          console.error('Error creating object URL:', error);
          return null;
        }
      }
      return null;
    }

    if (isVideo && file.originFileObj?.file?.thumbnails?.[0]) {
      return file.originFileObj.file.thumbnails[0];
    }

    return null;
  }, [file, isImage, isVideo]);

  return (
    <div className={style.teaserPreviewWrapper}>
      {src ? (
        <div className={style.previewContainer}>
          <Image
            src={src}
            preview={isImage}
            alt={file.name || 'Teaser Preview'}
            style={{ objectFit: 'cover' }}
          />
        </div>
      ) : (
        <div className={style.placeholderIcon}>
          {isVideo ? (
            <VideoCameraOutlined style={{ fontSize: '100px', color: '#666' }} />
          ) : (
            <FileImageOutlined style={{ fontSize: '100px', color: '#666' }} />
          )}
        </div>
      )}
      {isVideo && <div className={style['play-icon']} />}
      {(file.percentage || file.pendingUpload) && (
        <Progress
          percent={file.percentage || 0}
          className={style.uploadProgress}
        />
      )}
    </div>
  );
}

export function TeaserUploadPreview({
  file,
  onRemoveFile,
  disabled = false
}: IProps) {
  if (!file) return null;

  return (
    <div className={style.teaserUploadContainer}>
      <div className={style.teaserHeader}>
        <span className={style.teaserLabel}>Teaser Content</span>
        <Tooltip title="Preview content that will be visible to users before purchase">
          <div className={style.teaserBadge}>Teaser</div>
        </Tooltip>
      </div>
      <ul className={style.teaserGrid}>
        <li key={file.uid} className={style.teaserItem}>
          <TeaserPreview file={file} />
          {!disabled && (
            <button
              type="button"
              onClick={() => onRemoveFile(file)}
              className={style.removeButton}
            >
              <CloseCircleOutlined />
            </button>
          )}
        </li>
      </ul>
    </div>
  );
}

export default TeaserUploadPreview;
