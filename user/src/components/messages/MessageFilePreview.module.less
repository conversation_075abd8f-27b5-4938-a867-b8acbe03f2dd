.filePreviewContainer {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 8px;
  position: relative;

  &.small {
    .previewItem {
      width: 60px;
      height: 60px;
    }
  }

  &.medium {
    .previewItem {
      width: 80px;
      height: 80px;
    }
  }

  &.large {
    .previewItem {
      width: 100px;
      height: 100px;
    }
  }
}

.previewItem {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f0f2f5;

  &:hover {
    .playIcon {
      transform: scale(1.1) translate(-50%, -50%);
    }
  }
}

.thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.videoPreview {
  position: relative;
  width: 100%;
  height: 100%;

  .playIcon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    color: #fff;
    transition: transform 0.2s;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
}

.fileIcon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 8px;
  text-align: center;

  :global(.anticon) {
    font-size: 24px;
    color: #8c8c8c;
    margin-bottom: 4px;
  }
}

.fileName {
  font-size: 12px;
  color: #595959;
  word-break: break-word;
  line-height: 1.2;
}

.remainingBadge {
  position: absolute;
  bottom: 0px;
  right: 0px;

  :global(.ant-badge-count) {
    background: rgba(0, 0, 0, 0.65);
    box-shadow: none;
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    padding: 0 6px;
  }
}

@media (max-width: 576px) {
  .filePreviewContainer {
    gap: 4px;

    &.small,
    &.medium {
      .previewItem {
        width: 50px;
        height: 50px;
      }
    }

    &.large {
      .previewItem {
        width: 70px;
        height: 70px;
      }
    }
  }

  .videoPreview {
    .playIcon {
      font-size: 20px;
    }
  }
}
