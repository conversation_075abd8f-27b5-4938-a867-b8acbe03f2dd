/* eslint-disable react/no-unused-state */
/* eslint-disable react/require-default-props */
/* eslint-disable react/no-unused-prop-types */
import {
  ArrowLeftOutlined,
  ClockCircleOutlined,
  PaperClipOutlined,
  PlayCircleOutlined,
  SendOutlined,
  SmileOutlined
} from '@ant-design/icons';
import VaultSelectButton from '@components/vault/vault-select-button';
import { messageService } from '@services/index';
import {
  Button,
  DatePicker,
  Input,
  InputNumber,
  List,
  message,
  Modal,
  Popover,
  TimePicker,
  Tooltip
} from 'antd';
import { useCallback, useRef, useState } from 'react';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { isMobile } from 'react-device-detect';
import { useDispatch, useSelector } from 'react-redux';
import { useAtomValue, useSetAtom } from 'jotai';

import style from './MassMessageComposer.module.less';
import { Emotions } from './emotions';
import FilesUploadPreview from './file-upload-preview';
import MessageFilePreview from './MessageFilePreview';
import {
  isShowMassMessageInputAtom,
  isShowMassMessagePanelAtom,
  keywordAtom,
  recipientIdsAtom
} from 'src/states/chat';
import { getConversations } from '@redux/message/actions';
import TeaserUploadPreview from './TeaserUploadPreview';

const MassMessageStatus = ({
  recipientCount,
  onBack
}: {
  recipientCount: number;
  onBack?: () => void;
}) => {
  return (
    <div className={style.messageStatus}>
      {isMobile && (
        <button onClick={onBack} className={style.backButton}>
          <ArrowLeftOutlined />
        </button>
      )}
      <div className={style.statusContent}>
        <span className={style.statusIcon}>
          {!recipientCount ? '⚠️' : '✉️'}
        </span>
        <span className={style.statusText}>
          {!recipientCount
            ? 'Select recipients'
            : `Sending to ${recipientCount} recipient${
                recipientCount > 1 ? 's' : ''
              }`}
        </span>
      </div>
    </div>
  );
};

const MassMessageComposer = () => {
  const recipientIds = useAtomValue(recipientIdsAtom);
  const currentUser = useSelector((state: any) => state.user.current);
  const setIsShowMassMessagePanel = useSetAtom(isShowMassMessagePanelAtom);
  const dispatch = useDispatch();
  const keyword = useAtomValue(keywordAtom);
  const setIsShowMassMessageInput = useSetAtom(isShowMassMessageInputAtom);

  const [text, setText] = useState('');
  const [vaultOpen, setVaultOpen] = useState(false);
  const [defaultMediaId, setDefaultMediaId] = useState<string | null>(null);
  const [price, setPrice] = useState(0);
  const [previewFiles, setPreviewFiles] = useState<
    Array<{
      uid: string;
      name: string;
      type: string;
      preview: string;
      originFileObj: any;
    }>
  >([]);
  const [scheduleModalVisible, setScheduleModalVisible] = useState(false);
  const [scheduledDate, setScheduledDate] = useState<Dayjs | null>(null);
  const [scheduledTime, setScheduledTime] = useState<Dayjs | null>(null);
  const [showScheduledList, setShowScheduledList] = useState(false);
  const [scheduledMessages, setScheduledMessages] = useState([]);
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editingText, setEditingText] = useState('');
  const [schedulingMessage, setSchedulingMessage] = useState(false);
  const [sending, setSending] = useState(false);
  const [teaserVaultOpen, setTeaserVaultOpen] = useState(false);
  const [teaserFile, setTeaserFile] = useState<{
    uid: string;
    name: string;
    type: string;
    preview: string;
    originFileObj: any;
  } | null>(null);

  const inputRef = useRef<any>(null);

  const disabled = !recipientIds.length;

  const fetchConversations = useCallback(
    (params: any) => {
      dispatch(getConversations(params));
    },
    [dispatch]
  );

  const onKeyDown = (evt: React.KeyboardEvent) => {
    if (evt.keyCode === 13) {
      handleSendMassMessage();
    }
  };

  const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    setText(evt.target.value);
  };

  const onEmojiClick = (emoji: string) => {
    if (disabled) return;
    setText(`${text} ${emoji} `);
  };

  const handleRenderPreview = () => (
    <div
      aria-hidden
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        fontSize: '20px'
      }}
      onClick={() => setVaultOpen(true)}
    >
      <PaperClipOutlined />
    </div>
  );

  const handleSelectVault = async (media: any) => {
    const mediaItems = Array.isArray(media) ? media : [media];

    const previewFiles = mediaItems.map((item) => ({
      uid: item._id,
      name: item.name || 'Media',
      type:
        item.file?.mimeType ||
        (item.file?.type?.includes('video') ? 'video/mp4' : 'image/jpeg'),
      preview: item.file?.url || item.file?.thumbnailUrl || '',
      originFileObj: item
    }));

    setVaultOpen(false);
    setDefaultMediaId(null);
    setPreviewFiles(previewFiles);
  };

  const handleRemovePreview = (fileToRemove: any) => {
    setPreviewFiles((prev) =>
      prev.filter((file) => file.uid !== fileToRemove.uid)
    );
  };

  const handleCloseVault = () => {
    setVaultOpen(false);
  };

  const handleSendMassMessage = async () => {
    setSending(true);

    try {
      const fileIds =
        previewFiles?.map((item) => item.originFileObj?.fileId) || [];
      const teaserId =
        price && previewFiles.length > 0
          ? teaserFile?.originFileObj?.fileId
          : null;

      await messageService.sendMassMessage({
        message: text,
        recipientIds,
        fileIds,
        price,
        teaserId
      });

      setIsShowMassMessagePanel(false);
      setIsShowMassMessageInput(false);
      fetchConversations({
        limit: 25,
        offset: 0,
        type: 'private',
        keyword
      });
    } catch (error) {
      // Handle error
      console.error('Failed to send message:', error);
    } finally {
      setSending(false);
    }
  };

  const handleScheduleMessage = async () => {
    if (!scheduledDate || !scheduledTime) return;

    const scheduledDateTime = scheduledDate
      .hour(scheduledTime.hour())
      .minute(scheduledTime.minute())
      .second(0);
    setSchedulingMessage(true);

    try {
      const fileIds =
        previewFiles?.map((item) => item.originFileObj?.fileId) || [];

      await messageService.sendMassMessage({
        message: text,
        recipientIds,
        fileIds,
        price,
        scheduledFor: scheduledDateTime.toISOString()
      });

      setScheduleModalVisible(false);
      setIsShowMassMessagePanel(false);
      setIsShowMassMessageInput(false);
      fetchConversations({
        limit: 25,
        offset: 0,
        type: 'private',
        keyword
      });
    } catch (error) {
      // Handle error
      console.error('Failed to send message:', error);
    } finally {
      setSchedulingMessage(false);
    }
  };

  const handleEditClick = (msg: any) => {
    setEditingMessageId(msg._id);
    setEditingText(msg.text);
  };

  const handleCancelEdit = () => {
    setEditingMessageId(null);
    setEditingText('');
  };

  const handleEditChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditingText(e.target.value);
  };

  const handleSaveEdit = async (msg: any) => {
    if (!editingText.trim()) return;

    try {
      await messageService.updateScheduledMessage(msg._id, {
        text: editingText
      });

      setScheduledMessages((prev) =>
        prev.map((message) =>
          message._id === msg._id
            ? {
                ...message,
                text: editingText,
                updatedAt: new Date().toISOString()
              }
            : message
        )
      );
      setEditingMessageId(null);
      setEditingText('');
      message.success('Message updated successfully');
    } catch (error) {
      message.error('Failed to update message');
      console.error('Error updating scheduled message:', error);
    }
  };

  const handleDeleteMessage = async (msg: any) => {
    try {
      Modal.confirm({
        title: 'Delete scheduled message',
        content: 'Are you sure you want to delete this scheduled message?',
        okText: 'Delete',
        okButtonProps: {
          danger: true
        },
        onOk: async () => {
          try {
            await messageService.deleteScheduledMessage(msg._id);
            setScheduledMessages((prev) =>
              prev.filter((message) => message._id !== msg._id)
            );
            message.success('Message deleted successfully');
          } catch (error) {
            message.error('Failed to delete message');
            console.error('Error deleting scheduled message:', error);
          }
        }
      });
    } catch (error) {
      message.error('Failed to delete message');
      console.error('Error in delete confirmation:', error);
    }
  };

  const handleSelectTeaser = async (media) => {
    if (!media || (Array.isArray(media) && !media.length)) return;

    const item = Array.isArray(media) ? media[0] : media;
    const teaserFile = {
      uid: item._id,
      name: item.name || 'Teaser',
      type: item.file?.mimeType || 'image/jpeg',
      preview: item.file?.url || item.file?.thumbnailUrl || '',
      originFileObj: item
    };

    setTeaserVaultOpen(false);
    setTeaserFile(teaserFile);
  };

  const handleRenderTeaser = () => {
    const isDisabled = previewFiles.length === 0 || !price;

    return (
      <Tooltip
        title={
          isDisabled
            ? 'Add files and set price to enable teaser content'
            : 'Add teaser content'
        }
      >
        <div
          aria-hidden
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            fontSize: '20px',
            cursor: isDisabled ? 'not-allowed' : 'pointer',
            opacity: isDisabled ? 0.5 : 1
          }}
          onClick={() => !isDisabled && setTeaserVaultOpen(true)}
        >
          <PlayCircleOutlined />
        </div>
      </Tooltip>
    );
  };

  const handleCloseTeaserVault = () => {
    setTeaserVaultOpen(false);
  };

  const handleRemoveTeaser = () => {
    setTeaserFile(null);
  };

  return (
    <div className={style.compose}>
      <MassMessageStatus
        recipientCount={recipientIds.length}
        onBack={() => setIsShowMassMessageInput(false)}
      />
      <div className={style.filePreview}>
        <FilesUploadPreview
          files={previewFiles}
          onRemoveFile={handleRemovePreview}
          disabled={sending}
        />
      </div>
      <TeaserUploadPreview
        file={teaserFile}
        onRemoveFile={handleRemoveTeaser}
        disabled={sending}
      />
      {currentUser.isPerformer && (
        <Tooltip title="Price per message">
          <InputNumber
            prefix="$"
            className={style['inp-price']}
            onChange={(val) => setPrice(val)}
            disabled={disabled || sending}
            placeholder="0"
            min={0}
            controls={false}
          />
        </Tooltip>
      )}
      <Input
        value={text}
        className={style['compose-input']}
        allowClear
        placeholder="Write your message..."
        onKeyDown={onKeyDown}
        onChange={onChange}
        disabled={disabled || sending}
        ref={inputRef}
      />
      <div className={style['grp-icons']}>
        <Popover
          content={<Emotions onEmojiClick={onEmojiClick} />}
          trigger="click"
        >
          <div className="grp-emotions">
            <SmileOutlined />
          </div>
        </Popover>
      </div>
      <div className={style['grp-icons']}>
        <div className={style['grp-file-icon']}>
          <VaultSelectButton
            mediaType=""
            onSelect={handleSelectVault}
            buttonText="Add"
            renderPreviewItem={handleRenderPreview}
            externalOpenVault={vaultOpen}
            onCloseVault={handleCloseVault}
            defaultSelectedId={defaultMediaId}
            openModal={vaultOpen || !!defaultMediaId}
            multiple
          />
        </div>
      </div>
      <div className={style['grp-icons']}>
        <div className={style['grp-file-icon']}>
          <VaultSelectButton
            mediaType="image"
            onSelect={handleSelectTeaser}
            buttonText="Add"
            renderPreviewItem={handleRenderTeaser}
            externalOpenVault={teaserVaultOpen}
            onCloseVault={handleCloseTeaserVault}
            openModal={teaserVaultOpen}
            multiple={false}
          />
        </div>
      </div>
      <div className={style['grp-icons']}>
        <Button
          className={style['schedule-btn']}
          onClick={() => setScheduleModalVisible(true)}
          disabled={disabled || sending || (!text && previewFiles.length === 0)}
        >
          <ClockCircleOutlined />
        </Button>
      </div>
      <div className={style['send-button']} style={{ paddingRight: 0 }}>
        <div
          aria-hidden
          className={style['grp-send']}
          onClick={handleSendMassMessage}
        >
          <SendOutlined />
        </div>
      </div>
      <Modal
        title="Schedule Message"
        visible={scheduleModalVisible}
        onCancel={() => setScheduleModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setScheduleModalVisible(false)}>
            Cancel
          </Button>,
          <Button
            key="schedule"
            type="primary"
            onClick={handleScheduleMessage}
            loading={schedulingMessage}
            disabled={!scheduledDate || !scheduledTime}
          >
            Schedule Message
          </Button>
        ]}
      >
        <div className={style.scheduleForm}>
          <DatePicker
            className={style.datePicker}
            value={scheduledDate as any}
            onChange={(date) => setScheduledDate(date as any)}
            disabledDate={(current) =>
              current &&
              current.toISOString() < dayjs().startOf('day').toISOString()
            }
            placeholder="Select date"
            disabled={schedulingMessage}
          />
          <TimePicker
            className={style.timePicker}
            value={scheduledTime as any}
            onChange={(time) => setScheduledTime(time as any)}
            format="hh:mm A"
            use12Hours
            placeholder="Select time"
            minuteStep={10}
            disabled={schedulingMessage}
          />
        </div>
      </Modal>
      <Modal
        title="Scheduled Messages"
        visible={showScheduledList}
        onCancel={() => setShowScheduledList(false)}
        footer={null}
        className={style.scheduledListModal}
      >
        <List
          className={style.scheduledList}
          dataSource={scheduledMessages}
          locale={{ emptyText: 'No scheduled messages' }}
          renderItem={(msg) => (
            <List.Item
              actions={
                editingMessageId === msg._id
                  ? []
                  : [
                      <Button
                        key="edit"
                        type="link"
                        className={style.actionButton}
                        onClick={() => handleEditClick(msg)}
                      >
                        Edit
                      </Button>,
                      <Button
                        key="delete"
                        type="link"
                        className={style.actionButton}
                        danger
                        onClick={() => handleDeleteMessage(msg)}
                      >
                        Delete
                      </Button>
                    ]
              }
            >
              <List.Item.Meta
                title={
                  editingMessageId === msg._id ? (
                    <div className={style.editMode}>
                      <Input.TextArea
                        className={style.editInput}
                        value={editingText}
                        onChange={handleEditChange}
                        autoSize={{ minRows: 1, maxRows: 4 }}
                        placeholder="Enter your message"
                      />
                      <div className={style.editActions}>
                        <Button
                          type="primary"
                          size="small"
                          onClick={() => handleSaveEdit(msg)}
                        >
                          Save
                        </Button>
                        <Button size="small" onClick={handleCancelEdit}>
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className={style.messageText}>{msg.text}</div>
                      {msg.files && msg.files.length > 0 && (
                        <MessageFilePreview
                          files={msg.files}
                          size={isMobile ? 'small' : 'medium'}
                        />
                      )}
                    </div>
                  )
                }
                description={`Scheduled for ${dayjs(msg.createdAt).format(
                  'MMM D [at] h:mm A'
                )}`}
              />
            </List.Item>
          )}
        />
      </Modal>
    </div>
  );
};

export default MassMessageComposer;
