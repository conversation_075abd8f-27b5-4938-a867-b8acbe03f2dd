import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import { Modal, List, Input, Button, Space, Tooltip, message } from 'antd';
import { useEffect, useState } from 'react';

import { subscriberCategoryService } from '@services/subscriber-category.service';
import { subscriptionService } from '@services/subscription.service';
import SubscriberView from './SubscriberView';
import style from './CategoryModal.module.less';

interface CategoryModalProps {
  visible: boolean;
  onClose: () => void;
  performerId: string;
}

export interface Category {
  id: string;
  name: string;
  subscriberCount?: number;
}

export default function CategoryModal({
  visible,
  onClose,
  performerId
}: CategoryModalProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [newCategory, setNewCategory] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [addLoading, setAddLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(
    null
  );

  useEffect(() => {
    if (visible && performerId) {
      getCategories();
    }
  }, [visible, performerId]);

  const getCategories = async () => {
    setLoading(true);
    try {
      const [categoriesResponse, subscribersResponse] = await Promise.all([
        subscriberCategoryService.findMyCategories(),
        subscriptionService.getSubscribersCount(performerId)
      ]);

      const fetchedCategories =
        categoriesResponse.data?.map(({ category, subscriberCount }) => ({
          ...category,
          id: category._id,
          subscriberCount
        })) || [];

      const defaultCategory = {
        id: 'all',
        name: 'All Subscribers',
        subscriberCount: subscribersResponse.data,
        isDefault: true
      };

      setCategories([defaultCategory, ...fetchedCategories]);
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCategory = async () => {
    if (!newCategory.trim()) return;

    const newItem = {
      id: Date.now().toString(),
      name: newCategory.trim(),
      subscriberCount: 0
    };
    setAddLoading(true);

    try {
      const response = await subscriberCategoryService.create({
        name: newCategory.trim()
      });
      message.success('Category created successfully');

      // Insert newItem as second item after the default category
      const defaultCategory = categories[0];
      const otherCategories = categories.slice(1);
      setCategories([
        defaultCategory,
        { ...newItem, id: response?.data?._id },
        ...otherCategories
      ]);

      setNewCategory('');
    } catch (error) {
      console.error({ error });
      message.error('Failed to create category');
    } finally {
      setAddLoading(false);
    }
  };

  const handleEditCategory = (id: string) => {
    const category = categories.find((c) => c.id === id);
    if (category) {
      setEditingId(id);
      setEditValue(category.name);
    }
  };

  const saveEdit = async (id: string) => {
    setEditLoading(true);
    try {
      await subscriberCategoryService.update(id, { name: editValue });
      setCategories(
        categories.map((cat) =>
          cat.id === id ? { ...cat, name: editValue } : cat
        )
      );
      setEditingId(null);
    } catch (error) {
      console.error({ error });
    } finally {
      setEditLoading(false);
    }
  };

  const handleDeleteCategory = async (id: string) => {
    setDeleteLoading(true);
    try {
      await subscriberCategoryService.remove(id);
      setCategories(categories.filter((c) => c.id !== id));
    } catch (error) {
      console.error({ error });
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleCategoryClick = (category: Category) => {
    setSelectedCategory(category);
  };

  const CategoryHeader = () => (
    <div className={style['modal-header']}>
      {selectedCategory && (
        <Button
          type="text"
          icon={<ArrowLeftOutlined />}
          onClick={() => {
            setSelectedCategory(null);
            getCategories();
          }}
          className={style['back-button']}
        />
      )}
      <h2>{selectedCategory ? selectedCategory.name : 'Manage Categories'}</h2>
    </div>
  );

  const addCategoryForm = (
    <div className={style['add-category']}>
      <Input
        placeholder="Enter category name"
        value={newCategory}
        onChange={(e) => setNewCategory(e.target.value)}
        onPressEnter={handleAddCategory}
        maxLength={50}
        prefix={<PlusOutlined className={style['input-icon']} />}
      />
      <Button
        type="primary"
        onClick={handleAddCategory}
        loading={addLoading}
        disabled={!newCategory.trim()}
      >
        Add Category
      </Button>
    </div>
  );

  return (
    <div className={style.modalWrapper}>
      <Modal
        title={<CategoryHeader />}
        visible={visible}
        onCancel={onClose}
        footer={null}
        width={520}
        className={style['category-modal']}
      >
        {selectedCategory ? (
          <SubscriberView
            categoryId={selectedCategory?.id}
            type={selectedCategory?.id === 'all' ? 'all' : 'category'}
            categories={categories.filter((category) => category.id !== 'all')}
          />
        ) : (
          <div className={style['category-container']}>
            {addCategoryForm}

            <List
              className={style['category-list']}
              itemLayout="horizontal"
              dataSource={loading ? Array(3).fill({}) : categories}
              renderItem={(category) => (
                <List.Item>
                  <div className={style['category-item']}>
                    {loading ? (
                      <div className={style['skeleton-content']}>
                        <div className={style['skeleton-tag']}>
                          <div className={style['skeleton-name']}></div>
                          <div className={style['skeleton-count']}></div>
                        </div>
                      </div>
                    ) : (
                      <div
                        className={style['category-content']}
                        onClick={() => handleCategoryClick(category)}
                      >
                        <div
                          className={`${style['category-tag']} ${
                            category.isDefault
                              ? style['default-category-tag']
                              : ''
                          }`}
                        >
                          <div className={style['category-info']}>
                            {editingId === category.id ? (
                              <Input
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                onPressEnter={() => saveEdit(category.id)}
                                onClick={(e) => e.stopPropagation()}
                                autoFocus
                                maxLength={50}
                              />
                            ) : (
                              <span className={style['category-name']}>
                                {category.name}
                              </span>
                            )}
                            <span className={style['subscriber-count']}>
                              {`${category.subscriberCount} subscribers`}
                            </span>
                          </div>

                          {!category.isDefault && (
                            <Space className={style['action-buttons']}>
                              {editingId === category.id ? (
                                <Tooltip title="Save">
                                  <Button
                                    type="text"
                                    icon={<CheckOutlined />}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      saveEdit(category.id);
                                    }}
                                    loading={editLoading}
                                  />
                                </Tooltip>
                              ) : (
                                <Tooltip title="Edit">
                                  <Button
                                    type="text"
                                    icon={<EditOutlined />}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleEditCategory(category.id);
                                    }}
                                  />
                                </Tooltip>
                              )}
                              <Tooltip title="Delete">
                                <Button
                                  type="text"
                                  danger
                                  icon={<DeleteOutlined />}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteCategory(category.id);
                                  }}
                                  disabled={editingId === category.id}
                                  loading={deleteLoading}
                                />
                              </Tooltip>
                            </Space>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </List.Item>
              )}
            />
          </div>
        )}
      </Modal>
    </div>
  );
}
