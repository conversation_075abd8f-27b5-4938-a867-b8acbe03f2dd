import { useEffect, useMemo, useState } from 'react';
import { Badge, Button, Input, Spin } from 'antd';
import { ArrowRightOutlined, CloseOutlined } from '@ant-design/icons';
import { useSetAtom } from 'jotai';

import style from './SubscriberList.module.less';
import SubscriberListItem from './SubscriberListItem';
import { subscriptionService } from '@services/subscription.service';
import { subscriberCategoryService } from '@services/subscriber-category.service';
import { isShowMassMessageInputAtom, recipientIdsAtom } from 'src/states/chat';

interface SubscriberListProps {
  isSelectionMode: boolean;
  closeSelectionMode: () => void;
}

const SubscriberList = ({
  isSelectionMode,
  closeSelectionMode
}: SubscriberListProps) => {
  const setRecipientIds = useSetAtom(recipientIdsAtom);
  const setIsShowMassMessageInput = useSetAtom(isShowMassMessageInputAtom);

  const [subscriberData, setSubscriberData] = useState<any[]>([]);
  const [selectedSubscribers, setSelectedSubscribers] = useState<Set<string>>(
    new Set<string>()
  );
  const [filteredSubscriberData, setFilteredSubscriberData] = useState<any[]>(
    []
  );
  const [isSubsriberLoading, setIsSubscriberLoading] = useState(false);

  const recipientIds = useMemo(() => {
    const recipientIds = new Set<string>();

    selectedSubscribers.forEach((id) => {
      const category = filteredSubscriberData.find(
        (item) => item.isCategory && item.category?._id === id
      );

      if (category) {
        // If it's a category, add all subscriber IDs from the category
        category?.subscribers.forEach((sub) => recipientIds.add(sub.id));
      } else {
        // If it's a subscriber ID, add it directly
        recipientIds.add(id);
      }
    });

    return Array.from(recipientIds);
  }, [selectedSubscribers, filteredSubscriberData]);

  useEffect(() => {
    if (isSelectionMode) {
      fetchSubscriberData();
    }
  }, [isSelectionMode]);

  useEffect(() => {
    setFilteredSubscriberData(subscriberData);
  }, [subscriberData]);

  useEffect(() => {
    setRecipientIds(recipientIds);
  }, [recipientIds]);

  const isSelectedAll = useMemo(() => {
    const nonCategorySubscribers = subscriberData.filter(
      (subscriber) => !subscriber?.isCategory
    );

    // Return false if there are no non-category subscribers
    if (nonCategorySubscribers.length === 0) return false;

    return nonCategorySubscribers.every((subscriber) =>
      selectedSubscribers.has(subscriber._id)
    );
  }, [subscriberData, selectedSubscribers]);

  const handleSubscriberSearch = (searchValue: string): void => {
    if (!subscriberData?.length) return;

    const normalizedSearch = searchValue.toLowerCase().trim();

    const filteredData = subscriberData.filter((subscriber) => {
      // Filter by selection mode
      if (isSelectedAll && subscriber?.isCategory) return false;

      // Search logic
      const username = subscriber?.username?.toLowerCase() || '';
      const categoryName = subscriber?.category?.name?.toLowerCase() || '';

      return (
        username.includes(normalizedSearch) ||
        categoryName.includes(normalizedSearch)
      );
    });

    setFilteredSubscriberData(filteredData);
  };

  const toggleSelectionMode = () => {
    closeSelectionMode();
    setSelectedSubscribers(new Set());
    setFilteredSubscriberData(subscriberData);
  };

  const handleSelect = (subscriberId: string) => {
    const newSelected = new Set(selectedSubscribers);

    if (newSelected.has(subscriberId)) {
      newSelected.delete(subscriberId);
    } else {
      newSelected.add(subscriberId);
    }

    setSelectedSubscribers(newSelected);
  };

  const handleSelectAll = () => {
    if (isSelectedAll) {
      setSelectedSubscribers(new Set());
      setFilteredSubscriberData(subscriberData);
    } else {
      const newSelected = new Set(
        subscriberData
          ?.filter((subscriber) => !subscriber?.isCategory)
          .map((subscriber) => subscriber._id)
      );
      setSelectedSubscribers(newSelected);
      setFilteredSubscriberData(
        subscriberData?.filter((subscriber) => !subscriber?.isCategory)
      );
    }
  };

  const fetchSubscriberData = async () => {
    setIsSubscriberLoading(true);

    try {
      const [subscriptionResponse, categoryResponse] = await Promise.all([
        subscriptionService.search({
          sortBy: 'updatedAt',
          sort: 'desc',
          limit: 999,
          offset: 0
        }),
        subscriberCategoryService.findMyCategories({
          includeAvatars: true
        })
      ]);

      const subscriptionData = subscriptionResponse?.data?.data;
      if (!subscriptionData) {
        throw new Error('Invalid subscription data received');
      }

      const subscribers = subscriptionData.map(({ userInfo }) => ({
        ...userInfo,
        isCategory: false
      }));

      const categories = categoryResponse.data.map((category) => ({
        ...category,
        isCategory: true
      }));

      setSubscriberData([...categories, ...subscribers]);
    } catch (error) {
      console.error('Failed to fetch subscriber data:', error);
      setSubscriberData([]);
    } finally {
      setIsSubscriberLoading(false);
    }
  };

  const onNext = () => {
    setIsShowMassMessageInput(true);
  };

  return (
    <>
      <div className={style['selection-header']}>
        <Button
          type="text"
          onClick={toggleSelectionMode}
          icon={<CloseOutlined />}
        />
        <span>{selectedSubscribers.size} subscribers selected</span>
        <Button
          type="primary"
          className={style['mobile-next-btn']}
          disabled={selectedSubscribers.size === 0}
          onClick={onNext}
        >
          Next
        </Button>
      </div>
      <div className={style['subscriber-list']}>
        <Input.Search
          placeholder="Search subscribers and category..."
          className={style['subscriber-search']}
          onChange={(e) => handleSubscriberSearch(e.target.value)}
        />
        <div className={style['subscriber-items']}>
          {isSubsriberLoading ? (
            <div className={style['loading-container']}>
              <Spin />
            </div>
          ) : filteredSubscriberData?.length > 0 ? (
            <>
              <div className={style['select-all-item']}>
                <SubscriberListItem
                  key="select-all"
                  subscriber={{
                    _id: 'select-all',
                    username: 'Select All',
                    avatar: null
                  }}
                  selected={isSelectedAll}
                  onSelect={handleSelectAll}
                  isSelectAll
                />
              </div>
              {filteredSubscriberData.map((subscriber) => (
                <SubscriberListItem
                  key={
                    subscriber.isCategory
                      ? subscriber?.category?._id
                      : subscriber._id
                  }
                  subscriber={subscriber}
                  selected={selectedSubscribers.has(
                    subscriber.isCategory
                      ? subscriber?.category?._id
                      : subscriber._id
                  )}
                  onSelect={handleSelect}
                  isSelectedAll={isSelectedAll}
                />
              ))}
            </>
          ) : (
            <div className={style['empty-state']}>
              <p className={style['empty-text']}>No subscribers found</p>
              <span className={style['empty-subtext']}>
                Try adjusting your search
              </span>
            </div>
          )}
        </div>
      </div>
      {recipientIds.length > 0 && (
        <div className={style['float-next-btn']}>
          <Badge count={recipientIds.length}>
            <Button
              type="primary"
              size="large"
              shape="circle"
              icon={<ArrowRightOutlined />}
              onClick={onNext}
            />
          </Badge>
        </div>
      )}
    </>
  );
};

export default SubscriberList;
