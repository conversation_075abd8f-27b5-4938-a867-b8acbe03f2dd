.selection-header {
  display: flex;
  align-items: center;
  padding: 0 16px;
  height: 64px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);

  span {
    flex: 1;
    font-weight: 500;
  }
}

.subscriber-list {
  display: flex;
  flex-direction: column;
  height: calc(100% - 64px);

  .subscriber-search {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
  }

  .subscriber-items {
    overflow-y: auto;
    height: calc(100vh - 288px);
    min-height: 350px;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    padding-bottom: 80px;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 20px 0;
}

.select-all-item {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;

  // Add subtle shadow when scrolling
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);

  // Special styling for the select all item
  :global {
    .ant-checkbox-wrapper {
      font-weight: 600;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: #8c8c8c;

  .empty-text {
    font-size: 16px;
    margin-bottom: 4px;
  }

  .empty-subtext {
    font-size: 14px;
    opacity: 0.8;
  }
}

.mobile-next-btn {
  display: none;

  @media (max-width: 768px) {
    display: block;
  }
}

.float-next-btn {
  display: none;
  position: fixed;
  bottom: 64px;
  right: 30px;
  z-index: 100;

  @media (max-width: 768px) {
    display: block;
  }

  .ant-badge {
    .ant-badge-count {
      box-shadow: none;
      font-weight: 600;
    }
  }

  button {
    width: 56px;
    height: 56px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:active {
      transform: scale(0.96);
    }
  }
}

@media (max-width: 768px) {
  .selection-header {
    height: 56px;
    padding: 0 8px;

    button {
      padding: 4px 8px;
    }
  }

  .subscriber-list {
    .subscriber-search {
      padding: 8px;
    }
  }

  .select-all-item {
    position: sticky;
    top: 0;
  }

  .subscriber-search {
    position: sticky;
    top: 0;
    z-index: 2;
    background: #fff;
  }

  subscriber-items {
    .ant-checkbox-wrapper {
      padding: 12px 16px;
      margin: 0;
      width: 100%;

      &:active {
        background-color: rgba(0, 0, 0, 0.02);
      }
    }
  }
}
