.subscriberView {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  max-width: 800px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden; // Prevent content from breaking the border radius
}

.controlsWrapper {
  position: sticky;
  top: 0;
  z-index: 2; // Increased z-index to ensure it stays above list items
  background: #fff;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;

  :global {
    .ant-input-search {
      flex: 1;
      min-width: 0; // Prevent input from overflowing
    }

    .ant-select {
      width: 200px;
    }

    .ant-btn {
      white-space: nowrap;
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    padding: 12px 0px;

    :global {
      .ant-input-search,
      .ant-select,
      .ant-btn {
        width: 100% !important;
        margin-left: 0 !important;
      }
    }
  }
}

.subscriberList {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  max-height: 60vh; // Adjust based on header + controls height

  :global {
    .ant-list-empty {
      padding: 24px;
    }

    .ant-spin-nested-loading,
    .ant-spin-container {
      height: 100%;
    }

    .ant-list-item {
      padding: 12px 24px;
      cursor: pointer;
      border: none;
      border-bottom: 1px solid #f0f0f0;
      margin: 0;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

.subscriberContent {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;

  :global {
    .ant-checkbox-wrapper {
      margin-right: 0;
    }

    .ant-avatar {
      flex-shrink: 0;
    }
  }
}

.subscriberContentInfo {
  flex: 1;
  min-width: 0; // Prevent text overflow
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.subscriberContentName {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  margin: 0;
  text-transform: capitalize;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Mobile styles
@media (max-width: 480px) {
  .subscriberView {
    border-radius: 0;
    box-shadow: none;
    height: 100%;
  }

  .controlsWrapper {
    :global {
      .ant-input-group-addon {
        width: auto;
      }
    }
  }

  .subscriberList {
    max-height: 40vh; // Adjusted for mobile layout

    :global {
      .ant-list-item {
        padding: 12px;
      }
    }
  }

  .subscriberContent {
    gap: 12px;
  }
}
