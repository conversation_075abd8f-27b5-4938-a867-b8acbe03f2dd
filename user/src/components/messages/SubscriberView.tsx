import { useState, useEffect } from 'react';
import {
  Avatar,
  List,
  Input,
  Checkbox,
  Typography,
  Select,
  Button,
  message
} from 'antd';
import { UserAddOutlined, UserOutlined } from '@ant-design/icons';

import styles from './SubscriberView.module.less';
import { subscriptionService } from '@services/subscription.service';
import { Category } from './CategoryModal';

const { Search } = Input;
const { Text } = Typography;

interface Subscriber {
  id: string;
  username: string;
  avatar?: string;
  selected?: boolean;
}

interface SubscriberViewProps {
  categoryId?: string;
  type: 'category' | 'all';
  categories: Category[];
}

export default function SubscriberView({
  categoryId,
  type,
  categories
}: SubscriberViewProps) {
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredSubscribers, setFilteredSubscribers] = useState<Subscriber[]>(
    []
  );
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(
    categoryId === 'all' ? undefined : categoryId
  );
  const [addLoading, setAddLoading] = useState(false);

  useEffect(() => {
    if (!categoryId?.length) return;

    if (type === 'all') {
      getAllSubscribers();
    } else {
      getSubscribersByCategory();
    }
  }, [categoryId, type]);

  const getAllSubscribers = async () => {
    setLoading(true);
    try {
      const response = await subscriptionService.search({
        sortBy: 'updatedAt',
        sort: 'desc',
        limit: 999,
        offset: 0
      });

      if (!response?.data?.data) {
        throw new Error('Invalid subscription data received');
      }

      const subscribers = response.data.data.map(
        (subscription) =>
          ({
            id: subscription?._id,
            username: subscription.userInfo?.username,
            avatar: subscription.userInfo?.avatar,
            selected: false
          } as Subscriber)
      );
      setSubscribers(subscribers);
      setFilteredSubscribers(subscribers);
    } catch (error) {
      console.error('Error fetching subscribers:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSubscribersByCategory = async () => {
    setLoading(true);
    try {
      const response = await subscriptionService.getSubscriptionsByCategory(
        categoryId
      );

      if (!response?.data) {
        throw new Error('Invalid subscription data received');
      }

      const subscribers = response.data.map(
        (subscription) =>
          ({
            id: subscription?._id,
            username: subscription.userInfo?.username,
            avatar: subscription.userInfo?.avatar,
            selected: false
          } as Subscriber)
      );
      setSubscribers(subscribers);
      setFilteredSubscribers(subscribers);
    } catch (error) {
      console.error('Error fetching subscribers:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    // Search implementation will be handled by you
    const filtered = subscribers.filter((subscriber) =>
      subscriber.username.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredSubscribers(filtered);
  };

  const handleSubscriberSelect = (subscriberId: string) => {
    setFilteredSubscribers((prevSubscribers) =>
      prevSubscribers.map((sub) =>
        sub.id === subscriberId ? { ...sub, selected: !sub.selected } : sub
      )
    );
  };

  const handleAddToCategory = async () => {
    const selectedSubscriptionIds = filteredSubscribers
      .filter((sub) => sub.selected)
      .map((sub) => sub.id);
    setAddLoading(true);

    try {
      await subscriptionService.updateSubscriptionCategories({
        subscriptionIds: selectedSubscriptionIds,
        categoryId: selectedCategory
      });

      message.success('Subscribers successfully added to category');

      if (type !== 'all') {
        await getSubscribersByCategory();
      }
    } catch (error) {
      console.error('Error adding subscribers to category:', error);
      message.error('Failed to add subscribers to category');
    } finally {
      setAddLoading(false);
    }
  };

  return (
    <div className={styles.subscriberView}>
      <div className={styles.controlsWrapper}>
        <Search
          placeholder="Search subscribers by name or email..."
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          allowClear
          style={{ width: 'calc(100% - 300px)' }}
        />
        <Select
          placeholder="Select category"
          value={selectedCategory === 'all' ? undefined : selectedCategory}
          onChange={setSelectedCategory}
          style={{ width: '200px', marginLeft: 8 }}
          options={categories.map((cat) => ({
            label: cat.name,
            value: cat.id
          }))}
        />
        <Button
          type="primary"
          icon={<UserAddOutlined />}
          onClick={handleAddToCategory}
          disabled={
            !selectedCategory ||
            !filteredSubscribers.some((sub) => sub.selected)
          }
          style={{ marginLeft: 8 }}
          loading={addLoading}
        >
          Add
        </Button>
      </div>

      <List
        className={styles.subscriberList}
        loading={loading}
        dataSource={filteredSubscribers}
        locale={{ emptyText: 'No subscribers found' }}
        renderItem={(subscriber) => (
          <List.Item onClick={() => handleSubscriberSelect(subscriber.id)}>
            <div className={styles.subscriberContent}>
              <Checkbox checked={subscriber.selected} />
              <Avatar
                size={40}
                src={subscriber.avatar}
                icon={!subscriber.avatar && <UserOutlined />}
              />
              <div className={styles.subscriberContentInfo}>
                <Text className={styles.subscriberContentName}>
                  {subscriber.username}
                </Text>
              </div>
            </div>
          </List.Item>
        )}
      />
    </div>
  );
}
