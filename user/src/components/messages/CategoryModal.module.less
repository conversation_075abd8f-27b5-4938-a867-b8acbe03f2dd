/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  gap: 12px;
  position: sticky;
  top: 0;
  z-index: 2;
  background: #fff;
  padding: 4px 0;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

/* Container Layout */
.category-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 73vh;
  overflow-y: auto;
  padding-right: 8px;
  scroll-behavior: smooth;
}

/* Scrollbar Styling */
.category-container::-webkit-scrollbar {
  width: 6px;
}

.category-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.category-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.category-container::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Add Category Form */
.add-category {
  position: sticky;
  top: 0;
  z-index: 1;
  display: flex;
  gap: 12px;
  background: #fafafa;
  padding: 16px;
  border-radius: 12px;
  border: 1px dashed #d9d9d9;
  transition: all 0.3s ease;
  display: flex;
}

.add-category:hover {
  border-color: #1890ff;
  background: #f0f7ff;
}

/* Category List */
.category-list {
  border: none;
  border-radius: 12px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Category Items */
.category-item {
  width: 100%;
  transition: all 0.2s ease;
}

.category-content {
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.2s ease;
}

.category-content:hover {
  background: #fafafa;
  transform: translateY(-1px);
}

.category-tag {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  border: 1px solid #f0f0f0;
  padding: 16px;
  border-radius: 10px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

.category-tag:hover {
  border-color: #d9d9d9;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.06);
}

/* Category Info */
.category-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex-shrink: 0;
}

.category-name {
  font-weight: 600;
  font-size: 15px;
  color: #111827;
  letter-spacing: -0.01em;
}

.subscriber-count {
  color: #6b7280;
  font-size: 13px;
  background: #f3f4f6;
  padding: 4px 12px;
  border-radius: 16px;
  display: inline-block;
  transition: all 0.2s ease;
  width: fit-content;
}

/* Default Category Styling */
.default-category-tag {
  background: var(--primary-color-light);
  border: 1px solid var(--primary-color-fade);
  transition: all 0.3s ease;

  &:hover {
    background: var(--primary-color-fade);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-2);
  }
}

.default-category-tag .category-name {
  color: var(--primary-color);
  font-weight: 600;
}

.default-category-tag .subscriber-count {
  background: var(--primary-color-light);
  color: var(--primary-color);
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 11px;
  font-weight: 600;
}

/* Action Buttons */
.action-buttons {
  opacity: 0;
  transition: all 0.2s ease;
  display: flex;
  gap: 4px;
}

.category-content:hover .action-buttons {
  opacity: 1;
}

/* Skeleton Loading */
.skeleton-content {
  width: 100%;
  padding: 16px;
}

.skeleton-tag {
  display: flex;
  gap: 12px;
  background: var(--bg-secondary);
  padding: 16px;
  border-radius: 10px;
  border: 1px solid var(--border-color-base);
}

.skeleton-name {
  width: 140px;
  height: 20px;
  background: var(--border-color-base);
  border-radius: 4px;
  animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-count {
  width: 100px;
  height: 16px;
  background: #e8e8e8;
  border-radius: 4px;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Global Ant Design Overrides */
.modalWrapper {
  :global(.category-modal .ant-modal-content) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  :global(.category-modal .ant-modal-header) {
    border-bottom: none;
    padding: 20px 24px 0;
  }

  :global(.category-modal .ant-modal-body) {
    padding: 20px 24px;
  }

  :global(.input-icon) {
    color: var(--text-color-secondary);
  }

  :global(.ant-input-affix-wrapper) {
    border-radius: 8px;
    border-color: var(--border-color-base);
    background: var(--input-background);
    transition: all 0.2s ease;
  }

  :global(.ant-input-affix-wrapper:hover) {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-light);
  }

  :global(.ant-input-affix-wrapper:focus-within) {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-color-light);
  }

  :global(.ant-btn) {
    border-radius: 8px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  :global(.ant-btn:hover) {
    transform: translateY(-1px);
  }
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .category-container {
    max-height: 70vh;
    padding-right: 4px;
  }

  .add-category {
    flex-direction: column;
    padding: 12px;
    position: relative;
  }

  .category-tag {
    padding: 12px;
    align-items: flex-center;
    gap: 12px;
  }

  .subscriber-count {
    font-size: 12px;
  }

  .action-buttons {
    opacity: 1;
    width: 100%;
    justify-content: flex-end;
  }

  .modalWrapper {
    :global(.ant-modal-body) {
      padding: 16px;
    }

    :global(.ant-btn) {
      width: 100%;
    }
  }
}
