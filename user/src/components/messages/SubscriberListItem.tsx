import { Avatar, Checkbox } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import style from './SubscriberListItem.module.less';

interface SubscriberListItemProps {
  subscriber: {
    avatar: string;
    username: string;
    _id: string;
    name?: string;
    isCategory?: boolean;
    category?: any;
    subscriberCount?: number;
    subscribers?: any[];
  };
  selected: boolean;
  onSelect: (id: string) => void;
  isSelectAll?: boolean;
  isSelectedAll?: boolean;
}

const SubscriberListItem = ({
  subscriber,
  selected,
  onSelect,
  isSelectAll,
  isSelectedAll
}: SubscriberListItemProps) => {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    onSelect(
      subscriber.isCategory ? subscriber?.category?._id : subscriber._id
    );
  };

  const renderCategoryItem = () =>
    isSelectedAll ? null : (
      <div
        className={classNames(style['subscriber-item'], {
          [style.selected]: selected,
          [style.disabled]: subscriber.subscriberCount === 0
        })}
        onClick={subscriber.subscriberCount > 0 ? handleClick : undefined}
        role={subscriber.subscriberCount > 0 ? 'button' : undefined}
        tabIndex={subscriber.subscriberCount > 0 ? 0 : -1}
      >
        <div className={style.content}>
          <Checkbox
            checked={selected}
            className={style.checkbox}
            disabled={subscriber.subscriberCount === 0}
          />
          <div className={style.categoryInfo}>
            <div className={style.categoryDetails}>
              <span className={style.categoryName}>
                {subscriber.category.name}
              </span>
              <span className={style.subscriberCount}>
                {subscriber.subscriberCount}{' '}
                {subscriber.subscriberCount === 1
                  ? 'subscriber'
                  : 'subscribers'}
              </span>
            </div>
          </div>
          <div className={style.avatarStack}>
            {subscriber.subscribers.slice(0, 3).map((sub, index) => (
              <Avatar
                key={sub.id}
                src={sub.avatar || null}
                icon={!sub.avatar && <UserOutlined />}
                className={style.stackedAvatar}
                style={{ zIndex: 3 - index }}
              />
            ))}
            {subscriber.subscribers.length > 3 && (
              <div className={style.avatarMore}>
                +{subscriber.subscribers.length - 3}
              </div>
            )}
          </div>
        </div>
      </div>
    );

  const renderSubscriberItem = () => (
    <div
      className={classNames(style['subscriber-item'], {
        [style.selected]: selected,
        [style['select-all']]: isSelectAll
      })}
      onClick={handleClick}
      role="button"
      tabIndex={0}
    >
      <div className={style.content}>
        <Checkbox checked={selected} className={style.checkbox} />

        {!isSelectAll && (
          <Avatar
            src={subscriber?.avatar?.length ? subscriber.avatar : null}
            icon={subscriber?.avatar || <UserOutlined />}
            className={style.avatar}
          />
        )}

        <div className={style.info}>
          <span className={style.username}>
            {isSelectAll ? 'Select All Subscribers' : subscriber.username}
          </span>
        </div>
      </div>
    </div>
  );

  return subscriber.isCategory ? renderCategoryItem() : renderSubscriberItem();
};

export default SubscriberListItem;
