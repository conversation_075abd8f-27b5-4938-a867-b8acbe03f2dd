@import '../../../style/vars.less';

.teaserUploadContainer {
  width: 100%;
  margin-bottom: 16px;
  border-radius: 8px;
  padding: 12px;
}

.teaserHeader {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.teaserLabel {
  font-size: 14px;
  font-weight: 500;
  color: @text-color;
}

.teaserBadge {
  background: @theme-color;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.teaserGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
  list-style: none;
  padding: 0;
  margin: 0;

  @media (max-width: 576px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
}

.teaserItem {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  background: @light-grey;

  &:hover {
    .removeButton {
      opacity: 1;
    }
  }
}

.teaserPreviewWrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.previewContainer {
  width: 100%;
  height: 100%;
  position: relative;

  :global(.ant-image) {
    width: 100%;
    height: 100%;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.teaserOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  gap: 4px;

  .anticon {
    font-size: 20px;
  }

  span {
    font-size: 12px;
    font-weight: 500;
  }
}

.placeholderIcon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: @light-grey;
}

.removeButton {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
  padding: 0;

  .anticon {
    color: white;
    font-size: 16px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.8);
  }

  @media (max-width: 576px) {
    opacity: 1;
    width: 20px;
    height: 20px;

    .anticon {
      font-size: 14px;
    }
  }
}

.uploadProgress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0 4px;
  background: rgba(0, 0, 0, 0.4);

  :global {
    .ant-progress-inner {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .ant-progress-bg {
      background-color: @theme-color;
    }

    .ant-progress-text {
      color: white;
      font-size: 12px;
    }
  }
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 0 10px 20px;
  border-color: transparent transparent transparent white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}
