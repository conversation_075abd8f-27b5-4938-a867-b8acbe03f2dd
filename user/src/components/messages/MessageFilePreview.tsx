import { FileOutlined, PlayCircleFilled } from '@ant-design/icons';
import { Badge, Image } from 'antd';
import React from 'react';
import classNames from 'classnames';
import style from './MessageFilePreview.module.less';

interface IFile {
  _id: string;
  name: string;
  type: string;
  mimeType: string;
  url: string;
  thumbnails?: string[];
  blurImage?: string;
  preview?: string;
  status: string;
  mediaType: string;
}

interface IProps {
  files: IFile[];
  maxDisplay?: number;
  size?: 'small' | 'medium' | 'large';
}

const MessageFilePreview: React.FC<IProps> = ({
  files = [],
  maxDisplay = 4,
  size = 'medium'
}) => {
  if (!files.length) return null;

  const displayFiles = files.slice(0, maxDisplay);
  const remainingCount = Math.max(0, files.length - maxDisplay);

  return (
    <div className={classNames(style.filePreviewContainer, style[size])}>
      {displayFiles.map((file, index) => (
        <div key={file._id} className={style.previewItem}>
          {file.mediaType === 'video' ? (
            <div className={style.videoPreview}>
              <video
                src={file.url}
                className={style.thumbnail}
                playsInline
                muted
                preload="metadata"
                poster={file.thumbnails?.[0] || file.preview || file.blurImage}
              />
              <PlayCircleFilled className={style.playIcon} />
            </div>
          ) : file.mimeType?.startsWith('image/') ? (
            <Image
              src={file.url}
              alt={file.name}
              preview={false}
              className={style.thumbnail}
            />
          ) : (
            <div className={style.fileIcon}>
              <FileOutlined />
              <span className={style.fileName}>
                {file.name.length > 15
                  ? `${file.name.substring(0, 12)}...`
                  : file.name}
              </span>
            </div>
          )}
        </div>
      ))}
      {remainingCount > 0 && (
        <Badge count={`+${remainingCount}`} className={style.remainingBadge} />
      )}
    </div>
  );
};

export default MessageFilePreview;
