@import '../../../style/vars.less';

.compose {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  padding: 10px;
  gap: 8px;
  padding-top: 50px;

  :global .toolbar-button {
    color: @grey-color;
    margin-left: 15px;
  }

  :global .toolbar-button:hover {
    color: @grey-color;
  }

  &.custom {
    width: 96%;
    top: unset;
    left: 2%;
    bottom: 10px;
  }

  @media screen and (max-width: 576px) {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 8px;
    background: white;

    .inp-price {
      order: 1;
      width: 70px;
      margin-right: auto;
    }

    .compose-input {
      order: 3;
      width: 100%;
      margin: 0;
      flex-basis: 100%;
    }

    .grp-icons {
      order: 2;
      padding: 0;
      margin-right: 8px;
      display: flex;
      align-items: center;

      &:last-child {
        margin-right: 0;
      }

      .grp-send {
        margin-left: auto;
      }
    }

    .send-button {
      order: 2;
    }
  }
}

.messageStatus {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2;
  min-height: 40px;

  .statusContent {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    justify-content: center;
  }

  .statusIcon {
    font-size: 16px;
    display: flex;
    align-items: center;
  }

  .statusText {
    color: @grey-color;
    font-size: 14px;
    font-weight: 500;
  }

  .backButton {
    position: absolute;
    left: 12px;
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: @theme-color;
    transition: all 0.3s ease;
    border-radius: 50%;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    &:active {
      transform: scale(0.95);
    }

    :global(.anticon) {
      font-size: 20px;
    }
  }

  @media screen and (max-width: 576px) {
    padding: 8px 12px;
    min-height: 36px;

    .statusText {
      font-size: 13px;
    }
  }
}

.inp-price {
  height: 43px;
  display: flex;
  align-items: center;
  border-color: unset;
  border: none;
  max-width: 70px;
  cursor: pointer;
}

.compose-input {
  flex: 1;
  border: none;
  font-size: 14px;
  height: 43px;
  background: none;
  color: @black;
  outline: thin;
  padding: 10px;
  background-color: @light-grey;
  border-radius: 30px;

  &::placeholder {
    color: var(--color-text);
  }

  :global .ant-input {
    background-color: @light-grey;
  }
}

.compose-input::placeholder {
  opacity: 0.3;
}

.grp-icons {
  padding: 0 10px;
  cursor: pointer;
  position: relative;
  font-size: 18px;

  :global .anticon-send {
    font-size: 22px;
    color: @theme-color;
  }

  &.custom {
    display: flex;

    .anticon-send {
      margin-left: 15px;
    }
  }
}

.grp-emotions {
  .anticon {
    font-size: 22px;
    color: var(--color-text);
  }
}

.grp-file-icon {
  :global .avatar-uploader {
    .ant-upload {
      width: auto;
      height: auto;
      background: transparent;
      border: none;
      margin: 0;
    }
  }

  :global .ant-upload {
    font-size: 18px;
  }

  :global .ant-upload-select.ant-upload-select-picture-card .anticon {
    position: unset;
    top: unset;
    left: unset;
    color: var(--color-text);
    font-size: 22px;
    padding: 0;
    background: transparent;
  }
}

.grp-send {
  height: 43px;
  width: 43px;
  line-height: 43px;
  text-align: center;
  background-color: @theme-color;
  color: white;
  border-radius: 50%;
  cursor: pointer;

  :global .anticon-send {
    font-size: 18px;
    color: var(--color-text);
    transform: rotate(-45deg);
  }

  &:hover {
    background: @primary-color;
  }
}

.grp-send-tip {
  padding: 0px;
  display: inline-block;
  vertical-align: middle;
  border: 0;
  box-shadow: none;
  font-size: 20px;

  &:hover {
    color: #444;
  }

  &:focus {
    color: #444;
    border-color: #444;
  }
}

.schedule-btn {
  padding: 0;
  display: inline-block;
  vertical-align: middle;
  border: 0;
  box-shadow: none;
  font-size: 20px;
  color: @theme-color;

  &:hover {
    color: @primary-color;
  }

  &:focus {
    color: @theme-color;
  }

  &:disabled:hover {
    background-color: transparent;
  }

  &:disabled {
    background-color: transparent;
  }
}

.scheduleForm {
  display: flex;
  gap: 16px;
  margin: 20px 0;

  .datePicker,
  .timePicker {
    flex: 1;
  }
}

@media (max-width: 576px) {
  .scheduleForm {
    flex-direction: column;
    gap: 12px;
  }
}

.scheduleStatusBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  position: absolute; // Add fixed positioning
  top: 0; // Position at top
  left: 0;
  right: 0;
  z-index: 1;

  .scheduleInfo {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;

    .anticon {
      color: @theme-color;
    }
  }

  .viewScheduledBtn {
    display: flex;
    align-items: center;
    gap: 4px;
    color: @theme-color;
    font-size: 14px;
    padding: 0;
    height: auto;

    &:hover {
      color: @primary-color;
    }

    .anticon {
      font-size: 12px;
    }
  }

  @media (max-width: 576px) {
    padding: 8px 12px;

    .scheduleInfo {
      font-size: 13px;
    }

    .viewScheduledBtn {
      font-size: 13px;
    }
  }
}

.scheduledListModal {
  :global(.ant-modal-body) {
    padding: 0;
  }
}

.scheduledList {
  overflow-y: auto;
  max-height: 80vh;

  @media screen and (max-width: 576px) {
    max-height: 70vh;
  }

  :global {
    .ant-list-item {
      padding: 16px;
      border-bottom: 1px solid #e9ecef;

      &:last-child {
        border-bottom: none;
      }
    }

    .ant-list-item-meta-title {
      margin-bottom: 4px;
      font-size: 14px;
    }

    .ant-list-item-meta-description {
      font-size: 13px;
      color: #666;
    }

    .ant-list-item-action {
      margin-left: 16px;
    }
  }

  .editMode {
    .editActions {
      display: flex;
      gap: 8px;
      margin-top: 12px;
    }

    .editInput {
      margin-top: 8px;
    }
  }

  .messageText {
    white-space: pre-wrap;
    word-break: break-word;
  }

  @media screen and (max-width: 576px) {
    .editMode {
      .editActions {
        margin-top: 8px;
      }

      button {
        padding: 4px 12px;
        font-size: 13px;
      }
    }
  }
}

.actionButton {
  padding: 0px !important;
}

.filePreview {
  position: relative;
  width: 100%;
}
