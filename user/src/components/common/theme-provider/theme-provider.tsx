import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { ConfigProvider } from 'antd';
import { loadUIValue } from '../../../redux/ui/actions';
import { useTheme } from '../../../hooks/useTheme';

interface ThemeProviderProps {
  children: React.ReactNode;
}

const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const { theme: themeMode, actualTheme } = useTheme();

  useEffect(() => {
    // Load UI values from localStorage on initial mount
    dispatch(loadUIValue());
  }, [dispatch]);

  useEffect(() => {
    // Add theme transition class for smooth transitions
    const addTransitionClass = () => {
      document.body.classList.add('theme-transition');
      setTimeout(() => {
        document.body.classList.remove('theme-transition');
      }, 300);
    };

    // Initialize theme on mount
    addTransitionClass();
  }, [actualTheme]);

  return <ConfigProvider>{children}</ConfigProvider>;
};

export default ThemeProvider;
