@import '../../../../style/vars.less';

.themeToggle {
  position: relative;
  display: inline-block;
}

.toggleButton {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-color, @text-color);
  position: relative;
  overflow: hidden;

  // Subtle background for better visual hierarchy
  background: var(--component-background, @white);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-color, @primary-color);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
  }

  &:hover {
    color: var(--primary-color, @primary-color);
    transform: translateY(-1px);

    &::before {
      opacity: 0.05;
    }
  }

  &:focus {
    outline: none;
  }

  &:active {
    transform: translateY(0);
    transition-duration: 0.1s;
  }

  .anticon {
    font-size: 18px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
  }

  &:hover .anticon {
    transform: scale(1.1) rotate(5deg);
  }

  &:active .anticon {
    transform: scale(0.95);
  }
}

.buttonLabel {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  position: relative;
  z-index: 1;
  margin-left: 4px;
  transition: all 0.3s ease;
}

.dropdownMenu {
  min-width: 220px;
  padding: 12px;
  border-radius: 16px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(0, 0, 0, 0.05);
  background: var(--component-background, @white);
  border: none;
  backdrop-filter: blur(8px);
  transform-origin: top;

  @keyframes dropdownFadeIn {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(-10px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }
}

.menuItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 6px;
  position: relative;
  min-height: 56px; // Better touch target
  border: 2px solid transparent;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    background-color: var(--item-hover-bg, rgba(0, 0, 0, 0.04));
    transform: translateX(4px);
    border-color: var(--primary-color-fade, fade(@primary-color, 20%));
  }

  &:focus {
    outline: none;
    background-color: var(--item-hover-bg, rgba(0, 0, 0, 0.04));
    border-color: var(--primary-color, @primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-fade, fade(@primary-color, 20%));
  }

  &:active {
    transform: translateX(2px);
    transition-duration: 0.1s;
  }
}

.menuItemActive {
  background-color: var(--primary-color-fade, fade(@primary-color, 8%));
  border-color: var(--primary-color-fade, fade(@primary-color, 30%));

  &:hover {
    background-color: var(--primary-color-fade, fade(@primary-color, 12%));
  }
}

.menuItemContent {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.menuItemIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 18px;
  color: var(--text-color-secondary, @gray);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 6px;
  background: var(--icon-bg, rgba(0, 0, 0, 0.04));

  .menuItem:hover & {
    color: var(--primary-color, @primary-color);
    background: var(--primary-color-fade, fade(@primary-color, 15%));
    transform: scale(1.1);
  }

  .menuItemActive & {
    color: var(--primary-color, @primary-color);
    background: var(--primary-color-fade, fade(@primary-color, 20%));
  }
}

.menuItemText {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.menuItemLabel {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-color, @text-color);
  line-height: 1.4;
  transition: color 0.3s ease;

  .menuItem:hover & {
    color: var(--primary-color, @primary-color);
  }

  .menuItemActive & {
    color: var(--primary-color, @primary-color);
  }
}

.menuItemDescription {
  font-size: 13px;
  color: var(--text-color-secondary, @gray);
  line-height: 1.3;
  transition: color 0.3s ease;

  .menuItem:hover & {
    color: var(--text-color, @text-color);
  }
}

.checkIcon {
  color: var(--success-color, @success-color);
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: checkIconAppear 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  @keyframes checkIconAppear {
    from {
      opacity: 0;
      transform: scale(0.5);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
}

// Dark theme specific styles
:global(.dark-mode) {
  .toggleButton {
    color: rgba(255, 255, 255, 0.85);
    background: rgba(255, 255, 255, 0.05);

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: var(--primary-color, @primary-color);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    &:focus {
      box-shadow: 0 0 0 3px var(--primary-color-fade, fade(@primary-color, 30%));
    }
  }

  .dropdownMenu {
    background: #1a1a1a;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.4),
      0 10px 10px -5px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .menuItem {
    border-color: transparent;

    &:hover {
      background-color: rgba(255, 255, 255, 0.08);
      border-color: var(--primary-color-fade, fade(@primary-color, 30%));
    }

    &:focus {
      background-color: rgba(255, 255, 255, 0.08);
      border-color: var(--primary-color, @primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-fade, fade(@primary-color, 30%));
    }
  }

  .menuItemActive {
    background-color: var(--primary-color-fade, fade(@primary-color, 15%));
    border-color: var(--primary-color-fade, fade(@primary-color, 40%));

    &:hover {
      background-color: var(--primary-color-fade, fade(@primary-color, 20%));
    }
  }

  .menuItemIcon {
    color: rgba(255, 255, 255, 0.65);
    background: rgba(255, 255, 255, 0.05);

    .menuItem:hover & {
      color: var(--primary-color, @primary-color);
      background: var(--primary-color-fade, fade(@primary-color, 20%));
    }

    .menuItemActive & {
      color: var(--primary-color, @primary-color);
      background: var(--primary-color-fade, fade(@primary-color, 25%));
    }
  }

  .menuItemLabel {
    color: rgba(255, 255, 255, 0.9);

    .menuItem:hover & {
      color: var(--primary-color, @primary-color);
    }

    .menuItemActive & {
      color: var(--primary-color, @primary-color);
    }
  }

  .menuItemDescription {
    color: rgba(255, 255, 255, 0.5);

    .menuItem:hover & {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

// Responsive design
@media (max-width: @tablet-screen) {
  .dropdownMenu {
    min-width: 200px;
    padding: 10px;
  }

  .menuItem {
    padding: 12px 14px;
    min-height: 52px;
  }

  .menuItemContent {
    gap: 14px;
  }

  .menuItemIcon {
    width: 22px;
    height: 22px;
    font-size: 16px;
  }

  .menuItemLabel {
    font-size: 14px;
  }

  .menuItemDescription {
    font-size: 12px;
  }
}

@media (max-width: @mobile-screen) {
  .themeToggle {
    // Ensure proper touch targets on mobile
    min-width: 44px;
    min-height: 44px;
  }

  .toggleButton {
    min-width: 44px;
    min-height: 44px;
    padding: 10px;
    border-radius: 10px;

    .anticon {
      font-size: 20px;
    }
  }

  .buttonLabel {
    display: none; // Hide labels on mobile for space
  }

  .dropdownMenu {
    min-width: 180px;
    padding: 8px;
    border-radius: 14px;
    // Position dropdown better on mobile
    max-width: calc(100vw - 32px);
  }

  .menuItem {
    padding: 12px;
    min-height: 48px;
    border-radius: 10px;
  }

  .menuItemContent {
    gap: 12px;
  }

  .menuItemIcon {
    width: 20px;
    height: 20px;
    font-size: 16px;
  }

  .menuItemText {
    gap: 2px;
  }

  .menuItemLabel {
    font-size: 14px;
  }

  .menuItemDescription {
    font-size: 11px;
  }

  .checkIcon {
    font-size: 14px;
  }
}

// Extra small screens
@media (max-width: @max-smaller-screen) {
  .dropdownMenu {
    min-width: 160px;
    padding: 6px;
  }

  .menuItem {
    padding: 10px;
    min-height: 44px;
  }

  .menuItemIcon {
    width: 18px;
    height: 18px;
    font-size: 14px;
  }

  .menuItemLabel {
    font-size: 13px;
  }

  .menuItemDescription {
    font-size: 10px;
  }
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .toggleButton,
  .menuItem,
  .menuItemIcon,
  .menuItemLabel,
  .menuItemDescription,
  .checkIcon {
    transition: none;
    animation: none;
  }

  .toggleButton:hover .anticon {
    transform: none;
  }

  .menuItem:hover {
    transform: none;
  }

  .menuItemIcon {
    .menuItem:hover & {
      transform: none;
    }
  }

  .dropdownMenu {
    animation: none;
  }

  .checkIcon {
    animation: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .menuItem {
    border-width: 2px;
  }

  .dropdownMenu {
    border: 2px solid currentColor;
  }
}
