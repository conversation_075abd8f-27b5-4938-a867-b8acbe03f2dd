import React, {
  useState,
  useRef,
  useCallback,
  useEffect,
  useMemo
} from 'react';
import { Dropdown, But<PERSON>, Tooltip, Menu } from 'antd';
import { DesktopOutlined, CheckOutlined } from '@ant-design/icons';
import Icon from '@ant-design/icons';
import { useTheme, ThemeMode } from '../../../hooks/useTheme';
import { useOutsideAlerter } from '../../../hooks/use-outside-alerter';
import styles from './theme-toggle.module.less';

// Custom Sun Icon SVG
const SunSvg = () => (
  <svg width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z" />
  </svg>
);

// Custom Moon Icon SVG
const MoonSvg = () => (
  <svg width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor">
    <path d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z" />
  </svg>
);

// Custom Icon Components
const SunIcon = (props: any) => <Icon component={SunSvg} {...props} />;
const MoonIcon = (props: any) => <Icon component={MoonSvg} {...props} />;

interface ThemeToggleProps {
  size?: 'small' | 'middle' | 'large';
  showLabel?: boolean;
  placement?:
    | 'topLeft'
    | 'topCenter'
    | 'topRight'
    | 'bottomLeft'
    | 'bottomCenter'
    | 'bottomRight';
  className?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  size = 'middle',
  showLabel = false,
  placement = 'bottomRight',
  className = ''
}) => {
  const { theme, actualTheme, setTheme } = useTheme();
  const [visible, setVisible] = useState(false);
  const dropdownRef = useRef(null);

  // Enhanced keyboard navigation
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      setVisible(false);
    }
  }, []);

  // Improved theme change handler with animation
  const handleThemeChange = useCallback(
    (newTheme: ThemeMode) => {
      setTheme(newTheme);
      setVisible(false);
    },
    [setTheme]
  );

  const themeOptions = [
    {
      key: 'light',
      label: 'Light',
      icon: <SunIcon />,
      description: 'Light theme'
    },
    {
      key: 'dark',
      label: 'Dark',
      icon: <MoonIcon />,
      description: 'Dark theme'
    },
    {
      key: 'system',
      label: 'System',
      icon: <DesktopOutlined />,
      description: 'Follow system preference'
    }
  ];

  const getCurrentIcon = () => {
    if (theme === 'system') {
      return <DesktopOutlined />;
    }
    return actualTheme === 'dark' ? <MoonIcon /> : <SunIcon />;
  };

  const getCurrentLabel = () => {
    if (theme === 'system') {
      return `System (${actualTheme})`;
    }
    return theme.charAt(0).toUpperCase() + theme.slice(1);
  };

  const menuRender = useMemo(
    () => (
      <Menu className={styles.dropdownMenu}>
        {themeOptions.map((option) => (
          <Menu.Item
            key={option.key}
            onClick={() => handleThemeChange(option.key as ThemeMode)}
            className={`${styles.menuItem} ${
              theme === option.key ? styles.menuItemActive : ''
            }`}
          >
            <div className={styles.menuItemContent}>
              <span className={styles.menuItemIcon} aria-hidden="true">
                {option.icon}
              </span>
              <div className={styles.menuItemText}>
                <span className={styles.menuItemLabel}>{option.label}</span>
                <span className={styles.menuItemDescription}>
                  {option.description}
                </span>
              </div>
              {theme === option.key && (
                <CheckOutlined
                  className={styles.checkIcon}
                  aria-label="Currently selected"
                />
              )}
            </div>
          </Menu.Item>
        ))}
      </Menu>
    ),
    [theme]
  );

  return (
    <div
      ref={dropdownRef}
      className={`${styles.themeToggle} ${className}`}
      onKeyDown={handleKeyDown}
    >
      <Dropdown
        overlay={menuRender}
        placement={placement}
        trigger={['click']}
        visible={visible}
        onVisibleChange={setVisible}
        overlayClassName={styles.themeDropdown}
        getPopupContainer={(triggerNode) =>
          triggerNode.parentNode as HTMLElement
        }
      >
        <Button
          type="text"
          size={size}
          icon={getCurrentIcon()}
          className={styles.toggleButton}
          onClick={() => setVisible(!visible)}
          aria-label={`Theme toggle - currently ${getCurrentLabel()}`}
          aria-expanded={visible}
          aria-haspopup="menu"
        >
          {showLabel && (
            <span className={styles.buttonLabel} aria-hidden="true">
              {getCurrentLabel()}
            </span>
          )}
        </Button>
      </Dropdown>
    </div>
  );
};

export default ThemeToggle;
