@import '../../../style/vars.less';

.product-name {
  text-transform: capitalize;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--text-color);
  font-weight: 500;
  transition: color 0.3s ease;

  &:hover {
    color: var(--primary-color);
  }
}

.price-display {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 16px;
}

.quantity-input {
  .ant-input-number {
    background: var(--input-background);
    border-color: var(--border-color-base);
    border-radius: 6px;
    min-width: 80px;
    
    .ant-input-number-input {
      background: transparent;
      color: var(--text-color);
      text-align: center;
    }

    &:hover {
      border-color: var(--primary-color);
    }

    &:focus-within {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-color-light);
    }

    &.ant-input-number-disabled {
      background: var(--bg-disabled);
      color: var(--text-color-disabled);
      cursor: not-allowed;
    }
  }
}

.remove-button {
  .ant-btn.danger {
    background: var(--error-color);
    border-color: var(--error-color);
    color: var(--color-white);
    border-radius: 6px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      background: #ff4d4f;
      border-color: #ff4d4f;
      transform: translateY(-1px);
      box-shadow: var(--shadow-2);
    }

    &:focus {
      background: var(--error-color);
      border-color: var(--error-color);
      box-shadow: 0 0 0 2px rgba(240, 65, 52, 0.2);
    }

    .anticon {
      font-size: 14px;
    }
  }
}

// Table container styling
.table-cart-container {
  background: var(--component-background);
  border-radius: 12px;
  border: 1px solid var(--border-color-base);
  box-shadow: var(--shadow-2);
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-3);
  }

  .ant-table {
    background: transparent;

    .ant-table-thead > tr > th {
      background: var(--bg-secondary);
      border-bottom: 1px solid var(--border-color-base);
      color: var(--text-color);
      font-weight: 600;
      padding: 16px 12px;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .ant-table-tbody > tr > td {
      background: var(--component-background);
      border-bottom: 1px solid var(--border-color-split);
      color: var(--text-color);
      padding: 16px 12px;
      transition: all 0.2s ease;
      vertical-align: middle;
    }

    .ant-table-tbody > tr:hover > td {
      background: var(--item-hover-bg);
    }

    .ant-table-tbody > tr:last-child > td {
      border-bottom: none;
    }

    // Product image column
    .ant-table-tbody > tr > td:first-child {
      width: 80px;
      text-align: center;

      img {
        border-radius: 8px;
        border: 1px solid var(--border-color-base);
        transition: all 0.3s ease;
        
        &:hover {
          transform: scale(1.05);
          box-shadow: var(--shadow-2);
        }
      }
    }

    // Name column
    .ant-table-tbody > tr > td:nth-child(2) {
      min-width: 150px;
    }

    // Price column
    .ant-table-tbody > tr > td:nth-child(3) {
      width: 100px;
      text-align: center;
    }

    // Quantity column
    .ant-table-tbody > tr > td:nth-child(4) {
      width: 120px;
      text-align: center;
    }

    // Action column
    .ant-table-tbody > tr > td:last-child {
      width: 80px;
      text-align: center;
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 12px 8px;
        font-size: 12px;
      }

      .ant-table-tbody > tr > td:first-child {
        width: 60px;

        img {
          width: 45px !important;
          height: auto;
        }
      }

      .ant-table-tbody > tr > td:nth-child(2) {
        min-width: 100px;
      }

      .ant-table-tbody > tr > td:nth-child(3),
      .ant-table-tbody > tr > td:nth-child(4) {
        width: 80px;
      }

      .ant-table-tbody > tr > td:last-child {
        width: 60px;
      }
    }

    .product-name {
      max-width: 80px;
      font-size: 12px;
    }

    .price-display {
      font-size: 14px;
    }

    .quantity-input .ant-input-number {
      min-width: 60px;
    }

    .remove-button .ant-btn.danger {
      width: 32px;
      height: 32px;
    }
  }
}

// Empty state styling
.empty-cart {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-color-secondary);

  .empty-icon {
    font-size: 48px;
    color: var(--text-color-tertiary);
    margin-bottom: 16px;
  }

  .empty-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
  }

  .empty-description {
    font-size: 14px;
    color: var(--text-color-secondary);
    margin-bottom: 24px;
  }

  .shop-link {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      color: var(--primary-color);
      opacity: 0.8;
      text-decoration: underline;
    }
  }
}
