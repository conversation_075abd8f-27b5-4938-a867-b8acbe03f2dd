.recipientModal {
  :global(.ant-modal-body) {
    padding: 0;
  }
}

.selectionContainer {
  height: 60vh;
  display: flex;
  flex-direction: column;

  :global(.ant-tabs-nav) {
    margin: 0 16px;
  }
}

.searchInput {
  margin: 16px 16px 12px;

  :global(.ant-input-wrapper) {
    width: calc(100% - 30px) !important;
  }
}

.tabContent {
  height: calc(60vh - 120px);
  overflow-y: auto;
  padding: 0 16px 16px;
}

.checkbox {
  margin-right: 12px;
}

.recipientItem {
  display: flex;
  align-items: center;
  padding: 12px;
  margin: 4px 0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  min-height: 56px; // Add minimum height

  .checkBox {
    flex-shrink: 0; // Prevent checkbox from shrinking
  }

  &:hover {
    background-color: rgba(0, 0, 0, 0.03);
  }

  &.selected {
    background-color: rgba(24, 144, 255, 0.1);
  }

  &.selectAll {
    font-weight: 500;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 8px;
    padding-bottom: 12px;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      background-color: transparent;
    }
  }
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;
  flex-shrink: 0;
}

.itemContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0; // Prevent content overflow
}

.name {
  font-weight: 500;
}

.count {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.avatarStack {
  display: flex;
  align-items: center;
  margin-top: 8px; // Increase spacing
  flex-wrap: nowrap; // Prevent wrapping
}

.stackedAvatar {
  width: 24px !important;
  height: 24px !important;
  border: 2px solid white;
  margin-right: -8px !important; // Adjust overlap
  flex-shrink: 0; // Prevent shrinking
}

.moreCount {
  margin-left: 14px; // Adjust spacing
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 24px; // Match avatar height
}

.loading {
  display: flex;
  justify-content: center;
  padding: 24px 0;
}

@media (max-width: 768px) {
  .selectionContainer {
    height: 70vh;
  }

  .tabContent {
    height: calc(70vh - 120px);
  }
}
