.stateContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  width: 100%;
  padding: 24px;

  :global(.ant-alert) {
    max-width: 480px;

    .ant-alert-message {
      font-size: 16px;
      margin-bottom: 8px;
    }

    .ant-alert-description {
      font-size: 14px;
      margin-bottom: 16px;
    }
  }
}

.loadingContent {
  margin-top: 24px;
  text-align: center;
  color: rgba(0, 0, 0, 0.45);

  p {
    margin-top: 16px;
    font-size: 14px;
  }
}
