import { <PERSON>, <PERSON><PERSON>, But<PERSON> } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import styles from './StateViews.module.less';

export const LoadingView: React.FC = () => (
  <div className={styles.stateContainer}>
    <Spin size="large">
      <div className={styles.loadingContent}>
        <p>Loading your scheduled content</p>
      </div>
    </Spin>
  </div>
);

interface ErrorViewProps {
  message: string;
  onRetry: () => void;
}

export const ErrorView: React.FC<ErrorViewProps> = ({ message, onRetry }) => (
  <div className={styles.stateContainer}>
    <Alert
      message="Error Loading Content"
      description={message}
      type="error"
      showIcon
      action={
        <Button
          icon={<ReloadOutlined />}
          onClick={onRetry}
          type="primary"
          danger
        >
          Try Again
        </Button>
      }
    />
  </div>
);
