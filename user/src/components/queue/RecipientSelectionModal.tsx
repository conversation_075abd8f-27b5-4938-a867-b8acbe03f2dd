import { useState, useEffect, useMemo } from 'react';
import { Modal, Input, Button, Spin, Tabs, Avatar, Checkbox } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import styles from './RecipientSelectionModal.module.less';
import { subscriberCategoryService } from '@services/subscriber-category.service';

interface RecipientSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (selectedIds: string[]) => void;
  conversations: any[];
}

const RecipientSelectionModal = ({
  visible,
  onClose,
  onSelect,
  conversations
}: RecipientSelectionModalProps) => {
  const [selectedSubscriberIds, setSelectedSubscriberIds] = useState<
    Set<string>
  >(new Set());
  const [selectedCategoryIds, setSelectedCategoryIds] = useState<Set<string>>(
    new Set()
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('subscribers');

  const filteredData = useMemo(() => {
    const normalized = searchQuery.toLowerCase().trim();
    if (activeTab === 'subscribers') {
      return conversations.filter((conv) =>
        conv.recipientInfo.username.toLowerCase().includes(normalized)
      );
    } else {
      return categories.filter((cat) =>
        cat.category.name.toLowerCase().includes(normalized)
      );
    }
  }, [searchQuery, conversations, categories, activeTab]);

  const totalUniqueSubscribers = useMemo(() => {
    const recipientIds = new Set<string>();

    // Add subscribers from selected categories
    selectedCategoryIds.forEach((categoryId) => {
      const category = categories.find(
        (cat) => cat.category._id === categoryId
      );
      if (category?.subscribers?.length) {
        category.subscribers.forEach((subscriber) =>
          recipientIds.add(subscriber.id)
        );
      }
    });

    // Add individually selected subscribers
    selectedSubscriberIds.forEach((subscriberId) => {
      recipientIds.add(subscriberId);
    });

    return recipientIds;
  }, [selectedCategoryIds, selectedSubscriberIds, categories]);

  useEffect(() => {
    if (visible) {
      fetchCategories();
    }
  }, [visible]);

  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await subscriberCategoryService.findMyCategories({
        includeAvatars: true
      });
      setCategories(
        response.data.map((category) => ({
          ...category,
          isCategory: true
        }))
      );
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  const toggleSelection = (id: string, isCategory = false) => {
    if (isCategory) {
      const newSelected = new Set(selectedCategoryIds);
      if (newSelected.has(id)) {
        newSelected.delete(id);
      } else {
        newSelected.add(id);
      }
      setSelectedCategoryIds(newSelected);
    } else {
      const newSelected = new Set(selectedSubscriberIds);
      if (newSelected.has(id)) {
        newSelected.delete(id);
      } else {
        newSelected.add(id);
      }
      setSelectedSubscriberIds(newSelected);
    }
  };

  const isSelectedAll = useMemo(() => {
    const currentList =
      activeTab === 'subscribers' ? conversations : categories;
    const currentSelectedIds =
      activeTab === 'subscribers' ? selectedSubscriberIds : selectedCategoryIds;

    // Only consider selectable items for the "all selected" state
    const selectableItems =
      activeTab === 'categories'
        ? currentList.filter((item) => item.subscriberCount > 0)
        : currentList;

    return (
      selectableItems.length > 0 &&
      selectableItems.every((item) =>
        currentSelectedIds.has(
          activeTab === 'subscribers'
            ? item.recipientInfo?._id
            : item.category._id
        )
      )
    );
  }, [
    selectedSubscriberIds,
    selectedCategoryIds,
    conversations,
    categories,
    activeTab
  ]);

  const handleSelectAll = () => {
    if (isSelectedAll) {
      if (activeTab === 'subscribers') {
        setSelectedSubscriberIds(new Set());
      } else {
        setSelectedCategoryIds(new Set());
      }
    } else {
      const currentList =
        activeTab === 'subscribers' ? conversations : categories;
      const selectableItems =
        activeTab === 'categories'
          ? currentList.filter((item) => item.subscriberCount > 0)
          : currentList;

      if (activeTab === 'subscribers') {
        setSelectedSubscriberIds(
          new Set(selectableItems.map((item) => item.recipientInfo?._id))
        );
      } else {
        setSelectedCategoryIds(
          new Set(selectableItems.map((item) => item.category._id))
        );
      }
    }
  };

  const handleConfirm = () => {
    onSelect(Array.from(totalUniqueSubscribers));
    onClose();
  };

  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };

  const renderListItem = (item: any) => {
    const isCategory = activeTab === 'categories';
    const id = isCategory ? item.category._id : item.recipientInfo._id;
    const name = isCategory ? item.category.name : item.recipientInfo.username;
    const avatar = isCategory ? null : item.recipientInfo.avatar;
    const currentSelectedIds = isCategory
      ? selectedCategoryIds
      : selectedSubscriberIds;
    const selected = currentSelectedIds.has(id);
    const isDisabled = isCategory && item.subscriberCount === 0;

    return (
      <div
        key={id}
        className={`${styles.recipientItem} ${
          selected ? styles.selected : ''
        } ${isDisabled ? styles.disabled : ''}`}
        onClick={() => !isDisabled && toggleSelection(id, isCategory)}
      >
        <Checkbox
          checked={selected}
          className={styles.checkbox}
          disabled={isDisabled}
        />

        {!isCategory && (
          <Avatar
            src={avatar || undefined}
            icon={!avatar && <UserOutlined />}
            className={styles.avatar}
          />
        )}

        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            width: '100%'
          }}
        >
          <div className={styles.itemContent}>
            <span className={styles.name}>{name}</span>
            {isCategory && (
              <span className={styles.count}>
                {item.subscriberCount} subscribers
              </span>
            )}
          </div>
          {isCategory && item.subscribers?.length > 0 && (
            <div className={styles.avatarStack}>
              {item.subscribers.slice(0, 3).map((sub, index) => (
                <Avatar
                  key={sub.id}
                  src={sub.avatar || undefined}
                  icon={!sub.avatar && <UserOutlined />}
                  className={styles.stackedAvatar}
                  style={{ zIndex: 3 - index }}
                />
              ))}
              {item.subscribers.length > 3 && (
                <span className={styles.moreCount}>
                  +{item.subscribers.length - 3}
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Modal
      title="Select Recipients"
      visible={visible}
      onCancel={onClose}
      width={600}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Cancel
        </Button>,
        <Button
          key="confirm"
          type="primary"
          onClick={handleConfirm}
          disabled={totalUniqueSubscribers.size === 0}
        >
          Confirm ({totalUniqueSubscribers.size} selected)
        </Button>
      ]}
      className={styles.recipientModal}
    >
      <div className={styles.selectionContainer}>
        <Input.Search
          placeholder="Search..."
          onSearch={handleSearch}
          onChange={(e) => {
            e.preventDefault();
            setSearchQuery(e.target.value || '');
          }}
          className={styles.searchInput}
          allowClear
        />

        <Tabs
          defaultActiveKey="subscribers"
          tabPosition="top"
          onChange={handleTabChange}
        >
          <Tabs.TabPane tab={<span>Subscribers</span>} key="subscribers">
            <div className={styles.tabContent}>
              <div
                className={`${styles.recipientItem} ${styles.selectAll}`}
                onClick={handleSelectAll}
              >
                <Checkbox checked={isSelectedAll} className={styles.checkbox} />
                <span>Select All</span>
              </div>
              {loading ? (
                <div className={styles.loading}>
                  <Spin />
                </div>
              ) : (
                filteredData.map(renderListItem)
              )}
            </div>
          </Tabs.TabPane>

          <Tabs.TabPane tab={<span>Categories</span>} key="categories">
            <div className={styles.tabContent}>
              <div
                className={`${styles.recipientItem} ${styles.selectAll}`}
                onClick={handleSelectAll}
              >
                <Checkbox checked={isSelectedAll} className={styles.checkbox} />
                <span>Select All Categories</span>
              </div>
              {loading ? (
                <div className={styles.loading}>
                  <Spin />
                </div>
              ) : (
                filteredData.map(renderListItem)
              )}
            </div>
          </Tabs.TabPane>
        </Tabs>
      </div>
    </Modal>
  );
};

export default RecipientSelectionModal;
