import {
  Avatar,
  Button,
  Calendar,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Select,
  TimePicker
} from 'antd';
import { Dispatch, useEffect, useState } from 'react';
import {
  FileOutlined,
  PaperClipOutlined,
  VideoCameraOutlined
} from '@ant-design/icons';

import styles from './ScheduleMessageModal.module.less';
import { messageService } from '@services/message.service';
import VaultSelectButton from '@components/vault/vault-select-button';
import RecipientSelectionModal from './RecipientSelectionModal';

interface ScheduleMessageModalProps {
  isModalVisible: boolean;
  setIsModalVisible: Dispatch<boolean>;
  isMobileView: boolean;
  dayjs: any;
  selectedDate: string;
  fetchScheduledMessages: () => Promise<void>;
}

const ScheduleMessageModal = ({
  isModalVisible,
  setIsModalVisible,
  isMobileView,
  dayjs,
  selectedDate,
  fetchScheduledMessages
}: ScheduleMessageModalProps) => {
  const [form] = Form.useForm();

  const [conversations, setConversations] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [vaultOpen, setVaultOpen] = useState(false);
  const [previewFiles, setPreviewFiles] = useState<any[]>([]);
  const [selectedRecipients, setSelectedRecipients] = useState<string[]>([]);
  const [isRecipientModalVisible, setIsRecipientModalVisible] = useState(false);

  useEffect(() => {
    fetchConversations();
  }, []);

  const fetchConversations = async () => {
    const response = await messageService.getConversations({
      type: 'private'
    });
    setConversations(response.data?.data || []);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setSelectedRecipients([]);
    form.resetFields();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const { date, time, content, price } = values;
      const dateTime = date.clone().hour(time.hour()).minute(time.minute());
      const fileIds =
        previewFiles?.map((item) => item.originFileObj?.fileId) || [];

      await messageService.sendMassMessage({
        message: content || '',
        recipientIds: selectedRecipients,
        fileIds,
        price,
        scheduledFor: dateTime.toISOString()
      });

      message.success('Message scheduled successfully!');
      handleCancel();
      await fetchScheduledMessages();
    } catch (error) {
      console.error('Error scheduling message:', error);
      message.error('Failed to schedule message');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectVault = async (media) => {
    const mediaItems = Array.isArray(media) ? media : [media];
    const previewFiles = mediaItems.map((item) => ({
      uid: item._id,
      name: item.name || 'Media',
      type:
        item.file?.mimeType ||
        (item.file?.type?.includes('video') ? 'video/mp4' : 'image/jpeg'),
      preview: item.file?.url || item.file?.thumbnailUrl || '',
      originFileObj: item
    }));

    setVaultOpen(false);
    setPreviewFiles(previewFiles);
  };

  const handleCloseVault = () => {
    setVaultOpen(false);
  };

  const handleRemoveFile = (fileToRemove) => {
    setPreviewFiles(
      previewFiles.filter((file) => file.uid !== fileToRemove.uid)
    );
  };

  const handleRecipientsSelected = (ids: string[]) => {
    setSelectedRecipients(ids);
    form.setFieldsValue({ recipient: ids });
  };

  const renderAttachmentSection = () => (
    <div className={styles.attachmentSection}>
      <div
        className={styles.attachmentButton}
        onClick={() => setVaultOpen(true)}
      >
        <PaperClipOutlined />
        <span>Add files from vault</span>
      </div>

      {previewFiles?.length > 0 && (
        <div className={styles.attachmentPreview}>
          {previewFiles.map((file) => (
            <div key={file.uid} className={styles.previewItem}>
              {file.type?.includes('image') ? (
                <img src={file.preview} alt={file.name} />
              ) : file.type?.includes('video') ? (
                <VideoCameraOutlined className={styles.fileIcon} />
              ) : (
                <FileOutlined className={styles.fileIcon} />
              )}
              <div
                className={styles.removeButton}
                onClick={() => handleRemoveFile(file)}
              >
                ×
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const handleRenderPreview = () => (
    <Form.Item label="Attachments">{renderAttachmentSection()}</Form.Item>
  );

  return (
    <>
      <Modal
        title="Schedule a Message"
        visible={isModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="back" onClick={handleCancel}>
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleSubmit}
            loading={loading}
          >
            Schedule
          </Button>
        ]}
        bodyStyle={{
          maxHeight: isMobileView ? '60vh' : '70vh',
          overflowY: 'auto'
        }}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            date: dayjs(selectedDate),
            time: dayjs()
          }}
        >
          <Form.Item
            name="date"
            label="Date"
            rules={[{ required: true, message: 'Please select a date' }]}
          >
            <Calendar fullscreen={false} />
          </Form.Item>

          <Form.Item
            name="time"
            label="Time"
            rules={[{ required: true, message: 'Please select a time' }]}
          >
            <TimePicker
              format="h:mm A"
              minuteStep={10}
              className={styles.timePicker}
            />
          </Form.Item>

          <Form.Item
            name="recipient"
            label="Recipients"
            rules={[
              {
                required: true,
                message: 'Please select at least one recipient'
              }
            ]}
          >
            <div className={styles.recipientField}>
              <Button
                onClick={() => setIsRecipientModalVisible(true)}
                block
                style={{ textAlign: 'left' }}
              >
                {selectedRecipients.length === 0
                  ? 'Select recipients'
                  : `${selectedRecipients.length} recipients selected`}
              </Button>

              {selectedRecipients.length > 0 && (
                <div className={styles.selectedRecipients}>
                  {selectedRecipients.map((id) => {
                    const conv = conversations.find(
                      (c) => c.recipientInfo?._id === id
                    );
                    return (
                      <div key={id} className={styles.recipientTag}>
                        {conv?.recipientInfo.username}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </Form.Item>

          <Form.Item name="content" label="Content">
            <Input.TextArea rows={4} placeholder="Enter your content here" />
          </Form.Item>

          <Form.Item
            name="price"
            label="Price"
            rules={[
              {
                type: 'number',
                min: 0,
                message: 'Price must be greater than or equal to 0'
              }
            ]}
          >
            <InputNumber
              className={styles.priceInput}
              placeholder="Enter price"
              prefix="$"
              min={0}
              step={0.01}
              precision={2}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <VaultSelectButton
            mediaType=""
            onSelect={handleSelectVault}
            buttonText="Add"
            renderPreviewItem={handleRenderPreview}
            externalOpenVault={vaultOpen}
            onCloseVault={handleCloseVault}
            openModal={vaultOpen}
            multiple
          />
        </Form>
      </Modal>
      <RecipientSelectionModal
        visible={isRecipientModalVisible}
        onClose={() => setIsRecipientModalVisible(false)}
        onSelect={handleRecipientsSelected}
        conversations={conversations}
      />
    </>
  );
};

export default ScheduleMessageModal;
