import { Modal } from 'antd';
import { MessageOutlined, PictureOutlined } from '@ant-design/icons';

import styles from './ContentTypeModal.module.less';

const ContentTypeModal = ({ visible, onClose, onSelect }) => (
  <Modal
    visible={visible}
    footer={null}
    onCancel={onClose}
    width={600}
    className={styles.contentTypeModal}
  >
    <div className={styles.contentTypeGrid}>
      <div className={styles.contentTypeCard} onClick={() => onSelect('post')}>
        <div className={styles.contentTypeIcon}>
          <PictureOutlined />
        </div>
        <h3>Create Post</h3>
        <p>Share photos, videos, or text with your audience</p>
      </div>

      <div
        className={styles.contentTypeCard}
        onClick={() => onSelect('message')}
      >
        <div className={styles.contentTypeIcon}>
          <MessageOutlined />
        </div>
        <h3>Send Message</h3>
        <p>Schedule a private message to specific recipient</p>
      </div>
    </div>
  </Modal>
);

export default ContentTypeModal;
