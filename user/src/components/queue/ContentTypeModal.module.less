.contentTypeModal {
  :global(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :global(.ant-modal-body) {
    padding: 24px;
  }
}

.contentTypeGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
}

.contentTypeCard {
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .contentTypeIcon {
    font-size: 32px;
    color: #1890ff;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px;
    font-size: 18px;
    font-weight: 500;
  }

  p {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
  }
}
