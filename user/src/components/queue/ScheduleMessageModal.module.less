.timePicker {
  width: 100%;
}

.recipient-select {
  width: 100%;
}

.priceInput {
  width: 100%;

  :global {
    .ant-input-number-handler-wrap {
      border-inline-start: 1px solid #d9d9d9;
    }
  }
}

.attachmentSection {
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.3s;

  &:hover {
    border-color: #1890ff;
  }

  .attachmentButton {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s;

    &:hover {
      background-color: rgba(24, 144, 255, 0.1);
    }
  }

  .attachmentPreview {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
  }

  @media (max-width: 768px) {
    padding: 12px;

    .attachmentButton {
      width: 100%;
      justify-content: center;
    }
  }
}

.previewItem {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .fileIcon {
    font-size: 24px;
    color: #8c8c8c;
  }

  .removeButton {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 50%;
    padding: 4px;
    cursor: pointer;
    width: 14px;
    height: 14px;
    display: flex;
    justify-content: center;
    align-items: center;

    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }
  }
}

.recipientField {
  width: 100%;
}

.selectedRecipients {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.recipientTag {
  background-color: #f0f0f0;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.85);
  display: inline-flex;
  align-items: center;
  text-transform: capitalize;
}
