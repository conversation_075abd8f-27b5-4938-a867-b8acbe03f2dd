import { useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { updateUIValue } from '@redux/ui/actions';

export type ThemeMode = 'light' | 'dark' | 'system';

interface UseThemeReturn {
  theme: ThemeMode;
  actualTheme: 'light' | 'dark';
  setTheme: (theme: ThemeMode) => void;
  toggleTheme: () => void;
  isSystemTheme: boolean;
}

export const useTheme = (): UseThemeReturn => {
  const dispatch = useDispatch();
  const { theme, actualTheme } = useSelector((state: any) => state.ui);

  // Detect system theme preference
  const getSystemTheme = useCallback((): 'light' | 'dark' => {
    if (typeof window === 'undefined') return 'light';
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }, []);

  // Apply theme to document
  const applyTheme = useCallback((themeToApply: 'light' | 'dark') => {
    if (typeof document === 'undefined') return;
    
    const root = document.documentElement;
    const body = document.body;
    
    // Remove existing theme classes
    root.classList.remove('light-theme', 'dark-theme');
    body.classList.remove('light-mode', 'dark-mode');
    
    // Add new theme classes
    root.classList.add(`${themeToApply}-theme`);
    body.classList.add(`${themeToApply}-mode`);
    
    // Update CSS custom properties
    if (themeToApply === 'dark') {
      root.style.setProperty('--theme-mode', 'dark');
    } else {
      root.style.setProperty('--theme-mode', 'light');
    }
  }, []);

  // Set theme function
  const setTheme = useCallback((newTheme: ThemeMode) => {
    let resolvedTheme: 'light' | 'dark';
    
    if (newTheme === 'system') {
      resolvedTheme = getSystemTheme();
    } else {
      resolvedTheme = newTheme;
    }
    
    dispatch(updateUIValue({ 
      theme: newTheme, 
      actualTheme: resolvedTheme 
    }));
    
    applyTheme(resolvedTheme);
  }, [dispatch, getSystemTheme, applyTheme]);

  // Toggle between light and dark (skips system)
  const toggleTheme = useCallback(() => {
    const newTheme = actualTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  }, [actualTheme, setTheme]);

  // Listen for system theme changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      if (theme === 'system') {
        const newSystemTheme = e.matches ? 'dark' : 'light';
        dispatch(updateUIValue({ actualTheme: newSystemTheme }));
        applyTheme(newSystemTheme);
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, [theme, dispatch, applyTheme]);

  // Initialize theme on mount
  useEffect(() => {
    let resolvedTheme: 'light' | 'dark';
    
    if (theme === 'system') {
      resolvedTheme = getSystemTheme();
    } else {
      resolvedTheme = actualTheme || theme;
    }
    
    // Update state if needed
    if (actualTheme !== resolvedTheme) {
      dispatch(updateUIValue({ actualTheme: resolvedTheme }));
    }
    
    applyTheme(resolvedTheme);
  }, [theme, actualTheme, getSystemTheme, applyTheme, dispatch]);

  return {
    theme,
    actualTheme: actualTheme || 'light',
    setTheme,
    toggleTheme,
    isSystemTheme: theme === 'system'
  };
};

export default useTheme;
