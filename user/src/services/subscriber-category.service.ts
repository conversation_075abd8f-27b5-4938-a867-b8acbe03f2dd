import { APIRequest } from './api-request';

interface CreateSubscriberCategoryDto {
  name: string;
  description?: string;
  status?: string;
}

interface UpdateSubscriberCategoryDto {
  name?: string;
  description?: string;
  status?: string;
}

interface FindMyCategoriesOptions {
  includeAvatars?: boolean;
}

export class SubscriberCategoryService extends APIRequest {
  create(createSubscriberCategoryDto: CreateSubscriberCategoryDto) {
    return this.post('/subscriber-category', createSubscriberCategoryDto);
  }

  findAll() {
    return this.get('/subscriber-category');
  }

  findOne(id: string) {
    return this.get(`/subscriber-category/${id}`);
  }

  update(id: string, updateSubscriberCategoryDto: UpdateSubscriberCategoryDto) {
    return this.put(`/subscriber-category/${id}`, updateSubscriberCategoryDto);
  }

  remove(id: string) {
    return this.del(`/subscriber-category/${id}`);
  }

  findByCreatorId(creatorId: string) {
    return this.get(`/subscriber-category/creator/${creatorId}`);
  }

  findMyCategories(options: FindMyCategoriesOptions = {}) {
    const query = new URLSearchParams();
    if (options.includeAvatars) {
      query.append('includeAvatars', 'true');
    }
    const queryString = query.toString();
    return this.get(
      `/subscriber-category/my-categories${
        queryString ? `?${queryString}` : ''
      }`
    );
  }
}

export const subscriberCategoryService = new SubscriberCategoryService();
